<!-- START NAVIGATION -->
<header id="header_wrapper">
<div class="sticky-wrapper" id="navigation-sticky-wrapper">
	<nav style="" id="navigation">
        <div class="container">
            <!-- LOGO GOES HERE -->
     		<div class="logo"><a href="<?php echo LANG_URL?>"><img src="<?php echo IMG_URL?>logo-white.gif" alt="logo"></a></div>
             <!-- MENU -->
             <nav>
                <div id="menu">
                    <ul class="nav navbar-nav" id="mainNav">
                        <li><a href="<?php echo LANG_URL;?>#tag-software">Software</a>
                            <ul>
                                <li><a href="<?php echo LANG_URL;?>software/financial.htm">Financial Management</a></li>
                                <li><a href="<?php echo LANG_URL;?>software/operations.htm">Operations</a></li>
                                <li><a href="<?php echo LANG_URL;?>software/crm.htm">CRM</a></li>
                                <li><a href="<?php echo LANG_URL;?>software/service-manager.htm">Service Manager</a></li>
                            </ul>
                        </li>
                        <li><a href="<?php echo LANG_URL;?>#tag-itsolutions">IT Solutions</a>
                            <ul>
                                <li><a href="<?php echo LANG_URL;?>solutions/system-deployment.htm">System Deployment</a></li>
                                <li><a href="<?php echo LANG_URL;?>solutions/backup-solutions.htm">Backup Solutions</a></li>
                                <li><a href="<?php echo LANG_URL;?>solutions/web-hosting.htm">Web Hosting</a></li>
                            </ul>
                        </li>
                        <li><a href="<?php echo LANG_URL;?>#tag-itservices">IT Services</a>
                            <ul>
                                <li><a href="<?php echo LANG_URL;?>services/it-consulting.htm">IT Consulting</a></li>
                                <li><a href="<?php echo LANG_URL;?>services/it-support.htm">IT Maintenance Support</a></li>
                                <li><a href="<?php echo LANG_URL;?>services/helpdesk.htm">Helpdesk Support</a></li>
                                <li><a href="<?php echo LANG_URL;?>services/troubleshooting.htm">PC Troubleshooting</a></li>
                            </ul>
                        </li>
                        <li><a href="javascript:void();">Resources</a>
                             <ul>
                               	<li><a href="http://www.techspace.co.th/blog">Blog</a></li>
                                <li><a href="http://client.techspace.co.th">Knowledgebase</a></li>
                                <li><a href="<?php echo LANG_URL;?>support/online-forms.htm">Online Forms</a></li>
                                <li><a href="http://client.techspace.co.th">Client Login</a></li>
                            </ul>
                       </li>
                        <li><a href="<?php echo LANG_URL;?>#tag-aboutus">About Us</a>
                             <ul>
                                <li><a href="<?php echo LANG_URL;?>#tag-aboutus">About Us</a></li>
                                <li><a href="<?php echo LANG_URL;?>career.htm">Career Opportunities</a></li>
                                <li><a href="<?php echo LANG_URL;?>#contact">Contact Us</a></li>
                            </ul>
                       </li>
                                               
                        <li><a href="javascript:void();" class="btn btn-success">02 381-9075</a></li>
                        
<?php	//===2. lang option icon
			$filename = $_SERVER['REQUEST_URI'];
			if($lang=='th'){	//thai
				$filename = preg_replace("/\/$lang\//", '', $filename);
				$langUs = '<a href="'.SF_URL. $filename.'" title="Eng"  ><img src="'. IMG_URL . 'icons/us.gif" title="Eng" /></a>';	
				$langTh = '<img src="'. IMG_URL . 'icons/th.gif" title="Thai" />';	
			} else {
				$realFilename = preg_replace('/\.htm/','_th.php',$filename);
			//	$realFilename = preg_replace('/^\//','',$realFilename);
	//echo SF_PATH. $realFilename;
				if(file_exists(SF_PATH . $realFilename)){
					$filename = preg_replace('/\.php$/','.htm',$filename);	//incase this url ends with .php, we convert the link back to .htm forma			
					$langTh = '<a href="'.SF_URL. 'th' . $filename.'" title="Thai" ><img src="'. IMG_URL . 'icons/th.gif" /></a>';
				} else {
					$langTh = '';	
				}
				$langUs = '<img src="'. IMG_URL . 'icons/us.gif" title="Eng" />';	
			}			
?>
			<li class="btn-language"><?php echo $langUs;?> <?php echo $langTh;?></li>
                    </ul>
                    
                </div>
                </nav>
            </div>
        </nav>
    </div>
</header>
<!-- END NAVIGATION -->