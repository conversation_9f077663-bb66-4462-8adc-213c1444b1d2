<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB0_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library


$picCnt = 0;

?>
<!DOCTYPE html>

<html>
<head>
   <meta charset="utf-8">
   <meta http-equiv="X-UA-Compatible" content="IE=edge"> 
   <meta name="description" content="List of sofware applications supported by <?php echo $SITE_NAME;?> IT team">
   <meta name="keywords" content="supported software list, supported product list, IT support sofware list">
   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">

   <title><?php echo $SITE_NAME;?> IT Supported Product List</title>


<?php echo getPgCss(); ?>
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-main_en.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
      <h1>Supported Product List (SPL)</h1>
         <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
            <p>
              This Supported Product List includes all software products supported by <?php echo $SITE_NAME;?> Support Team.

              Changes are made periodically to reflect new products added, but no applications will be phased out mid-year.

<p> <?php echo $SITE_NAME;?> will continue to support products not in the SPL with an upgrade option that may incurred a cost to the end user.


<p>
            For any questions about our Supported Product List, please use the <a href="<?php echo LANG_URL;?>coy/contact-us.html">Online Contact Form</a> to submit your message to us.
</p>
        <h3>1. Productivity</h3>
        <ul class="me-list list-circle-check">
            <li>ACDSee</li>
            <li>Adobe Creative Cloud</li>
            <li>Adobe Creative Suite</li>
            <li>Autodesk</li>
            <li>Evernote</li>
            <li>Font</li>
            <li>Foxit PDF</li>
            <li>Google Keep</li>
            <li>Google Docs</li>
            <li>G Suite</li>
            <li>LibreOffice</li>
            <li>Microsoft Office</li>
            <li>Microsoft OneNote</li>
            <li>Open Oiffce</li>
            <li>Picasa</li>
           </ul>


           <h3>2. Browsers</h3>
           <ul class="me-list list-circle-check">
             <li>Chrome</li>
             <li>Firefox</li>
             <li>Internet Explorer</li>
             <li>Opera</li>
             <li>Safari</li>
           </ul>

           <h3>3. End-point Security</h3>
           <ul class="me-list list-circle-check">
             <li>Avast</li>
             <li>Bitdefender</li>
             <li>ESET</li>
             <li>Kaspersky</li>
             <li>Malwarebyte</li>
             <li>McAfee</li>
             <li>Microsoft Essential</li>
             <li>Norton</li>
             <li>Trend Micro</li>

           </ul>

          <h3>4.  Video Conferencing / Collaboration</h3>
            <ul class="me-list list-circle-check">
             <li>Anydesk</li>
             <li>Cisco WebX</li>
             <li>Line</li>
             <li>Sanook QQ</li>
             <li>Skype</li>
             <li>Teamviewer</li>
             <li>VNC</li>
             <li>Windows Live MSN</li>
             <li>Yahoo Messenger</li>
            </ul>

          <h3>5.  Multimedia</h3>
            <ul class="me-list list-circle-check">
             <li>AMP</li>
             <li>Flash Player</li>
             <li>KM Player</li>
             <li>Nero</li>
             <li>Quicktime</li>
             <li>Real Player</li>
             <li>VLC Media Player</li>
             <li>Wimamp</li>
            </ul>


          <h3>6.  3rd Party Applications</h3>
            <ul class="me-list list-circle-check">
             <li>CD Organizer</li>
             <li>Express</li>
             <li>RDNet</li>
            </ul>

          <h3>7.  Programming Language / Framework / Plugin</h3>
            <ul class="me-list list-circle-check">
             <li>ActiveX</li>
             <li>Adobe Plugin</li>
             <li>Autodesk Plugin</li>
             <li>Firefox Plugin</li>
             <li>Java</li>
            </ul>


           <h3>8. Operating Systems (ITMA customers only)</h3>
           <ul class="me-list list-circle-check">
             <li>Microsoft Windows</li>
           </ul>



          <h3>9.  Utilities</h3>
            <ul class="me-list list-circle-check">
             <li>7 zip</li>
             <li>Winrar</li>
             <li>Winzip</li>
             <li>FastCopy</li>
             <li>TeraCopy</li>
            </ul>



            <!--Header_section-->
               <?php include('menu-coy.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->

      <?php echo getPgFootJs(); ?>

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->

</body>
</html>