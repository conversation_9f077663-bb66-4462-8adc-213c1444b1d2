<?php
// Set default header classes if not provided
$headerClass = isset($headerClass) ? $headerClass : 'header-area';

// Check if we need to include the mega menu
$includeMegaMenu = isset($includeMegaMenu) ? $includeMegaMenu : false;

// Determine the base URL and current URI for dynamic links
$is_localhost = (isset($_SERVER['HTTP_HOST']) && $_SERVER['HTTP_HOST'] === 'localhost');
$base_url = $is_localhost ? 'http://localhost/globalitpartner.com/' : 'https://www.globalitpartner.com/';
$request_uri = $_SERVER['REQUEST_URI'];

// Set logo path
$logoImage = $base_url . 'assets/images/logo/globalitpartner-white-logo.png';

// Function to check if a navigation link is active
function is_active($link_path, $request_uri)
{
    // Check for homepage specifically
    if ($link_path === 'index.php' && (ends_with($request_uri, '/') || ends_with($request_uri, 'index.php'))) {
        return 'active';
    }
    // Check for other pages
    if ($link_path !== 'index.php' && strpos($request_uri, $link_path) !== false) {
        return 'active';
    }
    return '';
}

// Helper function to check if a string ends with a specific substring
function ends_with($haystack, $needle)
{
    $length = strlen($needle);
    if ($length == 0) {
        return true;
    }
    return (substr($haystack, -$length) === $needle);
}
?>
<!-- Header area start here -->
<header class="<?php echo $headerClass; ?>">
    <div class="container header__container">
        <div class="header__main">
            <a href="<?php echo $base_url; ?>" class="logo">
                <img src="<?php echo $logoImage; ?>" alt="logo" style="height: 50px; width: auto;">
            </a>
            <div class="main-menu">
                <nav>
                    <ul>
                        <li>
                            <a href="<?php echo $base_url; ?>" class="<?php echo is_active('index.php', $request_uri); ?>">Home</a>
                        </li>
                        <li>
                            <a href="<?php echo $base_url; ?>service" class="<?php echo is_active('our-services/', $request_uri); ?>" aria-label="View our IT services">Our Service <i class="fa-regular fa-angle-down"></i></a>
                            <ul class="sub-menu">
                                <li><a href="<?php echo $base_url; ?>it-services/end-user-support" class="<?php echo is_active('end-user-support', $request_uri); ?>">End-User
                                        Support</a></li>
                                <li><a href="<?php echo $base_url; ?>it-services/field-service" class="<?php echo is_active('field-service', $request_uri); ?>">Field Service</a>
                                </li>
                                <li><a href="<?php echo $base_url; ?>it-services/network-infrastruction" class="<?php echo is_active('network-infrastruction', $request_uri); ?>">Network &
                                        Infrastructure</a></li>
                                <li><a href="<?php echo $base_url; ?>it-services/wiring-service" class="<?php echo is_active('wiring-service', $request_uri); ?>">Wiring
                                        Service</a></li>
                                <li><a href="<?php echo $base_url; ?>it-services/roll-out-n-deployment" class="<?php echo is_active('roll-out-n-deployment', $request_uri); ?>">Roll Out &
                                        Deployment</a></li>
                                <li><a href="<?php echo $base_url; ?>it-services/procurment-n-license-renewal" class="<?php echo is_active('procurment-n-license-renewal', $request_uri); ?>">Procurement
                                        & License Renewal</a></li>
                            </ul>
                        </li>
                        <li><a href="<?php echo $base_url; ?>why-us" class="<?php echo is_active('why-us', $request_uri); ?>">Why Us</a></li>
                        <li><a href="<?php echo $base_url; ?>contact-us" class="<?php echo is_active('contact-us', $request_uri); ?>">Contact Us</a></li>
                        <!-- dropdown menu -->
                        <!-- <li>
                            <a href="#0">Services</a>
                            <ul class="sub-menu">
                                <li>
                                    <a href="<?php echo $base_url; ?>service-solutions">IT Solutions</a>
                                </li>
                                <li>
                                    <a href="<?php echo $base_url; ?>service">IT Services</a>
                                </li>
                                <li>
                                    <a href="<?php echo $base_url; ?>service-details">Service Details</a>
                                </li>
                            </ul>
                        </li> -->
                    </ul>
                </nav>
            </div>
            <div class="d-none d-xl-flex gap-4">
                <a href="<?php echo $base_url; ?>contact-us" class="btn-one">Contact us now <i
                        class="fa-regular fa-arrow-right-long"></i></a>
                <?php if (isset($showCallInfo) && $showCallInfo) : ?>
                <div class="about-three__left-item d-flex flex-wrap gap-2 align-items-center">
                    <div class="about-call-icon">
                        <span>
                            <svg width="26" height="26" viewBox="0 0 26 26" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_918_1337)">
                                    <path
                                        d="M5.41667 4.33337H9.75L11.9167 9.75004L9.20833 11.375C10.3685 13.7275 12.2725 15.6315 14.625 16.7917L16.25 14.0834L21.6667 16.25V20.5834C21.6667 21.158 21.4384 21.7091 21.0321 22.1154C20.6257 22.5218 20.0746 22.75 19.5 22.75C15.2742 22.4932 11.2885 20.6987 8.2949 17.7051C5.3013 14.7115 3.5068 10.7258 3.25 6.50004C3.25 5.9254 3.47827 5.3743 3.8846 4.96798C4.29093 4.56165 4.84203 4.33337 5.41667 4.33337Z"
                                        stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    </path>
                                    <path
                                        d="M16.25 7.58337C16.8246 7.58337 17.3757 7.81165 17.7821 8.21798C18.1884 8.6243 18.4167 9.1754 18.4167 9.75004"
                                        stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    </path>
                                    <path
                                        d="M16.25 3.25C17.9739 3.25 19.6272 3.93482 20.8462 5.15381C22.0652 6.37279 22.75 8.02609 22.75 9.75"
                                        stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    </path>
                                </g>
                                <defs>
                                    <clipPath>
                                        <rect width="26" height="26" fill="white"></rect>
                                    </clipPath>
                                </defs>
                            </svg>
                        </span>
                    </div>
                    <div class="info">
                        <span class="sm-font fw-600 text-white">Call Us Now</span>
                        <h5 class="text-white">+(65) 8224 2660</h5>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <div class="bars d-block d-lg-none">
                <i id="openButton" class="fa-solid fa-bars"></i>
            </div>
        </div>
    </div>
</header>
<!-- Header area end here -->