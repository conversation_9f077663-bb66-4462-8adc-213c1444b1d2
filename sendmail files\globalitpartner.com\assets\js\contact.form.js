/**
 *
 * -----------------------------------------------------------------------------
 *
Template Name: Global IT Partner - Contact Form
Author: Global IT Partner
Version: 1.1.0
Description: Contact form with honeypot spam protection
 *
 * -----------------------------------------------------------------------------
 *
 **/

(function ($) {
  "use strict";
  // Get the form.
  var form = $("#contact-form");

  // Get the messages div.
  var formMessages = $("#form-messages");

  // Set up an event listener for the contact form.
  $(form).submit(function (e) {
    // Stop the browser from submitting the form.
    e.preventDefault();

    // Basic form validation
    var name = $('input[name="name"]').val();
    var email = $('input[name="email"]').val();
    var message = $('textarea[name="message"]').val();
    var honeypot = $('input[name="website"]').val();

    // Check if honeypot field is filled (spam bot)
    if (honeypot) {
      // Silently exit - don't show error to spammers
      $(formMessages).text("Your message has been sent. Thank You!");
      $(formMessages).removeClass("text-warning fw-bolder mt-2");
      $(formMessages).addClass("text-dark fw-bold mt-3 border py-2 px-3");
      return false;
    }

    // Validate required fields
    if (!name || !email || !message) {
      $(formMessages).text("Please fill in all required fields.");
      $(formMessages).addClass("text-warning fw-bolder mt-2");
      $(formMessages).removeClass("text-dark fw-bold mt-3 border py-2 px-3");
      return false;
    }

    // Validate email format
    var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      $(formMessages).text("Please enter a valid email address.");
      $(formMessages).addClass("text-warning fw-bolder mt-2");
      $(formMessages).removeClass("text-dark fw-bold mt-3 border py-2 px-3");
      return false;
    }

    // Show sending message
    $(formMessages).text("Sending your message...");
    $(formMessages).removeClass("text-warning fw-bolder mt-2");
    $(formMessages).addClass("text-dark fw-bold mt-3 border py-2 px-3");

    // Serialize the form data.
    var formData = $(form).serialize();

    // Submit the form using AJAX.
    $.ajax({
      type: "POST",
      url: $(form).attr("action"),
      data: formData,
    })
      .done(function (response) {
        // Make sure that the formMessages div has the 'success' class.
        $(formMessages).removeClass("text-warning fw-bolder mt-2");
        $(formMessages).addClass("text-dark fw-bold mt-3 border py-2 px-3");

        // Set the message text.
        $(formMessages).text(response);

        // Clear the form with a smooth transition
        $(
          'input[name="name"], input[name="email"], input[name="subject"], textarea[name="message"], input[name="number"]'
        ).each(function () {
          // Store original background color
          var originalBg = $(this).css("background-color");

          // Add a subtle highlight effect
          $(this)
            .css("background-color", "#e6f7ff")
            .delay(300)
            .queue(function () {
              // Clear the value
              $(this).val("");
              // Restore original background with transition
              $(this).css({
                "background-color": originalBg,
                transition: "background-color 0.5s ease",
              });
              $(this).dequeue();
            });
        });

        // Reset any custom form states or classes
        $(form).find(".was-validated").removeClass("was-validated");
      })
      .fail(function (data) {
        // Make sure that the formMessages div has the 'error' class.
        $(formMessages).addClass("text-warning fw-bolder mt-2");
        $(formMessages).removeClass("text-dark fw-bold mt-3 border py-2 px-3");

        // Set the message text.
        if (data.responseText !== "") {
          $(formMessages).text(data.responseText);
        } else {
          $(formMessages).text(
            "Oops! An error occurred and your message could not be sent."
          );
        }

        // Highlight fields with a subtle error indication but preserve values
        $(
          'input[name="name"], input[name="email"], input[name="subject"], textarea[name="message"]'
        ).each(function () {
          if ($(this).attr("required") && !$(this).val()) {
            // Only highlight empty required fields
            $(this).css({
              "border-color": "#ffcccc",
              "background-color": "#fff8f8",
              transition: "all 0.3s ease",
            });
          }
        });

        // Focus on the first empty required field
        $("input[required], textarea[required]").each(function () {
          if (!$(this).val()) {
            $(this).focus();
            return false; // Break the loop after focusing the first empty field
          }
        });
      });
  });
})(jQuery);
