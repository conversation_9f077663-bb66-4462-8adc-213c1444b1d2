<?php
//restricted char: '|'
//used in: checkout.php

include_once LIB0_PATH . 'country.php';

/*
function getStateName($ccode, $stateKey){
	$stateArrayName = 'STATES_' . $ccode;
	global $$stateArrayName;
	$stateArrayName = $$stateArrayName;
	return $stateArrayName[$stateKey];
}
*/

//load states of each allowed country into javascript array
function loadAllCstates_js(){
global $FULL_COUNTRY, $COUNTRY_NO_CSTATES;
	echo '<script language="javascript">';
		//---- 1. load avail country states
		foreach($FULL_COUNTRY as $key => $value){
			$cstateName = 'STATES_'. $key;		
			global $$cstateName;
			$tmpStates = & $$cstateName;
			if(is_array($tmpStates)){
				$i=0;
				$tmpArray = array();
				foreach($tmpStates as $key_x => $value_x){
					$tmpArray[] = $key_x . '~' . $value_x;
	//				echo $cstateName .'[' . $i .']=' . "'" . $key_x . '~' . $value_x . "';";
					$i++;
				}//close foreach
				$tmpArray = implode('|', $tmpArray);
				echo "var $cstateName = '$tmpArray';";
			} else {
				$emptyArray[] = $cstateName;
				echo "var $cstateName ='';";
			}			
		}//close foreach
//echo "var STATES_ = '';";	//represent the '----------' in the select dropdown menu

	//---- 2. load country with NO states
	$noCstateStr = implode('|', $COUNTRY_NO_CSTATES);
	echo "var NO_CSTATE = '$noCstateStr';";

	echo '</script>';
}
//-----loads state and region arrays -------------------
/*
function loadStatesRegion_js(){
global $ALLOWED_COUNTRY, $myDb;

echo '<script language="javascript">';
	//-- load avail country states
	foreach($ALLOWED_COUNTRY as $key => $value){
		$cstateName = 'STATES_'. $key;
		
		global $$cstateName;
		$tmpStates = & $$cstateName;
		if(is_array($tmpStates)){
			$i=0;
			$tmpArray = array();
			foreach($tmpStates as $key_x => $value_x){
				$tmpArray[] = $key_x . '~' . $value_x;
//				echo $cstateName .'[' . $i .']=' . "'" . $key_x . '<~7~>' . $value_x . "';";
				$i++;
				
				//create REGION within the state (eg. REGION_US_WI)
				$qStr = "SELECT * FROM com WHERE com_show='y' AND com_ccode = '$key' AND com_state = '$key_x' AND com_type = 'regional'";
				$res = $myDb->query($qStr);
				while($rec = $myDb->fetchArray($res)){
					$tmpRegionArray[] = $rec[com_id] . '~' . addslashes($rec[com_name]);
				}			
				$regionName = 'REGION_'. $key . '_' . $key_x;
				$tmpRegionArray = implode('|', $tmpRegionArray);
				echo "var $regionName = '$tmpRegionArray';";
				unset($tmpRegionArray);			
				
			}//close foreach
			$tmpArray = implode('|', $tmpArray);
			echo "var $cstateName = '$tmpArray';";
			unset($tmpArray);			
		} else {
			$emptyArray[] = $cstateName;
			echo "var $cstateName ='';";
			
			//create REGION for the country (eg. REGION_SG)
			$qStr = "SELECT * FROM com WHERE com_show='y' AND com_ccode = '$key' AND com_type = 'regional'";
			$res = $myDb->query($qStr);
			while($rec = $myDb->fetchArray($res)){
				$tmpRegionArray[] = $rec[com_id] . '~' . addslashes($rec[com_name]);
			}			
			$regionName = 'REGION_'. $key;
			$tmpRegionArray = implode('|', $tmpRegionArray);
			echo "var $regionName = '$tmpRegionArray';";
			unset($tmpRegionArray);			

		}
		
	}//close foreach
echo "var STATES_ = '';";	//represent the '----------' in the select dropdown menu
echo '</script>';

}
//------------------------
function loadStates($id, $ccode, $selected){

	$cstateName = 'STATES_'. $ccode;

	global $$cstateName;
	$tmpStates = & $$cstateName;

	if(is_array($tmpStates)){
		$i=0;
		$tmpArray = array();
		foreach($tmpStates as $key_x => $value_x){
			$tmpArray[] = $key_x . '~' . $value_x;

			$i++;
		}//close foreach
	} else {

	}
}
*/
//--------------------------------------------------------------
$STATES_US = array(
"AL" => "Alabama",
"AK" => "Alaska",
"AZ" => "Arizona",
"AR" => "Arkansas",
"CA" => "California",
"CO" => "Colorado",
"CT" => "Connecticut",
"DE" => "Delaware",
"DC" => "District of Columbia",
"FL" => "Florida",
"GA" => "Georgia",
"HI" => "Hawaii",
"ID" => "Idaho",
"IL" => "Illinois",
"IN" => "Indiana",
"IA" => "Iowa",
"KS" => "Kansas",
"KY" => "Kentucky ",
"LA" => "Lousiana",
"ME" => "Maine",
"MD" => "Maryland",
"MA" => "Massachusetts",
"MI" => "Michigan",
"MN" => "Minnesota",
"MS" => "Mississippi",
"MO" => "Missouri",
"MT" => "Montana",
"NE" => "Nebraska",
"NV" => "Nevada",
"NH" => "New Hampshire",
"NJ" => "New Jersey",
"NM" => "New Mexico",
"NY" => "New York",
"NC" => "North Carolina",
"ND" => "North Dakota",
"OH" => "Ohio",
"OK" => "Oklahoma",
"OR" => "Oregon",
"PA" => "Pennsylvania",
"RI" => "Rhode Island ",
"SC" => "South Carolina",
"SD" => "South Dakota",
"TN" => "Tennessee",
"TX" => "Texas",
"UT" => "Utah",
"VT" => "Vermont",
"VA" => "Virginia",
"WA" => "Washington",
"WV" => "West Virginia",
"WI" => "Wisconsin",
"WY" => "Wyoming"
);

//-- Canada
$STATES_CA = array(
"AB" => "Alberta",
"BC" => "British Columbia",
"MB" => "Manitoba",
"NB" => "New Brunswick",
"NL" => "Newfoundland and Labrador",
"NT" => "Northwest Territories",
"NS" => "Nova Scotia",
"NU" => "Nunavut",
"ON" => "Ontario",
"PE" => "Prince Edward Island",
"QC" => "Quebec",
"SK" => "Saskatchewan",
"YT" => "Yukon"
);

//-- Australia
$STATES_AU = array(
"AT" => "Australian Capital Territory",
"NW" => "New South Wales",
"NN" => "Northern Territory",
"QD" => "Queensland",
"SA" => "South Australia",
"TA" => "Tasmania",
"VI" => "Victoria",
"WE" => "Western Australia"
);

/* DISABLE because thai user may enter the address in thai
//-- Thailand - need full name as key will appears in the ship to addr during checkout
$STATES_TH = array(
"AC" => "Amnat Charoen",
"AT" => "Ang Thong",
"BK" => "Bangkok",
"BR" => "Buriram",
"CC" => "Chachoengsao",
"CN" => "Chai Nat",
"CY" => "Chaiyaphum",
"CT" => "Chanthaburi",
"CM" => "Chiang Mai",
"CR" => "Chiang Rai",
"CB" => "Chon Buri",
"CP" => "Chumphon",
"KL" => "Kalasin",
"KP" => "Kamphaeng Phet",
"KC" => "Kanchanaburi",
"KK" => "Khon Kaen",
"KB" => "Krabi",
"LP" => "Lampang",
"LU" => "Lamphun",
"LO" => "Loei",
"LB" => "Lop Buri",
"MH" => "Mae Hong Son",
"MS" => "Maha Sarakham",
"MK" => "Mukdahan",
"NN" => "Nakhon Nayok",
"NP" => "Nakhon Pathom",
"NM" => "Nakhon Phanom",
"NC" => "Nakhon Ratchasima",
"NS" => "Nakhon Sawan",
"NT" => "Nakhon Si Thammarat",
"NA" => "Nan",
"NR" => "Narathiwat",
"NB" => "Nong Bua Lamphu",
"NK" => "Nong Khai",
"NI" => "Nonthaburi",
"PA" => "Pathum Thani",
"PT" => "Pattani",
"PG" => "Phangnga",
"PL" => "Phatthalung",
"PY" => "Phayao",
"PE" => "Phetchabun",
"PC" => "Phetchaburi",
"PH" => "Phichit",
"PS" => "Phitsanulok",
"PN" => "Phra Nakhon Si Ayutthaya",
"PR" => "Phrae",
"PK" => "Phuket",
"PB" => "Prachin Buri",
"PU" => "Prachuap Khiri Khan",
"RN" => "Ranong",
"RB" => "Ratchaburi",
"RY" => "Rayong",
"RE" => "Roi Et",
"SA" => "Sa Kaeo",
"SN" => "Sakon Nakhon",
"SM" => "Samut Prakan",
"SS" => "Samut Sakhon",
"SO" => "Samut Songkhram",
"SB" => "Sara Buri",
"ST" => "Satun",
"SG" => "Sing Buri",
"SI" => "Sisaket",
"SK" => "Songkhla",
"SH" => "Sukhothai",
"SP" => "Suphan Buri",
"SR" => "Surat Thani",
"SU" => "Surin",
"TK" => "Tak",
"TG" => "Trang",
"TR" => "Trat",
"UB" => "Ubon Ratchathani",
"UD" => "Udon Thani",
"UT" => "Uthai Thani",
"UD" => "Uttaradit",
"YA" => "Yala",
"YS" => "Yasothon"
);
*/


//--------------
function validState($ccode, $state){
//requires EXACT match to the key in the $COUNTRY & $STATE_ array (don't perform strtoupper here)
	$stateArray = 'STATES_' . $ccode;
	global $$stateArray;
	$stateArray = $$stateArray;	
	if(array_key_exists($state, $stateArray)) {
		return true;
	} else {return false;}
}
?>