# Global IT Partner - Contact Form Email Setup

## Overview
The contact form has been enhanced with professional email functionality including:
- ✅ Honeypot spam protection (already implemented)
- ✅ PHPMailer integration for reliable email delivery
- ✅ HTML email templates with professional styling
- ✅ Auto-reply functionality
- ✅ Comprehensive logging
- ✅ Input validation and sanitization

## Files Added/Modified

### New Files:
- `email-config.php` - Email configuration settings
- `vendor/phpmailer/src/PHPMailer.php` - Simplified PHPMailer implementation
- `vendor/phpmailer/src/Exception.php` - PHPMailer exception handling
- `vendor/phpmailer/src/SMTP.php` - SMTP functionality
- `test-contact-form.php` - Test page for contact form functionality

### Modified Files:
- `mailer.php` - Enhanced with new email functionality

## Configuration Steps

### 1. Update Email Settings
Edit `email-config.php` and update the following settings:

```php
// Basic Email Settings
'to_email' => '<EMAIL>',  // Change this!
'to_name' => 'Global IT Partner',

// SMTP Configuration (for production)
'smtp_host' => 'your-smtp-server.com',           // Change this!
'smtp_username' => '<EMAIL>',       // Change this!
'smtp_password' => 'your-app-password',          // Change this!
```

### 2. SMTP Setup Options

#### Option A: Gmail SMTP (Recommended for testing)
1. Enable 2-factor authentication on your Gmail account
2. Generate an App Password: Google Account → Security → App passwords
3. Use these settings:
   ```php
   'smtp_host' => 'smtp.gmail.com',
   'smtp_port' => 587,
   'smtp_secure' => 'tls',
   'smtp_username' => '<EMAIL>',
   'smtp_password' => 'your-16-character-app-password',
   ```

#### Option B: Business Email Provider
Contact your hosting provider for SMTP settings, typically:
```php
'smtp_host' => 'mail.yourdomain.com',
'smtp_port' => 587,
'smtp_secure' => 'tls',
'smtp_username' => '<EMAIL>',
'smtp_password' => 'your-email-password',
```

#### Option C: Use Native PHP mail() (Basic)
Set `'use_phpmailer' => false` in email-config.php

### 3. Testing

#### Local Testing:
1. Visit: `http://localhost/globalitpartner.com/test-contact-form.php`
2. Submit the test form
3. Check for log files: `contact_form.log` and `spam_attempts.log`

#### Production Testing:
1. Update email settings in `email-config.php`
2. Test the actual contact form: `/contact-us.php`
3. Verify emails are received and auto-replies are sent

### 4. Security Considerations

#### Honeypot Protection:
- Already implemented in the contact form
- Hidden field "website" catches spam bots
- Spam attempts are logged in `spam_attempts.log`

#### Input Validation:
- All inputs are sanitized and validated
- Length limits prevent abuse
- Email validation ensures proper format

#### Logging:
- All form submissions are logged
- Spam attempts are tracked
- Error logging for debugging

## Email Templates

### Main Notification Email:
- Professional HTML design with Global IT Partner branding
- Includes all form data, timestamp, and IP address
- Responsive design for mobile devices

### Auto-Reply Email:
- Thank you message to the user
- Includes company contact information
- Professional branding and styling

## Troubleshooting

### Common Issues:

1. **Emails not sending:**
   - Check SMTP credentials in `email-config.php`
   - Verify firewall/hosting allows SMTP connections
   - Check `contact_form.log` for error messages

2. **Auto-replies not working:**
   - Ensure `'auto_reply_enabled' => true` in config
   - Check if main email sends successfully first
   - Verify auto-reply email settings

3. **Spam protection too aggressive:**
   - Check `spam_attempts.log` for false positives
   - Adjust honeypot field name if needed

4. **Local development:**
   - Emails are simulated (not sent) on localhost
   - Check logs for confirmation of functionality

### Log Files:
- `contact_form.log` - All form submissions and errors
- `spam_attempts.log` - Blocked spam attempts

## Production Deployment

1. Update `email-config.php` with production email settings
2. Test thoroughly using `test-contact-form.php`
3. Remove or restrict access to `test-contact-form.php`
4. Monitor log files for issues
5. Set up log rotation if needed

## Support

For technical support with email configuration:
- Check log files for specific error messages
- Verify SMTP settings with your email provider
- Test with a simple email client first
- Contact your hosting provider for server-specific issues

---

**Note:** Remember to remove or secure the `test-contact-form.php` file in production environments.
