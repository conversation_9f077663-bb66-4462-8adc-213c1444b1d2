<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';

$picCnt = 0;

?>
<!DOCTYPE html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html lang="en" class="no-js"> <!--<![endif]-->
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="ระบบบัญชีการเงินเป็นอีกหนึ่งโมดูลหลักของ MySaaS Software ที่ช่วยคุณบริหารจัดการทางด้านระบบบัญชี,ทรัพย์สิน,ธนาคาร รวมทั้งระบบอื่นๆ ">
    <meta name="keywords" content="โปรแกรมบัญชีการเงิน, โปรแกรมบริหารงานบัญชีการเงิน , โปรแกรมการเงิน , Financial software">
    <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">

    <title>โปรแกรมบัญชีการเงิน</title>


<?php echo getPgCss(); ?>
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-main.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
            <h2>ระบบบัญชีการเงิน</h2>
            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
              <p>
                ระบบบัญชีการเงิน  (MySaaS Financial) เป็นอีกหนึ่งโมดูลหลักของ MySaaS Software ที่ช่วยคุณบริหารจัดการทางด้านระบบบัญชี, ระบบเจ้าหนี้ – ลูกหนี้, ทรัพย์สิน , ธนาคาร รวมทั้งระบบอื่นๆ ที่เกี่ยวข้อง   MySaaS Financial สามารถทำงานเชื่อมโยงข้อมูลถึงกันกับ MySaaS OPS และ FSM ทำให้งานด้านการซื้อ, ขาย, บริการสต๊อกสินค้า และงานซ่อมบำรุงเชื่อมโยงข้อมูลเป็นระบบเดียวกัน ซึ่งทำให้การทำงานในทุกส่วนมีประสิทธิภาพมากยิ่งขึ้น  
              </p>
          
              <p><strong>เราช่วยคุณได้อย่างไร:</strong></p>

              <ul class="me-list list-circle-check">
                <li>บริหารจัดการบัญชีแยกประเภทรวมถึงรายงานวิเคาระห์รูปแบบต่างๆ</li>
                <li>สามารถตั้งค่าเสื่อมและสินทรัพย์ได้ไม่จำกัดจำนวน </li>
                <li>สามารถตั้งค่าการจัดการลูกค้าได้หลายหลาย ไม่ว่าจะเป็นสกุลเงิน, ภาษี  หรือเงื่อนไขการจ่ายชำระ  เป็นต้น</li>
                <li>สามารถตั้งค่าการจัดการเจ้าหนี้ได้หลายหลาย ไม่ว่าจะเป็นสกุลเงิน, ภาษี  หรือเงื่อนไขการจ่ายชำระ  เป็นต้น</li>
                <li><span id="result_box" lang="en">ตั้งค่าปีงบประมาณ  และระยะเวลาในการปิดบัญชี</span></li>
                <li>ระบบการตั้งค่าธนาคารที่ใช้และการทำ Bank reconciliation</li>
                <li>สามารถสร้างงบประมาณการดำเนินงานในแต่ละแผนก แยกเป็นวัน, สัปดาห์, รายเดือน, รายไตรมาส , รายปี หรือกำหนดตามปีบัญชีได้</li>
                <li>สามารถกำหนดงบประมาณตามแผนกหรือตามโปรเจ็คงานได้</li>
              </ul>
               MySaaS Financial Module integrates with MySaaS Operations Module to reduce costs and boost productivity.</p>



            </div>

            <!--Header_section-->
               <?php include('menu-software.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->


<?php echo getPgFootJs(); ?>
</body>
</html>