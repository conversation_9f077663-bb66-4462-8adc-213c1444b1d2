# Enable URL rewriting
RewriteEngine On

# --- PRODUCTION SETTINGS ---
# Redirect non-www to www
RewriteCond %{HTTP_HOST} !^www\. [NC]
RewriteRule ^ https://www.%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Force HTTPS (proxy-aware)
RewriteCond %{HTTP:X-Forwarded-Proto} !https
RewriteCond %{HTTPS} off
RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Set the base directory for the live server
RewriteBase /

# --- LOCALHOST SETTINGS (Commented out) ---
# RewriteBase /globalitpartner.com/

# Redirect /our-services/ to /it-services/ (301 permanent redirect to prevent duplicate content)
# Only redirect if not already redirected (prevent infinite loop)
RewriteCond %{ENV:REDIRECT_STATUS} ^$
RewriteRule ^our-services/(.*)$ /it-services/$1 [R=301,L]

# Rewrite /it-services/ to /our-services/
RewriteRule ^it-services/(.*)$ our-services/$1 [L,NC]

# Hide PHP extension
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Redirect to error.php for non-existent files and directories
# --- PRODUCTION SETTINGS ---
ErrorDocument 404 /error.php

# --- LOCALHOST SETTINGS (Commented out) ---
# ErrorDocument 404 /globalitpartner.com/error.php

# Protect the .htaccess file
<Files .htaccess>
    Order Allow,Deny
    Deny from all
</Files>

# Protect the spam_attempts.log file
<Files spam_attempts.log>
    Order Allow,Deny
    Deny from all
</Files>

# Don't apply rules to existing files or directories
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Redirect all non-existent URLs to error.php
RewriteRule ^(.*)$ error.php [L,QSA]

# Prevent directory listing
Options -Indexes

# Set default character set
AddDefaultCharset UTF-8

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>

# Set browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>
