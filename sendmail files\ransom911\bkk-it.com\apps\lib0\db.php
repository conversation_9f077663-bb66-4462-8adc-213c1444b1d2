<?php

class sysDB{
	var $priDbId;			//specify one primary DB from the below data sources (eg. db1, db2, ..., db5)
	var $priConn;			//pri db handler
	var $sessionDbId;
	var $sessConn;			//session db handler
	var $maxDbSrc = 1;
	var $verbose = false;

	var $dbSrc = array(
			'db1' => array(
				'dbDesc'		=> 'Storefront',
				'dbStatus'		=> 'active',
				'isPrimaryDb'	=> true,				//if this is the primary DB to connect to
				'dbApp'			=> 'mySQL',
				'dbVer'			=> 'v2.8.3',
				'autoConn'		=> true,				//auto-connect to this DB when the page loads
				'enableCron'	=> true,				//activate cron jobs on this dataSrc
				'dbName'		=> 'tspace_sf',
				'host'			=> 'localhost',
				'usrName'		=> 'tspace_elliaw',
				'pswd' 			=> 'cck,3507'				  
			)
	);
	//-----------
	function getDbName($dbId){		return $this->dbSrc[$dbId]['dbName'];}
	//-----------
	function getDbDesc($dbId){		return $this->dbSrc[$dbId]['dbDesc'];}
	//-----------
	function getSessionDbId(){		return $this->sessionDbId;}	//get priDb. return empty strnig if not found
	//-----------
	function fetchAssoc($res){		return $res->fetch_assoc();}
	//-----------
	function fetchArray($res){		return $res->fetch_array();}
	//-----------
	function numRows($res){			return $res->num_rows;}
	//-----------
	function numFields($res){		return $res->field_count;}
	//-----------
	function lastInsertId(){		return $this->sessConn->insert_id;}
	//-----------
	function escapeString($str){	return $this->sessConn->real_escape_string($str);}
	//-----------
	function autoCommit($flag){		return $this->sessConn->autocommit($flag);}
	//-----------
	function commit(){				return $this->sessConn->commit();}
	//-----------
	function rollBack(){			return $this->sessConn->rollback();}
	//-----------
	function error(){				return $this->sessConn->error;}

	//-----------
	function init($dbId=''){
		$this->priDbId	= $this->getPriDbId();	//set primary DBID
		if($this->verbose){
			echo 'In init(): $dbId: '.$dbId.', DBID: '.$_SESSION['BS_DBID'] . ', $this->priDbId: '.$this->priDbId.'<br>';
		}
		if(isset($_SESSION['BS_DBID'])){								//already logged in with a BS_DBID
			$this->sessionDbId = $_SESSION['BS_DBID'];
		} else if($dbId!='' && $this->isActiveDataSrc($dbId)){		//first time login, specifically selecting a dataSrc
			$this->sessionDbId = $dbId;			
		} else {														//none of the above
			$this->sessionDbId = $this->priDbId;
		}
		$this->initConnect();		//connect to primary and selected(session) databases
	}
	//---- connect to both the priDbSrc and sessionDbSrc
	function initConnect(){
		//1. connect to primary DB
		if($this->priDbId==''){echo 'No primary database defined. Fail to connect to database.'; exit;}

		if($this->verbose){
			echo 'In initConnect(), connect to priDB: host: '.$this->dbSrc[$this->priDbId]['host'] . ', dbName: '. $this->dbSrc[$this->priDbId]['dbName']  . ', usrName: '. $this->dbSrc[$this->priDbId]['usrName'] . '<br>';
		}

		$this->priConn = new mysqli(	$this->dbSrc[$this->priDbId]['host'],	$this->dbSrc[$this->priDbId]['usrName'],
										$this->dbSrc[$this->priDbId]['pswd'],	$this->dbSrc[$this->priDbId]['dbName']
									);
		if ($this->priConn->connect_error) {
			die('myDB priConn: Connect Error: '. $this->priConn->connect_errno . ': ' . $this->priConn->connect_error);
		} else {
			$this->priConn->set_charset("utf8");					
		}
		//2. connect to session DB
		if($this->sessionDbId==$this->priDbId){
			$this->sessConn = &$this->priConn;
		} else {
			if($this->verbose){
				echo 'In initConnect(), connect to sessDB: host: '.$this->dbSrc[$this->sessionDbId]['host'] . ', dbName: '.
						$this->dbSrc[$this->sessionDbId]['dbName']  . ', usrName: '. $this->dbSrc[$this->sessionDbId]['usrName'] . '<br>';
			}
			
			$this->sessConn = new mysqli(	$this->dbSrc[$this->sessionDbId]['host'],	$this->dbSrc[$this->sessionDbId]['usrName'],
											$this->dbSrc[$this->sessionDbId]['pswd'],	$this->dbSrc[$this->sessionDbId]['dbName']
										);
			if ($this->sessConn->connect_error) {
				die('myDB sessConn: Connect Error: '. $this->sessConn->connect_errno . ': ' . $this->sessConn->connect_error);
			} else {
				$this->sessConn->set_charset("utf8");
			}
		}
	}
	//---- connect to primary DB
	function connectPriDb($dbId){
		$this->priDbId=$dbId;
		$this->priConn = new mysqli(		$this->dbSrc[$this->priDbId]['host'],	$this->dbSrc[$this->priDbId]['usrName'],
											$this->dbSrc[$this->priDbId]['pswd'],	$this->dbSrc[$this->priDbId]['dbName']
									);
		$this->priConn->set_charset("utf8");
	}
	//---- connect to session DB
	function connectSessionDb($dbId){
		$this->sessionDbId=$dbId;
		$this->sessConn = new mysqli(		$this->dbSrc[$this->sessionDbId]['host'],	$this->dbSrc[$this->sessionDbId]['usrName'],
											$this->dbSrc[$this->sessionDbId]['pswd'],	$this->dbSrc[$this->sessionDbId]['dbName']
									);
		$this->sessConn->set_charset("utf8");
	}
	//-----------
	function getPriDbId(){	//set priDb. return empty string if not found
		foreach($this->dbSrc as $dbId => $dbSrc){
			if($dbSrc['dbStatus']=='active' && $dbSrc['isPrimaryDb']==true){ return $dbId;}
		}
	}
	//-----------
	function getDbSrcOptions($dbId=''){
		$optionStr = '';	$entryFound = false;	$count = 0;
		foreach($this->dbSrc as $tmpId => $dbSrc){
			if($count < $this->maxDbSrc){			
				if($dbSrc['dbStatus']=='active'){ 
					$optionStr .= '<option value="'. $tmpId.'"';					
					if($tmpId==$dbId){
						$optionStr .=  ' Selected class="active bold"';
						$entryFound = true;
					}
					$optionStr .= '>'. $dbSrc['dbDesc'] .'</option>';
					$count++;
				}
			} else {break;}
		}
		if($dbId!='' && !$entryFound){
			$optionStr .= '<option value="-old" class="retired bold">Undefined</option>';
		} else if($dbId=='' && $count==0 && !$entryFound){
			$optionStr .= '<option value="-old" class="retired bold">Empty Data Source</option>';
		}
		return $optionStr;
	}
	//-----------
	function isActiveDataSrc($dbId){//check if active AND valid dbId. we do a loop count to prevent GET variable injection.
		$entryFound = false;
		$count = 0;
		foreach($this->dbSrc as $tmpId => $dbSrc){
			if($count < $this->maxDbSrc){			
				if($dbSrc['dbStatus']=='active'){ 
					if($tmpId==$dbId){$entryFound = true; break;}
					$count++;
				}
			} else {break;}
		}
		return $entryFound;
	}
	//-----------
	function query($stmt, $hdl=''){
//echo "<br>$stmt<br>";
		if($hdl!=''){$res = $hdl->query($stmt);} else {$res = $this->sessConn->query($stmt);}
		return $res;
	}


	//---- Fetch column of ONE row, escape "WHERE" condition. Allow specifying the fields for retreival and where clause
	function fetchRowEsc($tblName, $fieldArr, $whereMArr){
		if( empty( $tblName ) || empty($fieldArr) || empty($whereMArr) ){return false;}
		
		$qWhere = array();
		foreach($whereMArr as $tmp){	//multi-dimension array
			if(is_numeric($tmp['fieldVal']) || preg_match('/^ADDTIME\(NOW\(\)/', $tmp['fieldVal']) ){
				$qWhere[] = $tmp['fieldName'] . ' ' . $tmp['opr'] . ' ' . $tmp['fieldVal'];				
			} else {
				$qWhere[] = $tmp['fieldName'] . ' ' . $tmp['opr'] . ' ' . "'" . $this->escapeString($tmp['fieldVal']) . "'"; //esc string
			}
		}
		$qWhere = implode(' AND ', $qWhere);
		$stmt = "SELECT " . implode(',', $fieldArr) . " FROM $tblName WHERE $qWhere";
		return $this->fetchArray($this->query($stmt));
		
	}


	//---- Insert record into DB. Allow specifying the <fields, value> tuple to insert
	function insertRowEsc($tblName, $fieldArr){
		//		e.g. insertRowEsc("tablename", array(col1=>$val1, col2=>$val2, col3=>"val3", col4=>720, col5=>834.987));
		// 			Equivalent:
		//  		INSERT INTO 'tablename' (col1, col2, col3, col4, col5) values ('foobar', 495, 'val3', 720, 834.987)
		$fieldName	= array_keys($fieldArr);	// Find all the keys (column names) from the array $fieldArr
		$fieldVal	= array_values($fieldArr);	// Find all the values from the array $fieldArr
		$numField	= count($fieldVal);
		for($i=0; $i<$numField; $i++){
			if(!preg_match('/^ADDTIME\(NOW\(\)/', $fieldVal[$i]) ){ //esc string
				$fieldVal[$i] = "'" . $this->escapeString($fieldVal[$i]) . "'";
			}
		}		
		$stmt = 'INSERT INTO ' . $tblName  . '(' . implode(', ', $fieldName) . ') values ' . '(' . implode(', ', $fieldVal) . ')';
//echo "$stmt<br>";
		$result = $this->query($stmt);
		if(!$result){return 'DB INSERT error:' . $this->error();}
	}
	//---- update DB record DB.
	function updateRowEsc($tblName, $fieldArr, $whereMArr){
		if( empty( $tblName ) || empty($fieldArr) || empty($whereMArr) ){return false;}
		//---- prepare fields for update
		$tmpArr = array();
		foreach($fieldArr as $key => $val){	
			if(preg_match('/^ADDTIME\(NOW\(\)/', $val) ){
				$tmpArr[] = "$key=$val";
			} else {
				$tmpArr[] = "$key='" . $this->escapeString($val) . "'";
			}
		}
		
		//---- prepare qWhere
		$qWhere = array();
		foreach($whereMArr as $tmp){	//multi-dimension array
			if(!is_numeric($tmp['fieldVal'])){
				$qWhere[] = $tmp['fieldName'] . ' ' . $tmp['opr'] . ' ' . "'" . $this->escapeString($tmp['fieldVal']) . "'"; //esc string
			} else {$qWhere[] = $tmp['fieldName'] . ' ' . $tmp['opr'] . ' ' . $tmp['fieldVal'];}			
		}
		$qWhere = implode(' AND ', $qWhere);
		$stmt = "UPDATE $tblName SET " . implode(',', $tmpArr) . " WHERE $qWhere";
		if(!$res=$this->query($stmt)){
			return 'DB UPDATE error:' . $this->error();
		}
	}
	
} // end class


?>