<?php
// Email Test Script for BKK-IT Contact Form
// Use this to test email delivery to different recipients

// Simulate form data for testing
$_POST = [
    'name' => 'Test User',
    'company' => 'Test Company Ltd.',
    'email' => '<EMAIL>', // Change this to test different sender domains
    'subject' => 'Email Delivery Test',
    'message' => 'This is a test message to verify email delivery is working correctly.',
    'enquiryType' => 'Technical Support',
    'honeypot' => '' // Should be empty
];

echo "<h2>BKK-IT Email Delivery Test</h2>\n";
echo "<p>Testing email delivery with enhanced authentication...</p>\n";
echo "<hr>\n";

// Include the sendmail script
ob_start();
include 'sendmail.php';
$result = ob_get_clean();

echo "<h3>Test Results:</h3>\n";
echo "<p>Result: <strong>$result</strong></p>\n";

if ($result === 'success') {
    echo "<p style='color: green;'>✓ Email sent successfully!</p>\n";
    echo "<p>Check the following:</p>\n";
    echo "<ul>\n";
    echo "<li>Email should arrive at: <EMAIL></li>\n";
    echo "<li>CC should arrive at: <EMAIL></li>\n";
    echo "<li>Auto-reply should be sent to: <EMAIL></li>\n";
    echo "<li>Check email_log.txt for detailed logs</li>\n";
    echo "</ul>\n";
} else {
    echo "<p style='color: red;'>✗ Email failed to send</p>\n";
    echo "<p>Check email_errors.log for error details</p>\n";
}

echo "<hr>\n";
echo "<h3>Configuration Summary:</h3>\n";
echo "<ul>\n";
echo "<li><strong>From:</strong> BKK IT Contact Form &lt;<EMAIL>&gt;</li>\n";
echo "<li><strong>Reply-To:</strong> Test User &lt;<EMAIL>&gt;</li>\n";
echo "<li><strong>To:</strong> <EMAIL></li>\n";
echo "<li><strong>CC:</strong> <EMAIL></li>\n";
echo "<li><strong>Method:</strong> PHP mail() with enhanced headers</li>\n";
echo "</ul>\n";

echo "<hr>\n";
echo "<h3>Next Steps:</h3>\n";
echo "<ol>\n";
echo "<li>Test with different sender email domains (Gmail, Yahoo, etc.)</li>\n";
echo "<li>Check if emails arrive in inbox vs spam folder</li>\n";
echo "<li>Verify auto-reply is received</li>\n";
echo "<li>Monitor email logs for any issues</li>\n";
echo "<li>Set up DNS records as per EMAIL_SETUP_GUIDE.md</li>\n";
echo "</ol>\n";

// Display recent log entries
echo "<hr>\n";
echo "<h3>Recent Email Log:</h3>\n";
$log_file = __DIR__ . '/email_log.txt';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $log_lines = explode("\n", $log_content);
    $recent_lines = array_slice($log_lines, -10); // Last 10 lines
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>\n";
    echo htmlspecialchars(implode("\n", $recent_lines));
    echo "</pre>\n";
} else {
    echo "<p>No log file found yet.</p>\n";
}

echo "<hr>\n";
echo "<p><small>Test completed at: " . date('Y-m-d H:i:s') . "</small></p>\n";
?>
