<?php
    // Only process POST reqeusts.
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        // Get the form fields and remove whitespace.
        $name       = strip_tags(ucwords(trim($_POST["name"])));
		$name       = str_replace(array("\r","\n"),array(" "," "),$name);
        $email      = filter_var(trim($_POST["email"]), FILTER_SANITIZE_EMAIL); 
        $phone      = trim($_POST["phone"]);
        $company    = $_POST['company'];      
        $message    = trim($_POST["message"]);

        if(isset($_POST['contact_me_by_fax_only'])){
            $honeypot = true;
        } else {
            $honeypot = false;
        }

        $fromName   = 'EquipmentUpgrade [TH] Web Inquiry';
        $from       = '<EMAIL>';
        $incidentId = date('ymd-His');

        //local datetime
        $DATETIME_HOUR_OFFSET = 12; //offset to thailand time
        $servertime = date('U'); //gets PST Unix timestamp
        $local_timestamp = $servertime + $DATETIME_HOUR_OFFSET*3600; //add offset in second (60*60)
        $incidentDate = date('Y-m-d H:i:s', $local_timestamp); //format for MySQL DATETIME field


        // Check that data was sent to the mailer.
        if ( empty($name) OR empty($message) OR !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            // Set a 400 (bad request) response code and exit.
            http_response_code(400);
            echo "Oops! There was a problem with your submission. Please complete the form and try again.";
            exit;
        } else if($honeypot){
            //honeypot to drop spam
            echo "Your message sent. Thank you."; exit;
        }


        $recipient = "<EMAIL>";
        $subject = "New inquiry from $company" .  ' - Case: ' .$incidentId; ;


        // Headers optimized for inbox delivery (like working bkk-software)
        $email_headers = "From: $fromName <$from>\r\n";
        $email_headers.= "Reply-To: $email\r\n";
        $email_headers.= "Return-Path: $from\r\n";
        $email_headers.= "Content-Type: text/plain; charset=UTF-8\r\n";
        $email_headers.= "X-Mailer: PHP/" . phpversion() . "\r\n";

        $email_content="\n\n" .
                    "Source: equipmentupgrade.com [Thailand] Web Form\n\n" .
                    "Case ID: $incidentId\n\n" .
                    "Received Date: $incidentDate\n\n" .
                    "Client Name: $name\n" .
                    "Phone: $phone\n" .
                    "Company: $company\n" .
                    "Email: $email\n\n" .
                    "Client Message:\n$message\n\n";



        // Send the email with enhanced parameters (like working bkk-software)
        $additional_parameters = '-f' . $from; // Set envelope sender
        $email_sent = mail($recipient, $subject, $email_content, $email_headers, $additional_parameters);

        // Add logging to diagnose issues
        $log_message = date('Y-m-d H:i:s') . " - Equipment Upgrade Email attempt:\n";
        $log_message .= "  To: $recipient\n";
        $log_message .= "  From: $from\n";
        $log_message .= "  Customer Email: $email\n";
        $log_message .= "  Subject: $subject\n";
        $log_message .= "  Case ID: $incidentId\n";
        $log_message .= "  Success: " . ($email_sent ? 'YES' : 'NO') . "\n";
        $log_message .= "  Server: " . ($_SERVER['SERVER_NAME'] ?? 'Unknown') . "\n";
        $log_message .= "---\n";
        error_log($log_message, 3, __DIR__ . '/email_log.txt');

        if ($email_sent) {
            // Set a 200 (okay) response code.
            http_response_code(200);
            echo "Thank You! Your message has been sent. One of our representatives will be in contact with you shortly regarding your enquiry.";
        } else {
            // Set a 500 (internal server error) response code.
            http_response_code(500);
            echo "Oops! Something went wrong and we couldn't send your message. Please try again or contact us directly.";
        }

    } else {
        // Not a POST request, set a 403 (forbidden) response code.
        http_response_code(403);
        echo "There was a problem with your submission, please try again.";
    }

?>