<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB0_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library


$picCnt = 0;

?>
<!DOCTYPE html>

<html>
<head>
   <meta charset="utf-8">
   <meta http-equiv="X-UA-Compatible" content="IE=edge"> 
   <meta name="description" content="<?php echo $SITE_NAME;?> committed to building a workforce that respect individual skills and diversity while valuing team contrbution.">
   <meta name="keywords" content="IT jobs, software developer, php developer job, cloud administrator job, IT Career Opportunities, jobs at <?php echo $SITE_NAME;?>,<?php echo $SITE_NAME;?> company profile,<?php echo $SITE_NAME;?> official website">
   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">

   <title>Career Opportunities at <?php echo $SITE_NAME;?></title>


<?php echo getPgCss(); ?>

<style>
  .jobDetails {
     font-size: 85%;;
     color: #666;
  }

  tr.jobEntry0 td {
    background-color:  #fff;
  }

  tr.jobEntry1 td {
    background-color:  #EAF4FB;
  }


</style>

<script type="text/javascript">
function toggleDisplay(obj){
   if(document.getElementById(obj).style.display == 'none') {document.getElementById(obj).style.display = '';
   } else {document.getElementById(obj).style.display = 'none';}
}
</script>


</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-career_en.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
            <h1>Career@<?php echo $SITE_NAME;?></h1>
            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
               <p><?php echo $SITE_NAME?> is a home to a team of young talented individuals who share a common interest - technology. We are committed to building a  workforce that respect individual skills and diversity while valuing team contrbution. We take great pride in providing the best technology and services to our customers. </p>
    
               <p>Our innovative culture means everyone is encouraged to have creative ideas; to express them and to share them. We want to invest in ideas to help change lives and improve working practices.</p>
         
               <p>We are searching for creative and talented individuals to join our team. <?php echo $SITE_NAME?> offers its employees limitless opportunity for development and growth! We strive to create the best working culture to inspire and drive individuals while having fun in the process.</p>
     
               <p><a href="mailto:<?php echo $EMAIL_JOBS?>">Email us</a> to apply for your ideal job!</p>


            </div>

            <!--Header_section-->
               <?php include('menu-coy.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->



      <!-- container -->
      <section class="container" style="padding-top:0">
        <div class="row">
          <h2>Current Job Openings </h2>
          <?php
            $row = 1;
          ?>

          <?php include("career_openings.php"); ?>

        </div>
      </section>
      <!-- container end here -->



      <?php echo getPgFootJs(); ?>

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->

</body>
</html>