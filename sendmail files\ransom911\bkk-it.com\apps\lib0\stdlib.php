<?php

/**************************************************************************************
* Returns the REQUEST_URI from the server environment, or, failing that,
* constructs a new one, using the PHP_SELF constant and other variables.
*
* @return string URI
*/
function getUri() {
	if (env('HTTP_X_REWRITE_URL')) { $uri = env('HTTP_X_REWRITE_URL');
	} elseif (env('REQUEST_URI')) { $uri = env('REQUEST_URI');
	} else {
		if ($uri = env('argv')) {
			if (defined('SERVER_IIS') && SERVER_IIS) {
				if (key($_GET) && strpos(key($_GET), '?') !== false) {unset($_GET[key($_GET)]);}
				$uri = preg_split('/\?/', $uri[0], 2);
				if (isset($uri[1])) {
					foreach (preg_split('/&/', $uri[1]) as $var) {
						@list($key, $val) = explode('=', $var);
						$_GET[$key] = $val;
					}
				}
				$uri = SF_URL . $uri[0];
			} else {$uri = env('PHP_SELF') . '/' . $uri[0];}
		} else {$uri = env('PHP_SELF') . '/' . env('QUERY_STRING');}
	}
	return preg_replace('/\?url=\//', '', $uri);
}
/**************************************************************************************
* Gets an environment variable from available sources, and provides emulation
* for unsupported or inconsisten environment variables (i.e. DOCUMENT_ROOT on
* IIS, or SCRIPT_NAME in CGI mode).  Also exposes some additional custom
* environment information.
*
* @param  string $key Environment variable name.
* @return string Environment variable setting.
*/
function env($key) {
	if ($key == 'HTTPS') {
		if (isset($_SERVER) && !empty($_SERVER)) { return (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on');
		} else { return (strpos(env('SCRIPT_URI'), 'https://') === 0);}
	}
	if ($key == 'SCRIPT_NAME') {if (env('CGI_MODE')) {$key = 'SCRIPT_URL';}}
	$val = null;
	if (isset($_SERVER[$key])) {$val = $_SERVER[$key];
	} elseif (isset($_ENV[$key])) {$val = $_ENV[$key];
	} elseif (getenv($key) !== false) {$val = getenv($key);}

	if ($key == 'REMOTE_ADDR' && $val == env('SERVER_ADDR')) {
		$addr = env('HTTP_PC_REMOTE_ADDR');
		if ($addr != null) {$val = $addr;}
	}
	if ($val !== null) {return $val;}

	switch ($key) {
		case 'SCRIPT_FILENAME':
			if (defined('SERVER_IIS') && SERVER_IIS === true){return str_replace('\\\\', '\\', env('PATH_TRANSLATED') );}
		break;
		case 'DOCUMENT_ROOT':
			$offset = 0;
			if (!strpos(env('SCRIPT_NAME'), '.php')) {$offset = 4;}
			return substr(env('SCRIPT_FILENAME'), 0, strlen(env('SCRIPT_FILENAME')) - (strlen(env('SCRIPT_NAME')) + $offset));
		break;
		case 'PHP_SELF':
			return r(env('DOCUMENT_ROOT'), '', env('SCRIPT_FILENAME'));
		break;
		case 'CGI_MODE':
			return (substr(php_sapi_name(), 0, 3) == 'cgi');
		break;
		case 'HTTP_BASE':
			return preg_replace ('/^([^.])*/i', null, env('HTTP_HOST'));
		break;
	}
	return null;
}

//************************************************************************************************************
function getScriptName(){
	$file = $_SERVER["SCRIPT_NAME"];
	$break = explode('/', $file);
return $break[count($break) - 1]; 	
}
//-----------------
function isValidEmail($email){
 	if(eregi("^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[_a-z0-9-]+(\.[_a-z0-9-]+)*(\.[a-z]{2,4})$", $email) ){return true;} else {return false;}
}


//-----------------
function isValidUrl($url){
	if (preg_match("/^(http(s?):\/\/)((\w+\.){1,})\w{2,}$/i", $url)) {return true;} else {return false;}
}
//-----------------
function getIP(){
	if(isset($_SERVER['HTTP_CLIENT_IP']) && strcasecmp($_SERVER['HTTP_CLIENT_IP'], "unknown")){
		$ip = $_SERVER['HTTP_CLIENT_IP'];
	} else if(isset($_SERVER['HTTP_X_FORWARDED_FOR']) && strcasecmp($_SERVER['HTTP_X_FORWARDED_FOR'], "unknown")){
		list($ip,$junk) = explode(',',$_SERVER['HTTP_X_FORWARDED_FOR']);
	} else if(isset($_SERVER['REMOTE_ADDR']) && strcasecmp($_SERVER['REMOTE_ADDR'], "unknown")){
		$ip = $_SERVER['REMOTE_ADDR'];
	} else if(isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR'] && strcasecmp($_SERVER['REMOTE_ADDR'], "unknown")){
		$ip = $_SERVER['REMOTE_ADDR'];
	} else { $ip = "unknown";}
return $ip;
}
//----------------------------------
function minNumber($x,$y) {if($x > $y){ return $y; } else { return $x;}}
function maxNumber($x,$y) {if($x > $y){ return $x; } else { return $y;}}
//----------------------------------
function flatten_array($array) {
//flattens arrays within array into a single level array
   for($x = 0; $x < sizeof($array); $x++) {
       $element = $array[$x];
       if(is_array($element)) {
           $results = flatten_array($element);
           for($y = 0; $y < sizeof($results); $y++) {
               $flat_array[] = $results[$y];
           }
       } else {
           $flat_array[] = $element;
       }
   }
   return $flat_array;
}
/*----reindex 1 dimensional array to have index starting with 0. function call when we use exclude to clear out some elements:
1. original array: Array ( [0] => [1] => [2] => [3] => [4] => [5] => 1 [6] => 2 )
2. after we clear out the empty elements, Array ( [5] => 1 [6] => 2 ).
3. notice that the first index starts at 5. the below function will rekey the index to start at 0
*/
function arrayRekey($input){
$out = array();
	$i=0;
 	foreach($input as $val){ 
       $out[$i] = $val;
	   $i++;
	}
return $out;
}

//----------------------------------
function matchSysGenEmail($email){ 
//system generated email addr for non-member with NO email addr specified. e.g <EMAIL>
if(ereg("[0-9]{10}\@hotgift\.com",$email)){ return 1; } else { return 0; }
}






//----------------------------------
/*
function shippingCal( $id , $type ,$total ){
global $myDb;
			$sql="select * from shipping where shipping_id = '$id' ";
			$res = $myDb->query($sql);
			$rec = $myDb->fetchArray($res);
			if($type == "std"){
					$shipCost = ($total * ($rec['shipping_standard_percent'] / 100)) + $rec['shipping_standard_cost'];
					return $shipCost;
			}else if($type == "exp"){
					$shipCost =   ($total * ($rec['shipping_express_percent'] / 100)) + $rec['shipping_express_cost'];
					return $shipCost;
			}else {
					$shipCost =   ($total * ($rec['shipping_inter_percent'] / 100)) + $rec['shipping_inter_cost'];
					return $shipCost;
			}
}
*/

//==== DB related searches


//-----------------
function convert2SqlSrchKey($keyword){
	$keyword = preg_replace('/\+/', '_', $keyword);
	$keyword = preg_replace('/\*/', '%', $keyword);
return $keyword;
}




?>