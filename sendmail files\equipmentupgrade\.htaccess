# -FrontPage-
RewriteEngine On
Options +FollowSymLinks
RewriteBase /

# Port 443 on your server is the default used for SSL connections.
#sending the search engine robots to the robots_ssl.txt file where it's instructed not to index the site
RewriteCond %{SERVER_PORT} ^443$ [NC]
RewriteRule ^robots.txt$ robots_ssl.txt [L]


# HERE IS A GOOD PLACE TO ADD THE WWW PREFIX REDIRECTION
# [NC] is a case-insensitive match
RewriteCond %{HTTP_HOST} ^equipmentupgrades\.com$ [NC]
RewriteRule ^(.*) https://www.equipmentupgrades.com/$1 [L,R=301]


# First rewrite to HTTPS (for main domain ONLY)
RewriteCond %{HTTPS} off
RewriteCond %{HTTP_HOST} ^equipmentupgrades\.com$ [NC]
RewriteRule ^ https://equipmentupgrades.com%{REQUEST_URI} [L,R=301]


# Port 443 on your server is the default used for SSL connections.
#sending the search engine robots to the robots_ssl.txt file where it's instructed not to index the site
RewriteCond %{SERVER_PORT} ^443$ [NC]



#add trailing slash
RewriteCond $1 !/$
RewriteCond $1 !\.(jpg|jpeg|gif|png|php|html|htm|pdf)$
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule (.*) https://www.equipmentupgrades.com/$1/ [L,R=301] 

#2. Old url re-direct
rewriteRule ^th/services/it-maintenance\.htm$	/th/services/it-support.htm [R=permanent,L]
rewriteRule ^services/it-maintenance\.htm$		/services/it-support.htm [R=permanent,L]



#1b. convert xxx.html to xxx.htm
#RewriteRule ^(.*)\.html$ /$1.htm [L]
RewriteRule ^index.htm$ /index.php [L]
RewriteRule ^sitemap.htm$ /sitemap.php [L]



#1c. re-route thai version webpages
RewriteRule ^(th)/customer(/)?$ /customer/ [L]
RewriteRule ^(th)/kb(/)?$ /kb/ [L]
RewriteRule ^(th)/(.*)\.htm$ /$2_$1.php?lang=$1 [L]
RewriteRule ^(th)/(.*)/$ /$2/index_$1.php?lang=$1 [L]
RewriteRule ^(th)/$ /index_$1.php?lang=$1 [L]


#2c. Directory specific
RewriteRule ^software/([^/]+)\.htm$	/software/$1.php [L]
RewriteRule ^solutions/([^/]+)\.htm$	/solutions/$1.php [L]
RewriteRule ^services/([^/]+)\.htm$	/services/$1.php [L]
RewriteRule ^support/([^/]+)\.htm$	/support/$1.php [L]
RewriteRule ^support/([^/]+)/([^/]+)\.htm$	/support/$1/$2.php [L]
RewriteRule ^coy/([^/]+)\.htm$	/coy/$1.php [L]


#2d. Errordocs
ErrorDocument 400 /errordocs/index.htm
ErrorDocument 401 /errordocs/index.htm
ErrorDocument 403 /errordocs/index.htm
ErrorDocument 404 /errordocs/index.htm
ErrorDocument 500 /errordocs/index.htm
RewriteRule ^errordocs/index\.htm$ /errordocs/index.php [L]


#------ Prevent Files image/file hotlinking and bandwidth stealing
#RewriteCond %{HTTP_REFERER} !^$
#RewriteCond %{HTTP_REFERER} !^http://(www\.)?equipmentupgrades.com/.*$ [NC]
#RewriteRule \.(gif|jpg|swf|flv|png)$ /feed/ [R=302,L]
