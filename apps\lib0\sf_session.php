<?php

//---- activate user session
function startSfSession(){
/*
if you use PHP's default session handling, the only way to reliably change the session duration in all platforms is to change php.ini. That's because in some platforms, garbage collection is implemented through a script that runs every certain time (a cron script) that reads directly from php.ini, and therefore any attempts at changing it at run time, e.g. via ini_set(), are unreliable and most likely won't work.	
*/
	ini_set('session.gc_maxlifetime',	SF_SESSION_LIFETIME);
	ini_set("session.gc_probability",	1);
	ini_set("session.gc_divisor",		1); 
	ini_set("session.save_path",		SF_SESSION_PATH);
//	ini_set("session.cookie_domain", ".$SITE_DOMAIN_SHORT");
	session_start();
	set_time_limit(0); //never expire script execution duration.
}
//----------------
//---- page session records this user last visited page.
function trackPageSession(){
	//Remember previous page 
//	echo 'PREV_URL:' . $_SESSION['PREV_URL'] . '<br>CUR_URL:' . $_SESSION['CUR_URL'] . '<br>REQUEST_URI:' . $_SERVER['REQUEST_URI'] . '<br>';
	if($_SESSION['CUR_URL']==''){ $_SESSION['CUR_URL'] = SF_URL;}
	if($_SESSION['CUR_URL'] != (preg_replace('/\/$/','',SF_URL)  . $_SERVER['REQUEST_URI']) ){ //if there is a change in the current URL
		if($_SESSION['PREV_URL'] != $_SESSION['CUR_URL'] ){	$_SESSION['PREV_URL'] = $_SESSION['CUR_URL'];}		
		$_SESSION['CUR_URL'] = preg_replace('/\/$/','',SF_URL) . $_SERVER['REQUEST_URI'];
		if($_SESSION['PREV_URL']==''){ $_SESSION['PREV_URL'] = SF_URL;}
	}
//	echo 'PREV_URL:' . $_SESSION['PREV_URL'] . '<br>CUR_URL:' . $_SESSION['CUR_URL'] . '<br>REQUEST_URI:' . $_SERVER['REQUEST_URI'] . '<br>';

}

//----- validate session, prevent multiple, simultaneous logins using a single loginID
function sf_validateSession($forceExit=true){
global $SM_USR_TIMEOUT_MSG, $SM_USR_SIGNIN_MSG, $SM_USR_MULTI_LOGIN_MSG;
$validSession = false;

if(isset($_SESSION['SF_UID']) || isset($_COOKIE['sfActive'])){	//session or cookie still active
//echo "SF_UID: " . $_SESSION['SF_UID'].", session_id(): " .session_id() .  ', getSmSessionId: ' . getSmSessionId($_SESSION['SF_UID']) . '<br>';
	if(isset($_SESSION['SF_UID'])){	//1. active session detected
			setcookie("sfData",		$_SESSION['BS_DBID'],	time()+SF_SESSION_LIFETIME*60,"/");			//database ID
			setcookie("sfActive",	$_SESSION['SF_UID'],	time()+SF_SESSION_LIFETIME*60,"/");		//user ID
			setcookie("sfDead",		$_SESSION['SF_UID'],	time()+SF_SESSION_LIFETIME*60+21600,"/");
			$validSession = true;
	} else if(isset($_COOKIE['sfActive'])) {//2. session has expired, but cookie still valid
			$_SESSION['BS_DBID']	= $_COOKIE['sfData'];
			$_SESSION['SF_UID']		= $_COOKIE['sfActive'];
			setcookie("sfData",		$_SESSION['BS_DBID'],	time()+SF_SESSION_LIFETIME*60,"/");
			setcookie("sfActive",	$_SESSION['SF_UID'],	time()+SF_SESSION_LIFETIME*60,"/");
			setcookie("sfDead",		$_SESSION['SF_UID'],	time()+SF_SESSION_LIFETIME*60+21600,"/");
			$validSession = true;
	}
	if(session_id()!=getSmSessionId($_SESSION['SF_UID'])){	//current sessionId don't match usrSessionId stored in DB => old session
		setcookie("sfData",		'',time()-3600,"/");
		setcookie("sfActive",	'',time()-3600,"/");
		setcookie("sfDead",		'',time()-3600,"/");
		session_destroy();
		setcookie("referralUrl",$_SERVER['REQUEST_URI'],time()+SF_SESSION_LIFETIME*60,"/");
		if($forceExit){
			header("Location:" . SF_URL . "index.php?infoMsg=$SM_USR_MULTI_LOGIN_MSG");exit;			
		}
	}
} else if(isset($_COOKIE['sfDead'])) {//within 6 hours of inactivity, we show the inactivity msg
	//echo "cookie: SF_USER_TIMEOUT: SF_SESSION_LIFETIME"; exit;
	setcookie("referralUrl",$_SERVER['REQUEST_URI'],time()+SF_SESSION_LIFETIME*60,"/");
	if($forceExit){
		header("Location:" . SF_URL . "index.php?referral=$referralUrl&errorMsg=$SM_USR_TIMEOUT_MSG");exit;
	}
} else {	//no session or cookie detected. direct landing
	//$log = "Unregistered user, country:". getCountry() . ", IP:" . getIP();
	//$myLog->writeLog('WARNING', 'SM_USR', $log);
	setcookie("referralUrl",$_SERVER['REQUEST_URI'],time()+SF_SESSION_LIFETIME*60,"/");
	if($forceExit){
		header("Location:" . SF_URL . "index.php?infoMsg=$SM_USR_SIGNIN_MSG");exit;
	}
}
return $validSession;
}


?>