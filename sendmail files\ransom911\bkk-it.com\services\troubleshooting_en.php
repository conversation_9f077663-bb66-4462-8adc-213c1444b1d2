<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB0_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library


$picCnt = 0;

?>
<!DOCTYPE html>

<html>
<head>
   <meta charset="utf-8">
   <meta http-equiv="X-UA-Compatible" content="IE=edge"> 
   <meta name="description" content="<?php echo $SITE_NAME;?> Is your computer randomly crashing? Is your server slow for no reason? You can’t connect to the Internet? Your PC won’t talk to your Mac?">
   <meta name="keywords" content="Business Computer Repairs,General troubleshooting,Software installation,Data recovery">
   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">

   <title>Computer Troubleshooting Services</title>


<?php echo getPgCss(); ?>
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-services_en.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
            <h1>Home & Business Computer Repairs & Troubleshooting</h1>
            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >

               <p><em>Is your computer randomly crashing? Is your server slow for no reason? Are you facing the “blue screen of death”? You can’t connect to the Internet? Your PC won’t talk to your Mac? Are your business back-ups failing? Are you experiencing network drop-outs?
               </em></p>

               <p>
                <strong><?php echo $SITE_NAME;?> </strong>provides  same day onsite computer repairs and troubleshooting services for your home or business.
               </p>


               <h2>
                Our services include:
               </h2>
               <ul class="me-list list-circle-check">
                 <li>General troubleshooting </li>
                 <li>Reformat, reinstallation and upgrade of an operating system on a computer</li>
                 <li>Spyware, malware, virus, ransomware scanning and removal</li>
                 <li>Software installation</li>
                 <li>Data recovery</li>
               </ul>


<p>Contact a <a href="<?php echo LANG_URL?>coy/contact-us.html"><?php echo $SITE_NAME;?> Specialist</a> for more information about how we can help you in getting your computer fixed.</p>



            </div>

            <!--Header_section-->
               <?php include('menu-services.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->

      <?php echo getPgFootJs(); ?>

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->

</body>
</html>