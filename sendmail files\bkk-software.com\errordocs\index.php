<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);


							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';

$picCnt = 0;

?>
<!doctype html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html lang="en" class="no-js"> <!--<![endif]-->
<head>
   <meta charset="utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta name="description" content="<?php echo $SITE_NAME;?> offers affordable business software and IT outsourcing services for SMEs.">
   <meta name="keywords" content="Business IT, IT Solutions, Business Software, IT Outsourcing, IT outsource, IT Support, IT services, Computer Services, Network Solutions, IT Consulting, Maintenance Support">
   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">

   <title><?php echo $SITE_NAME;?> - Page not found</title>

 
<?php echo getPgCss(); ?>
</head>
<body>


   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 

      <!-- container -->
      <section class="container">
         <div class="row">
            <div class="large-8 large-push-2 column text-center">
               <p class="gap" data-gap-bottom="8">
                  <h2>Sorry, the page or URL you have requested is currently unavailable or has been moved.</h2>
               </p>
                  <strong>Please try one of the following options:</strong>
                  <ul>
                  <li>Check that the url you have entered is correct.</li>
                  <li>Click on the browser's REFRESH button to try reconnecting to this page.</li>
                  <li>Return to <a href="<?php echo LANG_URL;?>"><?php echo strtoupper($SITE_NAME);?></a> homepage and use the navigation menu to continue shopping. </li>
                  <li><a href="<?php echo LANG_URL;?>#contact">Contact Us</a> and we will try to help you locate what you are looking for.</li>
                  </ul>
    
                  <p>&nbsp;We apologize for the inconvenience.</p>    
             
            </div>
         </div>
      </section>
      <!-- container end here -->


      <!--Footer_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Footer_section--> 



   </div>
   <!-- main-container end here -->


<?php echo getPgFootJs(); ?>
</body>
</html>