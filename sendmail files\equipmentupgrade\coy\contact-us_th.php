<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';

$picCnt = 0;



?>
<!DOCTYPE html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!-->
<html lang="en" class="no-js">
<!--<![endif]-->

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description"
        content="<?php echo $CO_NAME;?> is a leading Information Technology company specializing in business software applications, IT consulting, infrastructure design, implementation and support.">
    <meta name="keywords" content="contact IT support, contact <?php echo $CO_NAME;?>, contact us">
    <meta name="techspace" content="techspace.co.th">

    <title>Contact Us | <?php echo $CO_NAME;?></title>


    <?php echo getPgCss(); ?>
</head>

<body>

    <!-- main-container -->
    <div id="main-container">

        <!--Header_section-->
        <?php include(INCLUDE_PATH . 'head.php');?>
        <!--Header_section-->



        <!-- slideshow -->
        <?php 
               // include(INCLUDE_PATH . 'slider-main.php');
         ?>
        <!-- slideshow end here -->


        <!-- container -->
        <section class="container">

            <div class="row">
                <div class="large-6 medium-6 medium-portrait-12 column me-animate" data-animate="fadeIn">
                    <h3 class="heading-light">Get in touch with us.</h3>

                    <p>
                        Get answers, help, advice and information in an instant.
                    </p>



                    <ul class="inline-list circle-icon-list">
                        <li><i class="linea-basic_smartphone"></i>Tel: +(66) 2 381-9075</li>
                        <!-- <li><i class="linea-basic_printer"></i>Fax: +(66) 2 713 6800</li> -->
                    </ul>

                    <div class="map-container">
                        <div id="map">Google Map</div>
                        <div class="map-information">
                            <div class="logo-map">
                                <img src="<?php echo IMG_URL;?>logo-pc.png" alt="logo map">
                            </div>
                            <address>
                                1112/110-111 Sukhumvit Road <br>
                                Phra Khanong, Khlong Toei, Bangkok 10110
                            </address>
                        </div>
                    </div>
                </div>

                <div class="large-6 medium-6 medium-portrait-12 column me-animate" data-animate="fadeIn">
                    <form name="myForm" id="myForm" class="contact-form clearfix">
                        <fieldset>
                            <legend>Send Us a Message</legend>

                            <div class="alert-box green success-contact">
                                <i class="fa fa-check-circle"></i>
                                Your Message has been received. Thank you.
                            </div>

                            <div class="form-group large-12 column">
                                <label for="name">Name</label>
                                <input type="text" name="name" id="name" class="form-control"
                                    placeholder="Enter your Name">
                            </div>

                            <div class="form-group large-12 column">
                                <label for="company">Company</label>
                                <input type="text" name="company" id="company" class="form-control"
                                    placeholder="Enter your Company">
                            </div>

                            <div class="form-group large-12 column">
                                <label for="email">Your Email</label>
                                <input type="email" name="email" id="email" class="form-control"
                                    placeholder="Enter your Email">
                            </div>

                            <div class="form-group large-12 column">
                                <label for="subject">Subject</label>
                                <input type="text" name="subject" id="subject" class="form-control"
                                    placeholder="Enter your Subject">
                            </div>

                            <div class="form-group large-12 column">
                                <label for="message">Message</label>
                                <textarea name="message" id="message" class="form-control" rows="5"
                                    placeholder="Enter your Message"></textarea>
                            </div>

                            <div class="form-group large-12 column">
                                <a class="button radius" id="buttonsend" href="#">Submit
                                    <i class="fa fa-angle-double-right"></i>
                                </a>

                                <span class="loading"></span>
                            </div>
                        </fieldset>
                    </form>
                </div>
            </div>
        </section>
        <!-- container end here -->


        <!--Header_section-->
        <?php include(INCLUDE_PATH . 'foot.php');?>
        <!--Header_section-->

    </div>
    <!-- main-container end here -->

    <?php echo getPgFootJs(); ?>

    <!-- javascript plugin for googlemaps and contact forms -->
    <script src="http://maps.google.com/maps/api/js?sensor=true"></script>
    <script src="<?php echo JSLIB_URL;?>jquery.maps.js"></script>
    <script src="<?php echo JSLIB_URL;?>contact-form.js?v=fixed<?php echo date('YmdHis'); ?>"></script>


    <script type="text/javascript">
    jQuery(document).ready(function($) {

        // your script here  
        var map = new GMaps({
            el: '#map',
            lat: 13.7134975,
            lng: 100.5912974,
            zoom: 16,
            scrollwheel: false,
            navigationControl: false,
            mapTypeControl: false,
            scaleControl: true,
            draggable: true,
            zoomControl: true,
            zoomControlOpt: {
                style: 'SMALL',
                position: 'TOP_LEFT'
            },
            panControl: false,
            streetViewControl: false,
            mapTypeControl: false,
            overviewMapControl: false
        });

        map.addMarker({
            lat: 13.712091,
            lng: 100.593099,
            icon: "<?php echo IMG_URL;?>/map-marker.png"
        });

        var styles = [{
            "featureType": "road",
            "elementType": "labels",
            "stylers": [{
                "visibility": "simplified"
            }, {
                "lightness": 20
            }]
        }, {
            "featureType": "administrative.land_parcel",
            "elementType": "all",
            "stylers": [{
                "visibility": "off"
            }]
        }, {
            "featureType": "landscape.man_made",
            "elementType": "all",
            "stylers": [{
                "visibility": "off"
            }]
        }, {
            "featureType": "transit",
            "elementType": "all",
            "stylers": [{
                "visibility": "off"
            }]
        }, {
            "featureType": "road.local",
            "elementType": "labels",
            "stylers": [{
                "visibility": "simplified"
            }]
        }, {
            "featureType": "road.local",
            "elementType": "geometry",
            "stylers": [{
                "visibility": "simplified"
            }]
        }, {
            "featureType": "road.highway",
            "elementType": "labels",
            "stylers": [{
                "visibility": "simplified"
            }]
        }, {
            "featureType": "poi",
            "elementType": "labels",
            "stylers": [{
                "visibility": "off"
            }]
        }, {
            "featureType": "road.arterial",
            "elementType": "labels",
            "stylers": [{
                "visibility": "off"
            }]
        }, {
            "featureType": "water",
            "elementType": "all",
            "stylers": [{
                "hue": "#a1cdfc"
            }, {
                "saturation": 30
            }, {
                "lightness": 49
            }]
        }, {
            "featureType": "road.highway",
            "elementType": "geometry",
            "stylers": [{
                "hue": "#f49935"
            }]
        }, {
            "featureType": "road.arterial",
            "elementType": "geometry",
            "stylers": [{
                "hue": "#fad959"
            }]
        }];

        map.addStyle({
            styledMapName: "Styled Map",
            styles: styles,
            mapTypeId: "map_style"
        });

        map.setStyle("map_style");
    });
    </script>
</body>

</html>