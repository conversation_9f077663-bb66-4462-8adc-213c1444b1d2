<!DOCTYPE html>
<html lang="en">

<?php
$title = 'Page Not Found - Global IT Partner';
// Get the URL that caused the 404 error
$requested_url = isset($_SERVER['REQUEST_URI']) ? htmlspecialchars($_SERVER['REQUEST_URI']) : 'unknown page';
?>
<?php
// Define the base path to handle both direct access and ErrorDocument redirects
// Always use the absolute path to the website root, regardless of where the 404 occurs
$script_filename = $_SERVER['SCRIPT_FILENAME'];
$document_root = $_SERVER['DOCUMENT_ROOT'];
$base_path = str_replace($document_root, '', dirname($script_filename));
if ($base_path == '/') $base_path = '';

// Ensure the base path ends with a slash if not empty
if (!empty($base_path) && substr($base_path, -1) !== '/') {
    $base_path .= '/';
}
include __DIR__ . '/partials/head.php';
?>
<style>
.error__item {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.error__item h2 {
    font-size: 42px;
    margin-bottom: 15px;
    color: #3C72FC;
}

.error__item p,
.error__item span {
    font-size: 18px;
    line-height: 1.6;
    color: #555;
}

.error__item ul {
    margin-top: 15px;
}

.error__item ul li {
    margin-bottom: 8px;
    font-size: 16px;
    color: #555;
}

.error__item .image img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

@media (max-width: 767px) {
    .error__item h2 {
        font-size: 32px;
    }

    .error__item p,
    .error__item span {
        font-size: 16px;
    }
}
</style>

<body>

    <!-- Preloader area start -->
    <?php // include __DIR__ . '/partials/preloader.php' ?>
    <!-- Preloader area end -->

    <!-- Mouse cursor area start here -->
    <?php // include __DIR__ . '/partials/cursor.php' ?>
    <!-- Mouse cursor area end here -->

    <!-- Top header area start here -->
    <?php include __DIR__ . '/partials/header-top.php' ?>
    <!-- Top header area end here -->

    <?php
    // Set header parameters
    $headerClass = "header-area";
    $logoImage = $base_path . "assets/images/logo/globalitpartner-white-logo.png";
    $includeMegaMenu = true;

    // Include header
    include __DIR__ . "/partials/header.php";
    ?>

    <!-- Sidebar area start here -->
    <?php include __DIR__ . '/partials/sidebar.php' ?>
    <!-- Sidebar area end here -->

    <!-- Fullscreen search area start here -->
    <?php include __DIR__ . '/partials/search.php' ?>
    <!-- Fullscreen search area end here -->

    <main>
        <!-- Error area start here -->
        <section class="error-area pt-120 pb-120">
            <div class="container">
                <div class="error__item">

                    <h2>Page Not Found</h2>
                    <p class="mb-3">Sorry, the page <strong>"<?php echo $requested_url; ?>"</strong> could not be found.
                    </p>
                    <span>The page you are looking for might have been removed, had its name changed, or is temporarily
                        unavailable.</span>

                    <div class="mt-4">
                        <p>You might want to try one of these options:</p>
                        <ul class="text-left mb-4" style="display: inline-block; text-align: left;">
                            <li>• Check the URL for typing errors</li>
                            <li>• Go back to the <a href="<?php echo $base_path; ?>/index.php"
                                    style="color: #3C72FC;">homepage</a></li>
                            <li>• Visit our <a href="<?php echo $base_path; ?>/our-services/end-user-support.php"
                                    style="color: #3C72FC;">services
                                    page</a></li>
                            <li>• Contact our <a href="<?php echo $base_path; ?>/contact-us.php"
                                    style="color: #3C72FC;">support team</a></li>
                        </ul>
                    </div>

                    <div class="btn-two mt-30">
                        <span class="btn-circle">
                        </span>
                        <a href="<?php echo $base_path; ?>/index.php" class="btn-one">Go Back Home</a>
                    </div>

                </div>
            </div>
        </section>
        <!-- Error area end here -->
    </main>

    <!-- Footer area start here -->
    <?php include __DIR__ . '/partials/footer.php' ?>
    <!-- Footer area end here -->

    <!-- Back to top area start here -->
    <?php include __DIR__ . '/partials/scroll-up.php' ?>
    <!-- Back to top area end here -->

    <!-- all js files -->
    <?php include __DIR__ . '/partials/script.php' ?>
</body>

</html>