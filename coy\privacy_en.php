<?php

/******** START BOOTSTRAP *********/

//--- 1.  bootstrap

define('DS', DIRECTORY_SEPARATOR);

include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';

include_once LIB0_PATH . 'bootstrap.php';



//--- 2. start session

//startBeSession();



//--- 3. extract parsed variables

extract($_GET);	extract($_POST);



/*

//--- 4. instantiate DB

$myDb		= new sysDB;	$myDb->init();

//====>>>>> DB Connection Established after this line ====>>>>>



//--- 5. SM init

include_once LIB1_PATH . 'sm_init.php';

*/



//==== include language type

$langUrl = SF_URL;

if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}

define('LANG_URL',		$langUrl);







							

//--- 6. special library





$picCnt = 0;



?>

<!DOCTYPE html>



<html>

<head>

   <meta charset="utf-8">

   <meta http-equiv="X-UA-Compatible" content="IE=edge"> 

   <meta name="description" content="<?php echo $SITE_NAME;?> IT outsource service Company - Privacy Policy">

   <meta name="keywords" content="Privacy Policy, <?php echo $SITE_NAME;?> privary policy">

   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">



   <title>Privacy Policy - <?php echo $SITE_NAME;?> IT Outsource</title>

<link rel="canonical" href="https://www.bkk-it.com/en/coy/online-forms.html" />



<?php echo getPgCss(); ?>

</head>

<body>



   <!-- main-container -->

   <div id="main-container">



      <!--Header_section-->

         <?php include(INCLUDE_PATH . 'head.php');?>

      <!--Header_section--> 







      <!-- slideshow -->

         <?php include(INCLUDE_PATH . 'slider-main_en.php');?>

      <!-- slideshow end here -->



 

       <!-- container -->

      <section class="container">

         <div class="row">

            <h1>About <?php echo $SITE_NAME;?></h1>

            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >

               <p>



               At <?php echo $SITE_NAME;?>

               , we respect and 

               understand your concern about privacy and the use of personal information. 

               We are committed to protecting your personal and account information 

               both online and offline.</p>

               <p style="margin-bottom: 3px">&nbsp;</p>

               <p style="margin-bottom: 3px"><STRONG>Use of Personal Information</STRONG></p>

               <p>

                 <?php echo $SITE_NAME;?>

                 does not collect any personal 

                 information such as name, address or e-mail address on our Web sites without 

                 your knowledge or consent. When you register for a<span style="margin-bottom: 5px">

                 <?php echo $SITE_NAME;?>

                 </span>  account, we ask for your email  address,  password and  other information such as your  geographic location, age and gender. We use this information we collect to enhance your online experience and to  communicate with you.</p>

               <p>&nbsp;  </p>

               <p>

                 <?php echo $SITE_NAME;?> 

                 collects information including your IP addresses, referring URL, browser type, profile information, and information regarding your use of our website and services. Such information is garthered to help  us manage our website, track usage or deny service in accordance with our terms of use.</p>

               <p>&nbsp;</p>

               <p><span>

                 <?php echo $SITE_NAME;?>  

                 also uses cookies to store user's preferences and session  information.</span></p>

               <p>&nbsp; </p>

               <p>We protect the interest our customer and pledge 

                 to preserve your privacy by:</p>

                 <div id = "test">

               <UL>

               <LI>Protecting any information you share with us and will not sell 

               or trade such information with other parties. 

               <LI>Provide   privacy setting options on information you choose to share or disclose. 

               <LI>Keeping visitors posted about your privacy rights. </LI>

               </UL></div>

               <p></p>

               <p>&nbsp;</p>

                     <p><?php echo $SITE_NAME;?> is happy to provide further details about our privacy policies. 

                       If you have any questions or comments regarding this Privacy Policy, please 

                       contact us at: <a href="mailto:privacy@<?php echo $CO_DOMAIN;?>">privacy@<?php $CO_DOMAIN;?></a>.      </p>



               </p>

            </div>



            <!--Header_section-->

               <?php include('menu-coy.php');?>

            <!--Header_section--> 



         </div>   

      </section>

      <!-- container end here -->



      <?php echo getPgFootJs(); ?>



      <!--Header_section-->

         <?php include(INCLUDE_PATH . 'foot.php');?>

      <!--Header_section--> 



   </div>

   <!-- main-container end here -->



</body>

</html>