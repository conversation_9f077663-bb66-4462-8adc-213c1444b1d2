/* Custom CSS to fix alignment issues */

/* OUR CULTURES section card layout */
.culture-row .service__item {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 280px;
}

.culture-row .service__item p {
  flex-grow: 1;
}

.culture-row .service__item .icon {
  margin-bottom: 20px;
}

/* Center the second row of cards */
@media (min-width: 1200px) {
  .culture-row:last-child {
    justify-content: center;
  }

  .culture-row:last-child .col-xl-4 {
    margin: 0 15px;
  }
}

/* Add more space above the culture section */
.culture-section {
  margin-top: 60px;
}

/* Fix for the service menu alignment */
.blog-single__right-item .item .category li,
.service-single__right-item .item .category li {
  display: block !important;
  text-align: left !important;
  padding: 15px 20px !important;
}

.blog-single__right-item .item .category li a,
.service-single__right-item .item .category li a {
  display: block;
  width: 100%;
  color: var(--heading-color);
  transition: var(--transition);
}

.blog-single__right-item .item .category li:hover,
.service-single__right-item .item .category li:hover {
  background-color: var(--primary-color) !important;
}

.blog-single__right-item .item .category li:hover a,
.service-single__right-item .item .category li:hover a {
  color: var(--white) !important;
}

/* Add hover animation for links */
.blog-single__right-item .item .category li a,
.service-single__right-item .item .category li a {
  position: relative;
  transition: color 0.3s ease;
}

.blog-single__right-item .item .category li a:hover,
.service-single__right-item .item .category li a:hover {
  color: var(--white) !important;
}

/* Fix for the "Read More" button in service slider */
.service-two__slider .swiper-slide-active .read-more-btn {
  color: var(--paragraph) !important;
}

.service-two__slider .swiper-slide-active .read-more-btn i {
  color: inherit !important;
  margin-left: 5px !important;
}

.service-two__slider .swiper-slide .read-more-btn:hover,
.service-two__slider .swiper-slide-active .read-more-btn:hover {
  color: var(--primary-color) !important;
}

.service-two__slider .swiper-slide .read-more-btn:hover i,
.service-two__slider .swiper-slide-active .read-more-btn:hover i {
  color: var(--primary-color) !important;
  margin-left: 10px !important;
}

/* Fix for standalone service cards "Read More" buttons */
.service-two__item .read-more-btn {
  color: var(--paragraph) !important;
}

.service-two__item .read-more-btn i {
  color: inherit !important;
  margin-left: 5px !important;
}

.service-two__item .read-more-btn:hover {
  color: var(--primary-color) !important;
}

.service-two__item .read-more-btn:hover i {
  color: var(--primary-color) !important;
  margin-left: 10px !important;
}
