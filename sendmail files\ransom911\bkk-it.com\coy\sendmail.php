<?php
// --- BEGIN: Enhanced Contact Form Mailer with Proper Authentication ---

// Get and sanitize form fields
$name = htmlspecialchars(strip_tags(trim($_POST["name"] ?? '')));
$name = str_replace(array("\r", "\n"), array(" ", " "), $name);
$company = htmlspecialchars(strip_tags(trim($_POST["company"] ?? '')));
$email = filter_var(trim($_POST["email"] ?? ''), FILTER_SANITIZE_EMAIL);
$subject = htmlspecialchars(trim($_POST["subject"] ?? ''));
$message = htmlspecialchars(trim($_POST["message"] ?? ''));
$enquiryType = htmlspecialchars(strip_tags(trim($_POST["enquiryType"] ?? '')));
$honeypot = $_POST['honeypot'] ?? '';

// Validate required fields
$required_fields = ['name', 'email', 'subject', 'message'];
$errors = [];
foreach ($required_fields as $field) {
    if (empty($_POST[$field])) {
        $errors[] = ucfirst($field) . ' is required';
    }
}
if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $errors[] = 'Please enter a valid email address';
}
if (!empty($honeypot)) {
    // Honeypot spam trap
    exit;
}
if (!empty($errors)) {
    echo 'email_error';
    exit;
}

// Enhanced Email config for bkk-it.com with proper authentication
$email_config = [
    'to_email' => '<EMAIL>',
    // 'cc_email' => '<EMAIL>', 
    'to_name' => 'BKK IT Support',
    'from_email' => '<EMAIL>', // Use domain email for authentication
    'from_name' => 'BKK IT Contact Form',
    'subject_prefix' => 'BKK IT Contact: ',
    'website_name' => 'www.bkk-it.com',
    'auto_reply_enabled' => true,
    'auto_reply_from_email' => '<EMAIL>', // Use consistent domain
    'auto_reply_from_name' => 'BKK IT Support',
    'auto_reply_subject' => 'Thank you for contacting BKK IT',
    'use_phpmailer' => true, // Set to true to use SMTP instead of mail()
    'organization' => 'Bangkok IT Solutions',
    'return_path' => '<EMAIL>', // For bounce handling
    // SMTP Configuration (when use_phpmailer is true)
    'smtp_host' => 'localhost', // Change to your SMTP server
    'smtp_port' => 587, // 587 for TLS, 465 for SSL, 25 for plain
    'smtp_secure' => 'tls', // 'tls', 'ssl', or false
    'smtp_auth' => true,
    'smtp_username' => '<EMAIL>',
    'smtp_password' => 'Thailand,Wat', // Add your SMTP password
    'smtp_debug' => false // Set to true for debugging
];

// Compose email to staff
$to = $email_config['to_email'];
$email_subject = $email_config['subject_prefix'] . $subject;
$email_body = "New contact form submission from " . $email_config['website_name'] . ":\n\n";
$email_body .= "Name: $name\n";
$email_body .= "Company: $company\n";
$email_body .= "Email: $email\n";
$email_body .= "Enquiry Type: $enquiryType\n";
$email_body .= "Subject: $subject\n\n";
$email_body .= "Message:\n$message\n\n";
$email_body .= "Website: " . $email_config['website_name'] . "\n";
$email_body .= "Submitted on: " . date('Y-m-d H:i:s') . "\n";
$email_body .= "IP Address: " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . "\n";
$email_body .= "User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "\n";

// Enhanced headers for better email authentication and deliverability
$headers = "From: " . $email_config['from_name'] . " <" . $email_config['from_email'] . ">\r\n";
$headers .= "Reply-To: $name <$email>\r\n"; // Customer can reply directly
$headers .= "Return-Path: " . $email_config['return_path'] . "\r\n"; // Bounce handling
if (!empty($email_config['cc_email'])) {
    $headers .= "Cc: " . $email_config['cc_email'] . "\r\n";
}
$headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
$headers .= "Content-Transfer-Encoding: 8bit\r\n";
$headers .= "MIME-Version: 1.0\r\n";
$headers .= "X-Mailer: BKK-IT Contact Form v2.0\r\n";
$headers .= "X-Originating-IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . "\r\n";
$headers .= "X-Priority: 3\r\n"; // Normal priority
$headers .= "Organization: " . $email_config['organization'] . "\r\n";
$headers .= "X-Auto-Response-Suppress: OOF, DR, RN, NRN\r\n"; // Suppress auto-responses
// Add message ID for better tracking
$message_id = '<' . time() . '.' . uniqid() . '@bkk-it.com>';
$headers .= "Message-ID: $message_id\r\n";
// Additional authentication-friendly headers
$headers .= "Date: " . date('r') . "\r\n"; // RFC 2822 date format
$headers .= "X-Sender: " . $email_config['from_email'] . "\r\n";
$headers .= "X-Source-IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . "\r\n";

// Enhanced error logging and email sending
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/email_errors.log');

// Function to send email via SMTP (basic implementation)
function sendEmailSMTP($to, $subject, $body, $headers, $config) {
    if (!$config['smtp_auth']) {
        return false; // SMTP not properly configured
    }

    // This is a basic SMTP implementation
    // For production, consider using PHPMailer library
    $smtp_headers = $headers;
    $smtp_headers .= "Date: " . date('r') . "\r\n";

    // For now, fall back to mail() with enhanced parameters
    // TODO: Implement full SMTP client or integrate PHPMailer
    $additional_parameters = '-f' . $config['return_path'];
    return mail($to, $subject, $body, $smtp_headers, $additional_parameters);
}

// Send the email using configured method
if ($email_config['use_phpmailer']) {
    $email_sent = sendEmailSMTP($to, $email_subject, $email_body, $headers, $email_config);
} else {
    // Set additional mail parameters for better deliverability
    $additional_parameters = '-f' . $email_config['return_path']; // Set envelope sender
    $email_sent = mail($to, $email_subject, $email_body, $headers, $additional_parameters);
}

// Enhanced logging with more details
$log_message = date('Y-m-d H:i:s') . " - Email attempt:\n";
$log_message .= "  To: $to\n";
$log_message .= "  From: " . $email_config['from_email'] . "\n";
$log_message .= "  Customer Email: $email\n";
$log_message .= "  Subject: $email_subject\n";
$log_message .= "  Message ID: $message_id\n";
$log_message .= "  Success: " . ($email_sent ? 'YES' : 'NO') . "\n";
$log_message .= "  Server: " . ($_SERVER['SERVER_NAME'] ?? 'Unknown') . "\n";
$log_message .= "  User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "\n";
$log_message .= "---\n";
error_log($log_message, 3, __DIR__ . '/email_log.txt');

// No auto-reply - removed to match TS configuration

if ($email_sent) {
    echo 'success';
} else {
    echo 'email_error';
}
// --- END: Robust Contact Form Mailer ---
?>