<?php
/**
 * Simplified PHPMailer class for Global IT Partner contact form
 * This is a basic implementation for SMTP email sending
 */

namespace PHPMailer\PHPMailer;

class PHPMailer
{
    public $Host = '';
    public $SMTPAuth = false;
    public $Username = '';
    public $Password = '';
    public $SMTPSecure = '';
    public $Port = 587;
    public $CharSet = 'UTF-8';
    public $Subject = '';
    public $Body = '';
    public $AltBody = '';
    public $ErrorInfo = '';
    
    private $to = [];
    private $from = [];
    private $replyTo = [];
    private $isHTML = false;
    private $smtp = null;
    
    public function __construct($exceptions = null)
    {
        // Constructor
    }
    
    public function isSMTP()
    {
        // Set to use SMTP
        return true;
    }
    
    public function setFrom($address, $name = '')
    {
        $this->from = ['address' => $address, 'name' => $name];
    }
    
    public function addAddress($address, $name = '')
    {
        $this->to[] = ['address' => $address, 'name' => $name];
    }
    
    public function addReplyTo($address, $name = '')
    {
        $this->replyTo = ['address' => $address, 'name' => $name];
    }
    
    public function isHTML($isHTML = true)
    {
        $this->isHTML = $isHTML;
    }
    
    public function send()
    {
        try {
            // For local development, always return true
            if ($this->isLocalEnvironment()) {
                return true;
            }
            
            // Build email headers
            $headers = [];
            $headers[] = 'MIME-Version: 1.0';
            
            if ($this->isHTML) {
                $headers[] = 'Content-Type: text/html; charset=' . $this->CharSet;
            } else {
                $headers[] = 'Content-Type: text/plain; charset=' . $this->CharSet;
            }
            
            $fromName = !empty($this->from['name']) ? $this->from['name'] : $this->from['address'];
            $headers[] = 'From: ' . $fromName . ' <' . $this->from['address'] . '>';
            
            if (!empty($this->replyTo)) {
                $replyName = !empty($this->replyTo['name']) ? $this->replyTo['name'] : $this->replyTo['address'];
                $headers[] = 'Reply-To: ' . $replyName . ' <' . $this->replyTo['address'] . '>';
            }
            
            $headers[] = 'X-Mailer: PHP/' . phpversion();
            $headers[] = 'X-Priority: 3';
            
            // Send to each recipient
            foreach ($this->to as $recipient) {
                $to = $recipient['address'];
                $body = $this->isHTML ? $this->Body : $this->AltBody;
                
                if (empty($body)) {
                    $body = $this->Body;
                }
                
                $result = mail($to, $this->Subject, $body, implode("\r\n", $headers));
                
                if (!$result) {
                    $this->ErrorInfo = 'Failed to send email to ' . $to;
                    return false;
                }
            }
            
            return true;
            
        } catch (Exception $e) {
            $this->ErrorInfo = $e->getMessage();
            return false;
        }
    }
    
    private function isLocalEnvironment()
    {
        $serverName = $_SERVER['SERVER_NAME'] ?? '';
        $serverAddr = $_SERVER['SERVER_ADDR'] ?? '';
        
        return in_array($serverName, ['localhost', '127.0.0.1']) || 
               strpos($serverName, 'xampp') !== false ||
               $serverAddr === '127.0.0.1';
    }
}
