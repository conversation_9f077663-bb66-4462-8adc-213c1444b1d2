<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);


							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';


//--- 8. local var -----
$picCnt = 0;


?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Field Service Manager, Work Order Management, Preventive Maintenance Planning & Scheduling | <?php echo $SITE_NAME;?></title>
<meta name="description" content="MySaaS Field Service Manager helps track work orders and service cost including labor, material and service charges.">
<meta name="keywords" content="field service management,cmms,work order management, PM Plan, work scheduling,field service management,work order management,planning software">
<link href="<?php echo CSS_URL?>panel.css" type="text/css" rel="stylesheet">
<link href="<?php echo APPS_URL?>fancybox/jquery.fancybox-1.3.4.css" type="text/css" rel="stylesheet">
<?php
echo getPgCss();
?>
<script language="JavaScript" type="text/javascript" src="<?php echo APPS_URL?>fancybox/jquery.fancybox-1.3.4.pack.js"></script>
<script type="text/javascript">
	//--- GLOBAL Variables -----	
	var NUMCAT = <?php echo count($SF_CAT);?>;
	activateCoinSlider();
</script>
</head>
<body>

<div style="display: block;" id="menuPanel" align="center"> 
	<?php include(INCLUDE_PATH . 'head.php');?>
</div>


<div class="wrapper">
<?php include(INCLUDE_PATH .'coin-slider.php');?>
<div class="content">

<div class="panel">



<div class="block">
<div class="block_entry">
<?
include("left-menu-software.php");
?>
<div class="mainPanel">
<div class="mainPanel_entry">

<h1 style="margin-bottom:15px">MySaaS Field Service Manager (FSM) / CMMS</h1>
<p>MySaaS Field Service Manager is a simple yet powerful application that is designed to help  improving work efficiency and customer satisfaction. It provides a 360 view of all your field service activities, enabling you to keep track of your  jobs, customers, invoices and schedules online. </p>
<p>&nbsp;</p>
<p>With MySaaS Field Service Manager, you can plan, schedule, assign and track work orders online. Additionally, the application also allows you to track job cost and labor utilization. Managers, engineers and technicians can track and update work  status through their laptops or mobile applications, providing instant  work status update to the customer and team.
</p>
<p>&nbsp;</p>
<p>MySaaS FSM runs on the cloud, providing instant, real-time access to work order information anywhere, anytime. With MySaaS FSM, you can:</p>
<ul>
  <li>Create, submit, approve, track, and manage work orders in   real-time</li>
  <li>Receive work notification and alerts via E-mail or SMS.</li>
  <li>PM Planning and auto-scheduling of work order on due dates</li>
  <li>Maintain equipment data, service history and costs</li>
  <li>Configurable workflows to match your business needs.</li>
  <li>Improve  operational  efficiencies and customer satisfaction</li>
</ul>
<h2>Features</h2>
  <ul>
  <li>Increase productivity, improve customer loyalty, and save more time  each day</li>
  <li>Gain greater control of  jobs and service personnel.</li>
  <li>Track work orders and service cost including labor, material and service charges.</li>
  <li>Create and dispatch work orders across multiple business units, region or locations.</li>
  <li>Simple, easy to use interface for technician/repair men to enter work status.</li>
  <li> Ability to configure  workflow behaviors</li>
  <li> User-definable email notification and screen alert capabilities</li>
  <li> User-definable service code with labor, material and service charges</li>
  <li>Generate billings and service reports</li>
  <li> Track job time, response time, machine downtime and other metrics </li>
  <li>Mobile work order integration</li>
  </ul>
  <p>&nbsp;</p>
  <p>MySaaS FSM is  fully integrated with the <a href="ops.htm">MySaaS Operation Management System (OPS)</a>, providing    real-time access to customer billings and stocks information.</p>
  <p>&nbsp;</p>
  <h2>Screenshots &nbsp;&nbsp;<span style="font-size:11px; color:#666; font-style:italic">(click on the image for enlarge view)</span></h2> 
<table width="100%">
  <tr valign="top">
  <td valign="top" style="height:200px;"><b>Service Dashboard</b><br />
  <a id="picture<?php echo $picCnt?>" href="<?php echo IMG_URL?>software/sw-dashbd.jpg"><img src="<?php echo IMG_URL?>software/sw-dashbd.jpg" onclick="activateFancyBox(<?php echo $picCnt++?>)" width="200" height="130"></a></td>
  <td><b>PM Plan, Scheduling</b><br />
  <a id="picture<?php echo $picCnt?>" href="<?php echo IMG_URL?>software/sw-scheduling.jpg"><img src="<?php echo IMG_URL?>software/sw-scheduling-s.jpg" onclick="activateFancyBox(<?php echo $picCnt++?>)"></a></td>
  <td><b>Service Requests / Work Orders</b><br />
  <a id="picture<?php echo $picCnt?>" href="<?php echo IMG_URL?>software/sw-work.jpg"><img src="<?php echo IMG_URL?>software/sw-work.jpg" onclick="activateFancyBox(<?php echo $picCnt++?>)" width="200" height="130"></a></td></tr>
  <tr valign="top">
  <td valign="top"><b>Work Details</b><br />
  <a id="picture<?php echo $picCnt?>" href="<?php echo IMG_URL?>software/sw-work-details.jpg"><img src="<?php echo IMG_URL?>software/sw-work-details-s.jpg" onclick="activateFancyBox(<?php echo $picCnt++?>)"></a></td>
  <td><b>Job Costs</b><br />
  <a id="picture<?php echo $picCnt?>" href="<?php echo IMG_URL?>software/sw-job-cost.jpg"><img src="<?php echo IMG_URL?>software/sw-job-cost-s.jpg" onclick="activateFancyBox(<?php echo $picCnt++?>)" width="200" height="130"></a></td>
  <td><b>Service Log</b><br />
  <a id="picture<?php echo $picCnt?>" href="<?php echo IMG_URL?>software/sw-log.jpg"><img src="<?php echo IMG_URL?>software/sw-log-s.jpg" onclick="activateFancyBox(<?php echo $picCnt++?>)" width="200" height="130"></a></td></tr>
  </table>  



<br /><p>

</div> <?php //close mainPanel_entry ?>
</DIV> <?php //close mainPanel ?>


</div></div> <?php //close div block, block_entry ?>



</div>	<?php //close panel?>



</div>	<?php //close content?>
</div>	<?php //close wrapper?>
<?php include(INCLUDE_PATH . 'foot.php');?>
</body></html>