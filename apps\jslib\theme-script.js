(function($){"use strict";var defaultset={barColor:'#95a5a6',trackColor:'#f1f1f1',scaleColor:false,lineCap:'round',lineWidth:15,size:100,animation:5000,font:18,fontColor:"inherit",bgColor:false};$.fn.responsivePieChart=function(options){return this.each(function(){var chartConfig=$.extend({},defaultset,options),sizeElement=getSize(chartConfig.size,$(this),chartConfig.mode);$(this).css({'height':sizeElement,'width':sizeElement,'position':'relative','display':'inline-block','margin':'auto 0','text-align':'center'});$(this).append("<div class='percent' style='position:absolute;top:0;left:0;line-height:"+sizeElement+"px;text-align:center;width:"+sizeElement+"px;color:"+chartConfig.fontColor+";font-size:"+chartConfig.font+"px;font-weight:300;'></div>");if(chartConfig.bgColor){sizeElement=sizeElement-chartConfig.lineWidth;$(this).css({'padding':chartConfig.lineWidth/2});}$(this).easyPieChart({barColor:chartConfig.barColor,trackColor:chartConfig.trackColor,scaleColor:chartConfig.scaleColor,lineCap:chartConfig.lineCap,lineWidth:chartConfig.lineWidth,size:sizeElement,animation:chartConfig.animation,onStep:function(from,to,percent){$(this.el).find('.percent').text(Math.round(percent)+'%');}});});};function getSize(chartSize,self){var defaultSize=100,sizeElement;if(chartSize==defaultSize){sizeElement=self.parent().width();}else{sizeElement=chartSize;}return sizeElement;}})(jQuery)+(function($){"use strict";$.fn.responsiveProgressBar=function(){return this.each(function(){var bar=$(this);var percentage=$(this).attr('data-percent');progress(percentage,bar);});};function progress(percent,element){var progressBarWidth=percent*element.width()/100;element.find('.progress-content').append("<div class='progress-meter'></div>").animate({width:progressBarWidth,number:percent},{duration:4000,step:function(number){element.find('.progress-meter').text(Math.round(number)+'%');}});}})(jQuery)+(function($){"use strict";if($.fn.countTo){if(!Modernizr.touch){var waypoint=new Waypoint({element:document.getElementsByClassName('count-me'),handler:function(){$('.count-me').each(function(){$(this).data('countToOptions',{formatter:function(value,options){return value.toFixed(options.decimals).replace(/\B(?=(?:\d{3})+(?!\d))/g,',');}});$('.timer').each(count);function count(options){var $this=$(this);options=$.extend({},options||{},$this.data('countToOptions')||{});$this.countTo(options);}});this.destroy();},offset:'90%'});}}})(jQuery);var Foundstrap=function($){var widthScreen=$(window).width(),mobileScreen=(widthScreen<767);var theme_animate=function(){if(Modernizr.touch||mobileScreen){$('.me-animate').css('opacity','1');}else{foundstrapAnimate($('.me-animate'));}function foundstrapAnimate(items,trigger){items.each(function(){var animateElement=$(this),animateItem=animateElement.attr('data-animate'),animateDelay=animateElement.attr('data-animate-delay');if(animateDelay>0){var delayTime=(animateDelay/1000)+'s';animateElement.css({'visibility':'visible','-webkit-animation-delay':delayTime,'-moz-animation-delay':delayTime,'-o-animation-delay':delayTime,'animation-delay':delayTime});}var animateTrigger=(trigger)?trigger:animateElement;var waypoints=animateTrigger.waypoint({handler:function(){animateElement.css('opacity','1');animateElement.addClass('animated '+animateItem);this.destroy();},offset:'80%'});});}};var theme_fancybox=function(){if($.fn.fancybox){$(".fancybox").fancybox({padding:0,openEffect:'elastic',openSpeed:250,closeEffect:'elastic',closeSpeed:250,closeClick:false,helpers:{title:{type:'outside'},media:{}}});$('.fancybox-media').attr('rel','media-gallery').fancybox({openEffect:'none',closeEffect:'none',prevEffect:'none',nextEffect:'none',padding:0,arrows:false,helpers:{media:{},buttons:{}}});}};var theme_isotope=function(){if($.fn.isotope){$(window).load(function(){var l=$(".portfolio-container");l.isotope({filter:"*",animationOptions:{duration:750,easing:"linear",queue:false}});$(".portfolio-filter a").click(function(){var n=$(this).attr("data-filter");l.isotope({filter:n,animationOptions:{duration:750,easing:"linear",queue:false}});return false;});var k=$(".portfolio-filter"),m=k.find("a");m.click(function(){var o=$(this);if(o.hasClass("selected")){return false;}var n=o.parents(".portfolio-filter");n.find(".selected").removeClass("selected");o.addClass("selected");});});}};var theme_mediaelement=function(){$("audio, video").each(function(){$(this).mediaelementplayer({success:function(media,player){player.addEventListener("playing",function(){},false);}});});$(".me-video iframe").each(function(){$(this).attr('allowFullScreen','').attr('mozallowfullscreen','').attr('webkitAllowFullScreen','');});};var theme_parallax=function(){if(!Modernizr.touch){$(".parallax1").parallax("50%",0.15);$(".parallax3, .parallax2").parallax("30%",0.15);}};var theme_scrollUp=function(){$.scrollUp({scrollText:'<i class="fa fa-chevron-up"></i>',scrollSpeed:1250});};var theme_header=function(){var clickTarget=$(".form-search-trigger"),clickObject=$(".form-search");$('html').click(function(){if($("#me-header").hasClass("header-version2")||$("#me-header").hasClass("header-version3")){if(mobileScreen){clickObject.slideUp("slow");}else{clickObject.fadeOut("slow");}}clickTarget.removeClass("active");});clickObject.click(function(e){e.stopPropagation();});clickTarget.click(function(e){e.stopPropagation();if($(this).hasClass("active")){$(this).removeClass("active");if(mobileScreen){clickObject.slideUp("slow");}else{clickObject.fadeOut("slow");}}else{$(this).addClass("active");if(mobileScreen){clickObject.slideDown("slow");}else{clickObject.fadeIn("slow");}}});};var theme_chart=function(){if($.fn.easyPieChart){$(".chart").responsivePieChart({barColor:'#169fe6'});$(".chart-green").responsivePieChart({barColor:'#87B822'});$(".chart-blue").responsivePieChart({barColor:'#169fe6'});$(".chart-yellow").responsivePieChart({barColor:'#F0C42C'});$(".chart-red").responsivePieChart({barColor:'#E75B4C'});$(".chart-gray").responsivePieChart({barColor:'#7f8c8d'});$('.progress-bar').responsiveProgressBar();}};var theme_twitter=function(){if($.fn.tweet){$("#twitter-feed").tweet({username:"envato",join_text:"auto",modpath:'js/twitter/',count:2,loading_text:"Loading tweets...",template:"<div class='twitter-text'><p>{text}</p></div>"});}};var theme_retina=function(){if($.fn.retina){$('img.retina').retina("@2x");}};var theme_carousel=function(){var testimonialCarousel=$(".testimonial-carousel");$(".testimonial-carousel-nav .left-nav").click(function(){testimonialCarousel.trigger('owl.next');});$(".testimonial-carousel-nav .right-nav").click(function(){testimonialCarousel.trigger('owl.prev');});testimonialCarousel.owlCarousel({autoPlay:6000,navigation:false,slideSpeed:400,paginationSpeed:500,singleItem:true,pagination:false});$(".client-carousel").owlCarousel({itemsCustom:[[0,1],[400,2],[700,4],[1000,5],[1200,5],[1600,5]],autoPlay:6000,navigation:false,slideSpeed:400,paginationSpeed:500,pagination:false});var blogCarousel=$(".blog-carousel");$(".blog-carousel-nav .left-nav").click(function(){blogCarousel.trigger('owl.next');});$(".blog-carousel-nav .right-nav").click(function(){blogCarousel.trigger('owl.prev');});blogCarousel.owlCarousel({autoPlay:6000,navigation:false,slideSpeed:300,paginationSpeed:400,singleItem:true,pagination:false});$(".portfolio-single-carousel").owlCarousel({autoPlay:6000,navigation:false,slideSpeed:300,paginationSpeed:400,singleItem:true,pagination:false});};var theme_responsive=function(){if(widthScreen<865){if(!$("#me-header").hasClass("header-version2")&&!$("#me-header").hasClass("header-version3")){$(".menu-container").after('<div class="search-trigger"><i class="fa fa-search"></i></div>');$(".form-search").hide();$(".search-trigger").click(function(e){e.stopPropagation();if($(this).hasClass("active")){$(this).removeClass("active");$(".form-search").slideUp("slow");}else{$(this).addClass("active");$(".form-search").css({"width":"100%","padding-bottom":"16px"}).slideDown("slow");$(".form-search").find(".input-group-placeholder").css("display","block");}});}}if(mobileScreen){$("#main-container").removeClass("box");if(!$("#me-header").hasClass("header-version2")&&!$("#me-header").hasClass("header-version3")){$(".header-info-left").prepend('<div class="menu-trigger"><i class="fa fa-bars"></i></div>');$(".search-trigger").insertAfter(".header-info-right");}else{$(".logo-container").before('<div class="menu-trigger"><i class="fa fa-bars"></i></div>');$(".form-search-trigger").insertAfter(".logo-container").addClass("search-trigger");$(".form-search").insertAfter(".menu-container");}$(".menu-trigger").click(function(e){e.preventDefault();if($(this).hasClass("active")){$(this).removeClass("active");$(".menu-container").slideUp("slow");}else{$(this).addClass("active");$(".menu-container").slideDown("slow");}});}};return{theme:function(){theme_responsive();theme_carousel();theme_parallax();theme_scrollUp();theme_header();theme_chart();theme_isotope();theme_fancybox();theme_mediaelement();theme_twitter();theme_retina();theme_animate();}};}(jQuery);