<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';

$picCnt = 0;

?>
<!DOCTYPE html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html lang="en" class="no-js"> <!--<![endif]-->
<head>
   <meta charset="utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta name="description" content="Cloud-based Operations Management Software that integrate your sales, purchase and inventory management into one single software.">
   <meta name="keywords" content="business operations software, operation management systems, operation management software,web based operation management, sales management, purchase software, inventory management, erp software">
   <meta name="techspace" content="techspace.co.th">

   <title>Business Operations, Inventory Management Software for SMBs</title>


<?php echo getPgCss(); ?>
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-main.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
      <h2>MySaas Operation Management System (OPS)</h2>
         <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
            <p>
               MySaas Operation Management System is a web-based application that allow businesses to manage customers, sales, inventories and purchases online. It integrates business functions and data into a centralized system and it facilitates the flow of information across departments, organizational units and geographical locations.
            </p>
            <p>
               MySaas OPS provides business owner and the management team with accurate, reliable and consistent view of information, thereby enabling business owner to make better decision. It is a starting point for reliable, secure and scalable enterprise solutions. With MySaas OPS, you can get started right away without the huge deployment and maintenance costs of traditional enterprise automation systems.
            </p>
            <p>MySaas OPS runs on the cloud, providing instant, real-time access to work order information anywhere, anytime. With MySaas OPS, you can:</p>
            <ul class="me-list list-circle-check">
              <li>Track customers, contacts and sales histories online</li>
              <li>Create sales quotations, post shipments and invoices</li>
              <li>Manage stocks, stock locations, transfers and adjustments</li>
              <li>Receive notifications and low stock alerts via Email or SMS</li>
              <li>Manage warranties, lot and serial information</li>
              <li>Track vendors,  purchases and item cost</li>
              <li>Raise POs, post goods receipts and invoices</li>
              <li>Configurable workflows to match your business needs.</li></ul>
            <h3>Features</h3>
            <ul class="me-list list-circle-check">
                <li>Single data source, providing accurate and consistent information across  organization.</li>
                <li>Effectively control and communicate business activities.</li>
                <li>Synchronize business processes (sales, manufacturing, finance, logistics etc.) in an collaborative environment.</li>
                <li>Improve efficiency, customer satisfaction and profitability.</li>
                <li>Provides real-time, enterprise-wide view of the business, enabling business owner to make better decision.</li>
              </ul>

  <p>MySaas OPS is fully integrated with MySaas <a href="crm.htm" target="_blank">Customer Relationship Management (CRM)</a> and <a href="service-manager.htm" target="_blank">Service Manager (SVM)</a>, providing real-time access to new sales opportunities and jobs status related to an order.</p>


            </div>

            <!--Header_section-->
               <?php include('menu-software.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->

   
      <section class="container container-gray container-border">
         <div class="row">
            <div class="large-12 column text-center">
               <h3 class="heading-light">Operating Software for Business</h3>
               <p class="gap" data-gap-bottom="38">Everything you need to boost sales, step up productivity and manage all day-to-day activities</p>

               <ul class="portfolio-container large-block-grid-4 medium-block-grid-2 small-block-grid-1 no-wrap">
                  <li class="web motion">
                     <figure class="me-image">
                        <div class="image-content">
                           <img src="<?php echo IMG_URL?>software/sales-dashboard.png" alt="sales dashboard">
                           <div class="img-overlay"></div>  
                           
                           <a href="<?php echo IMG_URL?>software/sales-dashboard.png" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">
                              <i class="fa fa-search"></i>
                           </a>
                        </div>

                        <figcaption class="image-caption">
                           <span>Sales Dashboard</span>
                           <p>Provides real-time sales overview of your business</p>
                        </figcaption>
                     </figure>
                  </li>
                  <li class="motion">
                     <figure class="me-image">
                        <div class="image-content">
                           <img src="<?php echo IMG_URL?>software/sales-order.png" alt="Sales Order">
                           <div class="img-overlay"></div>  
                           
                           <a href="<?php echo IMG_URL?>software/sales-order.png" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">
                              <i class="fa fa-search"></i>
                           </a>
                        </div>

                        <figcaption class="image-caption">
                           <span>Sales Listings</span>
                           <p>Instant access to recent sales transactions and histories</p>
                        </figcaption>
                     </figure>
                  </li>
                  <li class="web print">
                     <figure class="me-image">
                        <div class="image-content">
                           <img src="<?php echo IMG_URL?>software/sales-order.png" alt="Sales Form">
                           <div class="img-overlay"></div>  
                           
                           <a href="<?php echo IMG_URL?>software/sales-order.png" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">
                              <i class="fa fa-search"></i>
                           </a>
                        </div>

                        <figcaption class="image-caption">
                           <span>Cusomizable Sales Form</span>
                           <p>Customize and print sales forms anywhere, anytime</p>
                        </figcaption>
                     </figure>
                  </li>
                  <li class="web">
                     <figure class="me-image">
                        <div class="image-content">
                           <img src="<?php echo IMG_URL?>software/sales-order.png" alt="Accounting Integration">
                           <div class="img-overlay"></div>  
                           
                           <a href="<?php echo IMG_URL?>software/sales-order.png" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">
                              <i class="fa fa-search"></i>
                           </a>
                        </div>

                        <figcaption class="image-caption">
                           <span>Accounting Integration</span>
                           <p>Seemless integration with Financial Module</p>
                        </figcaption>
                     </figure>
                  </li>
               </ul>

            </div>
         </div>
      </section>


      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->


<?php echo getPgFootJs(); ?>
</body>
</html>