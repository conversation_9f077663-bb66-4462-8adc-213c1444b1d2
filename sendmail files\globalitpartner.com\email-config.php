<?php
// Email Configuration for Global IT Partner
// For local development (XAMPP), emails will be logged to local_emails.log
// For production, configure SMTP settings below

return [
    // SMTP Settings (for PHPMailer)
    'smtp_host' => 'smtp.gmail.com',
    'smtp_port' => 587,
    'smtp_username' => '<EMAIL>', // Change this to your email
    'smtp_password' => 'your-app-password',    // Change this to your app password
    'smtp_encryption' => 'tls', // or 'ssl'

    // Email Recipients
    // <EMAIL> 
    'to_email' => '<EMAIL>',
    'to_name' => 'Global IT Partner Support',

    // Email Settings
    'from_name' => 'Global IT Partner Contact Form',
    'subject_prefix' => 'Global IT Partner Contact: ',
    'website_name' => 'globalitpartner.com',

    // Auto-reply settings
    'auto_reply_enabled' => true,
    'auto_reply_from_email' => '<EMAIL>',
    'auto_reply_from_name' => 'Global IT Partner',
    'auto_reply_subject' => 'Thank you for contacting Global IT Partner',

    // Fallback settings (if PHPMailer is not available)
    'use_phpmailer' => false, // Set to false to use simple mail() function

    // Other providers examples:
    /*
    // For Outlook/Hotmail
    'smtp_host' => 'smtp-mail.outlook.com',
    'smtp_port' => 587,

    // For Yahoo
    'smtp_host' => 'smtp.mail.yahoo.com',
    'smtp_port' => 587,

    // For custom hosting
    'smtp_host' => 'mail.yourdomain.com',
    'smtp_port' => 587,
    */
];
?>
