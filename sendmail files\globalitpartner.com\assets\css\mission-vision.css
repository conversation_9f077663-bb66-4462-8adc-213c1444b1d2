/* Mission Vision Section Styles */

.mission-vision-section {
  padding: 80px 0;
  background-color: #f8f9fa;
}

.mission-vision-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.mission-vision-row {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  justify-content: center;
  margin-top: 60px;
}

.mission-vision-card {
  flex: 1;
  min-width: 300px;
  max-width: 550px;
  display: flex;
  flex-direction: column;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.mission-vision-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.mission-vision-title {
  font-size: 28px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 20px;
  color: #0f0d1d;
}

.mission-vision-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.mission-vision-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.mission-vision-card:hover .mission-vision-image img {
  transform: scale(1.05);
}

.mission-vision-content {
  padding: 25px;
  background: #3c72fc;
  color: #fff;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.mission-vision-content p {
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
}

/* Responsive styles */
/* PC Styles */
@media (min-width: 1200px) {
  .mission-vision-row {
    display: flex;
    gap: 30px;
  }

  .mission-vision-card {
    flex: 1;
    max-width: calc(50% - 15px);
  }

  .mission-vision-image {
    height: 280px;
  }
}

/* Tablet Styles */
@media (min-width: 768px) and (max-width: 1199px) {
  .mission-vision-row {
    display: flex;
    gap: 20px;
  }

  .mission-vision-card {
    flex: 1;
    max-width: calc(50% - 10px);
  }

  .mission-vision-image {
    height: 220px;
  }

  .mission-vision-content {
    padding: 20px;
  }

  .mission-vision-content p {
    font-size: 15px;
  }
}

/* Mobile Styles */
@media (max-width: 767px) {
  .mission-vision-section {
    padding: 50px 0;
  }

  .mission-vision-row {
    display: flex;
    flex-direction: column;
    gap: 30px;
    margin-top: 30px;
  }

  .mission-vision-card {
    width: 100%;
    max-width: 100%;
  }

  .mission-vision-image {
    height: 200px;
  }

  .mission-vision-title {
    font-size: 24px;
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .mission-vision-content {
    padding: 20px 15px;
  }

  .mission-vision-content p {
    font-size: 14px;
    line-height: 1.5;
  }
}
