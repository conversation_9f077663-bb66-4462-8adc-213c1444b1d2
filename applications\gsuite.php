<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB0_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library


$picCnt = 0;

?>
<!DOCTYPE html>

<html>
<head>
      <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge"> 
      
   <title>Google G Suite</title>

   <meta name="description" content="integrated suite of secure, cloud-based productivity and collaboration tools powered by Google AI.">
   <meta name="keywords" content="gsuite, google suite, google gsuite">

      <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">



<?php echo getPgCss(); ?>
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-gsuite.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
            <h1>Google G Suite</h1>
            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
               <p>
                  G Suite หรือชื่อเดิมว่า Google Apps for Business ซึ่งเป็นเครื่องมือที่จะอำนวยความสะดวกสบายให้กับผู้ใช้งานแบบ All in One โดย G Suite นั้นสามารถเพิ่มประสิทธิภาพการทำงานและช่วยให้บริษัทของคุณสามารถเชื่อมต่อในการทำงานและการประสานงานได้อย่างไม่สะดุด 

               </p>

               <h2>คุณสมบัติที่สำคัญของ G Suite</h2>
               <ul class="me-list list-circle-check">
                 <li>ด้วยความพร้อมกว่า 99.9% สำหรับ E-Mail รวมไปถึงการสำรองข้อมูลผ่าน Gmail อีกทั้งยังสามารถจัดการ E-Mail ขอองค์กรผ่านศูนย์กลางสำหรับจัดการ E-Mail ได้อีกด้วย</li>
                 <li>ติดต่อกับทุกคนในองค์กรและสามารถจัดการประชุมทางไกล รวมไปถึงการแชร์หน้าจอได้พร้อมกันมากถึง 100 คน ในทุกที่ทุกเวลา ด้วยบริการ Google Meet ที่เหมาะกับองค์กรที่ต้องประสานงานผ่านต่างประเทศเป็นประจำ</li>
                 <li>Google Drive ที่รองรับการเก็บข้อมูลบนคลาวด์ตามที่คุณต้องการสำหรับการเก็บข้อมูลเอกสารแผ่นงานหรือการนำเสนอต่างๆ โดยมีเครื่องมืออำนวยความสะดวกมากมาย และยังซิงค์หรือเรียกไฟล์จากอุปกรณ์ใดๆก็ได้</li>
                 <li>สามารถทำงานร่วมกันได้อย่างมีประสิทธิภาพแบบไม่จำกัดแม้ว่าจะไม่ได้อยู่สถานที่เดียวกันก็สามารถแก้ไข หรือเพิ่มเติมเอกสารได้แบบเรียลไทม์ </li>
               </ul>



      <p>
        ด้วยประสิทธิภาพการทำงานร่วมกันแบบเรียลไทม์ ความสามารถในการประชุม,ติดต่อและเครื่องมือการจัดเก็บไฟล์ต่างๆ ทำให้ G Suite เป็นเครื่องมือที่สำคัญที่จะช่วยทุ่นแรงและเพิ่มประสิทธิภาพของการทำงานให้กับองค์กรของคุณได้มากแน่นอน 
      </p>
      <h4>          
        คุณต้องการย้ายและโอนถ่ายข้อมูลทางธุรกิจที่สำคัญของคุณไปยัง G Suite หรือไม่? ปล่อยให้เราดูแลเรื่อง IT ให้คุณอย่างมีประสิทธิภาพและประสิทธิผล 
      </h4>
      <p>
        ผู้เชี่ยวฃาญด้านไอทีจาก <?php echo $SITE_NAME;?> ที่สามารถช่วยเหลือองค์กรของคุณเมื่อคุณต้องการการถ่ายโอนข้อมูลหรือบำรุงรักษาระบบ 
      </p>

      <p>
        เนื่องจากการย้ายจากระบบปัจจุบันไปยัง G Suite เป็นการทำงานที่ค่อนข้างใช้เวลาและทรัพยากรในการดำเนินการ แต่การบริการจาก Tech Space จะช่วยให้คุณประหยัดทรัพยากรในส่วนที่ใช้การถ่ายโอนข้อมูลรวมไปถึงการบำรุงรักษาระบบในองค์กรของคุณอีกด้วย 
      </p>

      <p>
        ที่ <?php echo $SITE_NAME;?> เราให้สัญญาว่าการมอบประสบการณ์การบริการลูกค้าที่มีคุณภาพสูงสุดคือเป้าหมายของเรา <?php echo $SITE_NAME;?> จะช่วยให้องค์กรของคุณสามารถใช้งาน G Suite ได้อย่างราบรี่น โดยที่จะประหยัดเวลาในการถ่ายโอนรวมไปถึงลดปัญหาที่เกิดขึ้นระหว่างการถ่ายโอนให้มากที่สุด อีกทั้งยังมั่นใจได้อีกว่าข้อมูลที่สำคัญต่อธุรกิจคุณจะถูกถ่ายโอนไปอย่างปลอดภัย G Suite จะช่วยให้คุณประหยัดทรัพยากรที่มีค่าขององค์กรและนำไปใช้งานได้อย่างเหมาะสมในทุกๆด้านของธุรกิจคุณ 
      </p>

      <p>
        ติดต่อเราเพื่อรับบริการ G Suite เพิ่มเติม
      </p>
      <p>
          <a href="<?php echo LANG_URL?>coy/contact-us.html">Contact us</a> now for more on our G suite services. 
      </p>

            </div>


            <!--Header_section-->
               <?php include('../software/menu-software.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->

   


      <?php echo getPgFootJs(); ?>

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->

</body>
</html>