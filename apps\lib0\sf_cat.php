<?php

#########################################################################
#	category.php														#
#-----------------------------------------------------------------------#
#	Author:		 	<PERSON><PERSON><PERSON> Liaw										#
#	Version:	 	3.0												#
#	Last Edited:	21 Jan 16											#
#	Remarks:															#
#																		#
#########################################################################


//------prepare page CSS ----
function getPgCss(){
    global $lang;

    if($lang=='en'){
    //  $str = '<style type="text/css" media="all">@import "' . CSS_URL . 'default_th.css";</style>';
        $hreflang = '<link rel="alternate" hreflang="x-default"  href="'. preg_replace('/\/$/','',SF_URL) . preg_replace('/^\/en/','',$_SERVER['REQUEST_URI']) .'" />
                     <link rel="alternate" hreflang="en"         href="'. preg_replace('/\/$/','',SF_URL) .  $_SERVER['REQUEST_URI'] .'" />';

    } else {
    //  $str = '<style type="text/css" media="all">@import "' . CSS_URL . 'default.css";</style>';
        $hreflang = '<link rel="alternate" hreflang="x-default"  href="'. preg_replace('/\/$/','',SF_URL) . $_SERVER['REQUEST_URI'] .'" />
                     <link rel="alternate" hreflang="en"         href="'. preg_replace('/\/$/','',SF_URL) . '/th'. $_SERVER['REQUEST_URI'] .'" />';

    }

	$str .= '

    <!-- Favicon -->
    <link rel="shortcut icon"       href="'. IMG_URL .'favicon.ico" type="image/x-icon" />
    <link rel="apple-touch-icon"    href="'. IMG_URL .'apple-touch-icon.png">

    <!-- Mobile Metas -->
    <meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">


    <!-- Google SEO -->
    '. $hreflang .'
   
    <!-- Vendor CSS -->
    <link rel="stylesheet" href="'. APPS_URL . DIR_LIB8 .'bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="'. APPS_URL . DIR_LIB8 .'font-awesome/css/font-awesome.min.css">

    <!-- Theme CSS -->
    <link rel="stylesheet" href="'. CSS_URL .'theme.css">

'
    //<link rel="stylesheet" href="'. CSS_URL .'theme-elements.css">
    //  <!-- Skin CSS -->
    //<link rel="stylesheet" href="'. CSS_URL .'skins/default.css">
.'




<!-------- enix CSS -------------->
    <link rel="stylesheet" href="'. CSS_URL .'foundstrap.css" />


    <!--[if (lt IE 9) & (!IEMobile)]>
      <script src="https://cdnjs.cloudflare.com/ajax/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

    <!-- CSS Plugin -->   
    <link rel="stylesheet" href="'. JSLIB_URL .'rs-plugin/css/settings.css" media="screen">

    <!-- theme Stylesheet -->
    <link rel="stylesheet" href="'. CSS_URL .'animate.min.css">
    <link rel="stylesheet" href="'. CSS_URL .'linea-icon.min.css">
    <link rel="stylesheet" href="'. CSS_URL .'smartmenu.min.css">
    <link rel="stylesheet" href="'. CSS_URL .'owl-carousel.min.css">
    <link rel="stylesheet" href="'. CSS_URL .'fancybox.css">
    <link rel="stylesheet" href="'. CSS_URL .'element.css">

    <link rel="stylesheet" href="'. CSS_URL .'style.css">
    <link rel="stylesheet" href="'. CSS_URL .'theme-responsive.css">   


    <!-- modernizr -->
    <script src="'. JSLIB_URL .'modernizr.js"></script>

	';

    //NOUSE    <link rel="stylesheet" href="'. CSS_URL .'revolution-responsive.css">
	return $str;
}



//------prepare page CSS ----
function getPgFootJs(){

	$str .= '
    <!-- 3rd party -->
    <script src="'. APPS_URL . DIR_LIB8 .'jquery/jquery.min.js"></script>
    <script src="'. APPS_URL . DIR_LIB8 .'bootstrap/js/bootstrap.min.js"></script>

    
    <!-- Theme Base, Components and Settings -->
    <script src="'. JSLIB_URL .'theme.js"></script>
    
    <!-- Theme Initialization Files -->
    <script src="'. JSLIB_URL .'theme.init.js"></script>


    <!-- javascript -->
    <script src="'. JSLIB_URL .'foundstrap.js"></script>
    <script src="'. JSLIB_URL .'jquery.sscr.js"></script>
    <script src="'. JSLIB_URL .'jquery.waypoints.min.js"></script>
    <script src="'. JSLIB_URL .'owl.carousel.min.js"></script>
    <script src="'. JSLIB_URL .'jquery.scrollUp.js"></script>
    <script src="'. JSLIB_URL .'jquery.retina.js"></script>


    <!-- javascript plugin - popup images  -->
    <script src="'. JSLIB_URL .'jquery.fancybox.js"></script>
    <script src="'. JSLIB_URL .'jquery.fancybox-media.js"></script>
    <script src="'. JSLIB_URL .'jquery.isotope.js"></script>


    <!-- javascript plugin -->
    <script src="'. JSLIB_URL .'rs-plugin/js/jquery.themepunch.tools.min.js"></script>
    <script src="'. JSLIB_URL .'rs-plugin/js/jquery.themepunch.revolution.min.js"></script>

    <!-- javascript core -->
    <script src="'. JSLIB_URL .'theme-script.js"></script>
    <script src="'. JSLIB_URL .'jquery.cookie.js"></script>

   <script type="text/javascript">
      jQuery(document).ready(function($) { 
         Foundstrap.theme();

         // revolution slider configuration here
         $(\'.slideshow\').revolution({
            delay:8000,
            startwidth:1080,
            startheight:520,
            hideThumbs:1,
            navigationType:"none",                  // bullet, thumb, none
            navigationArrows:"solo",                // nexttobullets, solo (old name verticalcentered), none
            navigationStyle:"square",               // round,square,navbar,round-old,square-old,navbar-old, or any from the list in the docu (choose between 50+ different item), custom
            navigationHAlign:"center",              // Vertical Align top,center,bottom
            navigationVAlign:"bottom",              // Horizontal Align left,center,right
            navigationHOffset:0,
            navigationVOffset:0,
            soloArrowLeftHalign:"left",
            soloArrowLeftValign:"center",
            soloArrowLeftHOffset:25,
            soloArrowLeftVOffset:0,
            soloArrowRightHalign:"right",
            soloArrowRightValign:"center",
            soloArrowRightHOffset:25,
            soloArrowRightVOffset:0,
            touchenabled:"on",                      // Enable Swipe Function : on/off
            onHoverStop:"on",                       // Stop Banner Timet at Hover on Slide on/off
            stopAtSlide:-1,                         // Stop Timer if Slide "x" has been Reached. If stopAfterLoops set to 0, then it stops already in the first Loop at slide X which defined. -1 means do not stop at any slide. stopAfterLoops has no sinn in this case.
            stopAfterLoops:-1,                      // Stop Timer if All slides has been played "x" times. IT will stop at THe slide which is defined via stopAtSlide:x, if set to -1 slide never stop automatic
            hideCaptionAtLimit:0,                   // It Defines if a caption should be shown under a Screen Resolution ( Basod on The Width of Browser)
            hideAllCaptionAtLilmit:0,               // Hide all The Captions if Width of Browser is less then this value
            hideSliderAtLimit:0,                    // Hide the whole slider, and stop also functions if Width of Browser is less than this value
            shadow:0,                               // 0 = no Shadow, 1,2,3 = 3 Different Art of Shadows  (No Shadow in Fullwidth Version !)
            fullWidth:"off",                        // Turns On or Off the Fullwidth Image Centering in FullWidth Modus
            fullScreen:"off"
         });
      });
   </script>



	';

	
	return $str;
}

?>