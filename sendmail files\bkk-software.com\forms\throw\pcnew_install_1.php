<?php

extract($_GET);	extract($_POST);


$submitMsg = '';



if($submitFlag == "submitForm"){
	$strTo = "<EMAIL>";
	$strSubject = "=?UTF-8?B?".base64_encode("IT request installation for ".$_POST["com_name"]." ")."?=";
	$strHeader = "MIME-Version: 1.0' . \r\n";
	$strHeader .= "Content-type: text/html; charset=utf-8\r\n";
	$strHeader .= "From: <EMAIL>";
	$appList = "";
	for( $i = 1; $i <= 8; $i++ ) {
		$app = 'app' . $i;
		if( isset($_POST[$app]) ){
			$appList .= $_POST[$app] . " / ";
		}
	} 	
	$msg = ' <html><head>
<style type="text/css">
.myTable { background-color:#FFFFFF;border-collapse:collapse; }
.myTable th { background-color:#585858;color:white; }
.myTable td, .myTable th { padding:5px;border:1px solid #000000; }
</style>
<style type="text/css">
.myStaff { background-color:#00000;border-collapse:collapse; }
.myStaff th { background-color:#000000;color:white; }
.myStaff td, .myStaff th { padding:5px;border:1px solid #000000; }
.style3 {font-size: large; font-weight: bold; }
</style>
<style type="text/css" media="screen">
body {
	font: .75em/1.5 "Lucida Grande", "Lucida Sans Unicode", helvetica, verdana, arial, sans-serif;
  	margin: 0 auto;
  	padding: 0 10px;
  	width: 610px;
}
strong {
  background: #ffc;
}
</style></head><body>
<th><p class="style3">New PC Installation Request</p></th>
<table width="700" class="myTable">
<tr><th colspan="2" align="left"><p>Company Installation</p></th></tr>
<tr><td colspan="2">' . $_POST[com_name].  ' (Request by K. ' . $_POST[request]. ') 
<br>Contact +66 '. $_POST[contact].' &nbsp;E-mail : '. $_POST[email].' </td> </tr>
<tr><th colspan="2" align="left"><p>Installation Details </p></th></tr>
<tr><td width="150">Operation System :</td>
<td width="550">( ) Operation System : ' . $_POST[os].  '</td></tr>
<tr>
  <td>Microsoft Office :</td>
  <td> ' . $_POST[office].  ' </td>
</tr>
<tr>
	<td> Default Language :</td>
	<td> ' . $_POST[lang]. ' </td>
</tr>
<tr>
	<td> Computer Name :</td>
	<td> ' . $_POST[computername]. ' </td>
</tr>
<tr>
	<td> Preferred Username :</td>
	<td> ' . $_POST[username]. ' </td>
</tr>
<tr>
	<td> Preferred Password :</td>
	<td> ' . $_POST[password]. ' </td>
</tr>
<tr>
	<td> Drive Partition :</td>
	<td> ' . $_POST[partition]. ' ('. $_POST[partition1].') </td>
</tr>
<tr>
  <td>Application :</td>
  <td> ' . $appList. ' </td>
</tr>
  <tr>
    <th colspan="2" align="left"><p>Additional Note </p></th>
  </tr>
  <tr>
    <td colspan="2"> ' . nl2br($_POST[notes]).  ' </td>
  </tr></table>
  
<th><p class="style3">For Internal Use. Pre-installation Check</p></th>
<table width="700" class="myStaff">
  <tr><td width="150">&nbsp;Operation System :</td> <td width="550">(&nbsp; ) '. $_POST[os].' </td></tr>
  <tr><td>&nbsp;Microsoft Office :</td><td> (&nbsp; ) '. $_POST[office].' </td></tr>
  <tr><td>&nbsp;Default Language :</td><td> (&nbsp; ) '. $_POST[lang].' </td></tr>
  <tr><td valign="top">&nbsp;Application : <br> <td> (&nbsp; )  ' . $appList. ' </td></tr>
  
  <tr><td colspan="2">&nbsp;Note :<br><br><br><br><br><br><br></td></tr>
</table>
</body>
</html>
';
	
	$flgSend = @mail($strTo,$strSubject,$msg,$strHeader);
	if($flgSend){
		$submitMsg = "Your submission is completed. We will contact you once the installation is completed. Thank you.";	
	} else {
		$submitMsg = "An error has occur while submitting this form. Please contact the administrator for assistance.";			
	}

	
}

?>
<!doctype html>
<html lang="en">
  <head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8">
    <title></title>
    <style type="text/css" media="screen">
body {
	font: .75em/1.5 "Lucida Grande", "Lucida Sans Unicode", helvetica, verdana, arial, sans-serif;
  	margin: 0 auto;
  	padding: 0 10px;
  	width: 610px;
}
strong {
  background: #ffc;
}
	.style1 {font-size: large}
    .style8 {font-size: 12px}
    </style>
</head>
	<body>
	<table width="790" border="0" cellspacing="0" cellpadding="0">
      <tr>
        <td height="66"><img src="image/tslogo.gif" width="505" height="103"></td>
      </tr>
    </table>
<?php
if($submitMsg!=""){
	echo '<p>&nbsp;</p><div style="background-color:#ddd; padding: 5px;">'.$submitMsg.'</div><br>';
	
}

?>
    <h1 class="style1">New PC Installation Form.</h1>
    <form id="newpc" name="newpc" method="post" action="pcnew_install_1.php">
      <p>Please complete the below form. Once the form is submitted, it will take up to 6 hours to complete the installation.</p>
<fieldset>
        <legend><span style="font-size: 14px; font-weight:bold">Contact Information</span></legend>
      <div>
     	<table width="577" border="0">
          <tr>
            <td width="158">Company Name</td>
            <td width="409">:  
            <input type="text" name="com_name" size="40"></td>
          </tr>
          <tr>
            <td>Requester Name</td>
            <td>: 
              <input type="text" name="request" size="40"></td>
          </tr>
          <tr>
            <td>Requester Contact</td>
            <td>: 
              <input type="text" name="contact" size="40"></td>
          </tr>
          <tr>
            <td>Requester E-mail</td>
            <td>: 
              <input type="text" name="email" size="40"></td>
          </tr>
        </table>
      </div>
  </fieldset>
      <fieldset>
        <legend><span style="font-size: 14px; font-weight:bold">Installation Details</span></legend>
        <table width="585" border="0">
          <tr>
            <td width="158">Operation System </td>
            <td width="417">:              
              <select id="post[os]" name="os">
                <option value="">Please Select...</option>
                <option value="Windows 8.1 64 bit">Windows 8.1 64 bit</option>
                <option value="Windows 8.1 Pro 64 bit">Windows 8.1 Pro 64 bit</option>
                <option value="Windows 8 64 bit">Windows 8 64 bit</option>
                <option value="Windows 8 Pro 64 bit">Windows 8 Pro 64 bit</option>
                <option value="Windows 7 Home Basic 64 Bit">Windows 7 Home Basic 64 Bit</option>
                <option value="Windows 7 Home Premium 64 Bit">Windows 7 Home Premium 64 Bit</option>
                <option value="Windows 7 Professional 64 Bit">Windows 7 Professional 64 Bit</option>
                <option value="Windows 7 Ultimate 64 Bit">Windows 7 Ultimate 64 Bit</option>
                <option value="Windows 8.1 32 bit">Windows 8.1 32 bit</option>
                <option value="Windows 8.1 Pro 32 bit">Windows 8.1 Pro 32 bit</option>
                <option value="Windows 8 32 bit">Windows 8 32 bit</option>
                <option value="Windows 8 Pro 32 bit">Windows 8 Pro 32 bit</option>
                <option value="Windows 7 Home Basic 32 Bit">Windows 7 Home Basic 32 Bit</option>
                <option value="Windows 7 Home Premium 32 Bit">Windows 7 Home Premium 32 Bit</option>
                <option value="Windows 7 Professional 32 Bit">Windows 7 Professional 32 Bit</option>
                <option value="Windows 7 Ultimate 32 Bit">Windows 7 Ultimate 32 Bit</option>
                <option value="Windows XP Professional">Windows XP Professional</option>
              </select></td>
          </tr>
          <tr>
            <td>Microsoft Office </td>
            <td>: 
              <select id="post[office]" name="office">
                <option value="">Please Select...</option>
                <option value="Office 365 Enterprise E1">Office 365 Enterprise E1</option>
                <option value="Office 365 Business Premium">Office 365 Business Premium</option>
                <option value="Office 365 Business">Office 365 Business</option>
                <option value="Office 365 Business Essentials">Office 365 Business Essentials</option>
                <option value="Professional 2013 64 bit">Professional 2013 64 bit</option>
                <option value="Home and Business 2013 64 bit">Home and Business 2013 64 bit</option>
                <option value="Professional Plus 2013 64 bit">Professional Plus 2013 64 bit</option>
                <option value="Professional 2010 64 bit">Professional 2010 64 bit</option>
                <option value="Home and Business 2010 64 bit">Home and Business 2010 64 bit</option>
                <option value="Professional 2013 32 bit">Professional 2013 32 bit</option>
                <option value="Home and Business 2013 32 bit">Home and Business 2013 32 bit</option>
                <option value="Professional Plus 2013 32 bit">Professional Plus 2013 32 bit</option>
                <option value="Professional 2010 32 bit">Professional 2010 32 bit</option>
                <option value="Home and Business 2010 32 bit">Home and Business 2010 32 bit</option>
                <option value="Professional 2007">Professional 2007</option>
                <option value="Small Business 2007">Small Business 2007</option>
                <option value="Professional Plus 2007">Professional Plus 2007</option>
              </select></td>
          </tr>
          <tr>
            <td>Default Language </td>
            <td>: 
              <select id="post[lang]" name="lang">
              <option value="English">English</option>
              <option value="Thai">Thai</option>
            </select></td>
          </tr>
          <tr>
            <td>Computer Name </td>
            <td>:              
            <input name="computername" type="text" id="computername"></td>
          </tr>
          <tr>
            <td>Preferred Username </td>
            <td>:            
            <input type="text" name="username"></td>
          </tr>
          <tr>
            <td>Preferred Password </td>
            <td>: 
              <input type="text" name="password"></td>
          </tr>
          <tr>
            <td>Harddrive Partition </td>
            <td><span class="style8">:
              <input name="partition" type="radio" value="IT can decides">
              Let IT decides 
		      
            <input name="partition" type="radio" value="The Customer request">
            I want 
            <input name="partition1" type="text" id="partition1" placeholder="Drive Spaces C/D/..">
            TB.</span></td>
          </tr>
          <tr>
            <td>Application</td>
            <td>:<span class="style8">
              <input id="post[teamviewer]" name="app1" type="checkbox" value="Teamviewer">
              
              <label for="label">Teamviewer</label>
              
              <label for="label"></label>
              <label for="label"></label>
              <label for="label"></label>
              <label for="label"></label>
              
              
              <label for="label"></label>
            
              <label for="label">
              <input id="post[skype]" name="app2" type="checkbox" value="Skype">
              <span class="style8">Skype
              <input id="post[line]" name="app3" type="checkbox" value="Line">
              Line 
              <input id="post[firefox]" name="app4" type="checkbox" value="Firefox">
              Firefox 
              <input id="post[chrome]" name="app5" type="checkbox" value="Chrome">
              Chrome <br> &nbsp;<span class="style8">
              <input id="post[adobe]" name="app6" type="checkbox" value="Adobe Reader">
              Adobe Reader</label>
              <input id="post[winrar]" name="app7" type="checkbox" value="WinRAR">
              WinRar 
              <input id="app6" name="app8" type="checkbox" value="WinRAR">
              7zip
            </span></td>
          </tr>
        </table>
        <div>
        <label for="post_content"><span style="font-size: 14px; font-weight:bold">Additional Information: </span></label>
        <br>Enter additional requirement or preferences:<br>
        <ul>
            <li> Addtional software applications to install.</li>
            <li>E-mail account(s), and email configuration details from web host.</li>
            <li>
			Printer information:
			  <ul>
			    <li>For standalone printer, please specify the printer's brand and model.</li>
			    <li>For network printer, we will remote access to setup the printer once we deliver the PC.</li>
		      </ul>
            </li>
        </ul>
 		<textarea id="notes" name="notes" row="7" style="width: 550px;"></textarea>
      </div>
      </fieldset>
      <p>Thank you for your submission. We will contact you when the installation is completed. </p>
      <p>
      <input type="hidden" name="submitFlag" value="submitForm">
        <input type="submit" value="Submit">
        or
        <a href="#">Cancel</a>
      </p>
  </form>
</body>
</html>