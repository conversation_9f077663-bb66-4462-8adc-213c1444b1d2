
var menuFadeOutTimer;
var menuFadeInTimer;

var menuTabTimerClose;
var browserVer;
var isNS=0,isIE=0;


//--------	
function menuSetClassName(){
	for(var i=1; i<=NUMCAT; i++){
		document.getElementById('MenuL1_'+i).className='MenuL1';
	}
}
//--------	
function menuShowLayer(catId){
	for(var i=1; i<=NUMCAT; i++){
		if(i==catId){document.getElementById('menuLayer'+i).style.display='block'; 			
		} else {if(document.getElementById('menuLayer'+i))document.getElementById('menuLayer'+i).style.display='none';}
	}

	menuSetClassName();
	document.getElementById('MenuL1_'+catId).className='MenuL1_hover';                                                                
}
//--------	
function activateMenuLayer(catId, showSubLayer){
	clearTimeout(menuFadeOutTimer);		//prevent fade out
	if(showSubLayer){	
		menuFadeOutTimer = setTimeout(menuShowLayer(catId), 140);	//highlight when mouseover L1 menubar
		clearTimeout(menuFadeInTimer);
		menuFadeInTimer = setTimeout(menuPlus(), 220);		
	} else {
		menuFadeOutTimer = setTimeout(menuMinus(), 140);	//highlight when mouseover L1 menubar
	}
}


if((navigator.appName == "Netscape") && (parseInt(navigator.appVersion,10) >=5)){	//special handler for Netscape
	isNS=1;
}else{	//special handler for IE
	browserVer=navigator.appVersion.split('MSIE');
	browserVer=parseInt(browserVer[1],10);	
	if(browserVer<=8){	//IE8 and below
		isIE=1;
	}else{isNS=1;}
}

var menuTimerMinus,menuTimerPlus;
var shuttle=20;
var shuttle_s=8; 
var shuttle_change_v=80; 
var last_open_height=120;

function menuDivMinus(h){
	var y;
	if ( document.getElementById('menuContent').clientHeight>=h){  	  
	    y=document.getElementById('menuContent').clientHeight-shuttle;     
		if(y < 0)y=h;
		document.getElementById('menuContent').style.height=y+'px';  		  
	}    
	if(document.getElementById('menuContent').clientHeight<=h)clearInterval(menuTimerMinus);;
}

function menuDivPlus(h){	
	var y,event_height,range1,range2;
	
	event_height=document.getElementById('menuContent').clientHeight;
	range1=h-event_height;
	if (isIE && event_height<h){ // mean ie4,ie5,ie5.5 or above
		if(range1 <= shuttle_change_v){
		document.getElementById('menuContent').style.height=event_height+shuttle;
		}else{
		document.getElementById('menuContent').style.height=event_height+shuttle_s;      
		}  
	}
	if (isNS && event_height<h){
		if(range1 <= shuttle_change_v){
	 		y=event_height+shuttle;
		}else{
		  y=event_height+shuttle_s;       
		}
	    document.getElementById('menuContent').style.height=y+'px';
	}
    if(event_height>=h)clearInterval(menuTimerPlus);;		
}


function menuPlus(){  
	clearInterval(menuTimerMinus);;
	clearInterval(menuTimerPlus);;
	menuTimerPlus=setInterval('menuDivPlus('+last_open_height+')',1);
}
 
 
function menuMinus(){
	 clearInterval(menuTimerMinus);;
	 clearInterval(menuTimerPlus);;
	 menuTimerMinus=setInterval('menuDivMinus(0)',1);	 
	 menuSetClassName();
 
}  



function menuTabClose(){
	clearTimeout(menuTabTimerClose);
	menuTabTimerClose = setTimeout('menuMinus()', 300);
}