<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB0_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library


$picCnt = 0;

?>
<!DOCTYPE html>

<html>
<head>
   <meta charset="utf-8">
   <meta http-equiv="X-UA-Compatible" content="IE=edge"> 
   <meta name="description" content="Microsoft Office 365 is a suite of cloud-based productivity and collaboration applications that integrates all Microsoft's existing applications online.">
   <meta name="keywords" content="office 365, microsoft office 365, office365, microsoft office">
   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">

   <title>Microsoft Office 365</title>


<?php echo getPgCss(); ?>
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-software_en.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
            <h1>Microsoft Office 365</h1>
            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
               <p>
               	Microsoft Office 365 is an all-in-one collaboration tool that incorporates their desktop-based software with their cloud-based collaboration services. It allows your company to access and edit files on a centralized cloud on any device, creating a seamless user experience for your staff and in turn, enhancing productivity. It is a cost-effective solution in integrating your company’s communication, information sharing and data transfer.

               </p>

               <h2>Key Features of Office 365</h2>
               <ul class="me-list list-circle-check">
                 <li>Secure collaboration, any time, any where, any device.</li>
                 <li>Real-Time Co-Authoring (Word, Excel, PowerPoint).</li>
                 <li>Chat with co-workers in Office apps.</li>
                 <li>Business intelligence (BI) solution to analyze and visualize data.</li>
               </ul>



				<p>
					Microsoft Office is the perfect tool for companies looking to have a secure, seamless and effective suite tool that covers every aspect of their business. From insights to management, Office enables your company to run to its fullest potential.
				</p>
				<p>
					<?php echo $SITE_NAME;?> helps your company to migrate, implement and maintain your Microsoft Office 365 services, allowing your company to save money, time and resources. Be IT worry free with <?php echo $SITE_NAME;?>.
				</p>
				<H4>
					Leave IT to us to deliver enterprise-wide migration, implementation and maintenance of Office 365 for your business.
				</H4>
				<p>
					By engaging in <?php echo $SITE_NAME;?>’s professional services, you will be able to enjoy a minimal disruption, hassle-free and secure migration of your critical data onto Office 365. Allowing you to effortlessly integrate your enterprise into Office 365 at a reasonable, competitive rate. Our strong commitment to customer service excellence also ensures you a quick response and turnaround rate while at the same time catering to every one of your organizational needs.
				</p>
				<p>
					<a href="<?php echo LANG_URL?>coy/contact-us.html">Contact us</a> today and find out more how we can help with your office 365 implementation.
				</p>


            </div>

            <!--Header_section-->
               <?php include('../software/menu-software.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->

   


      <?php echo getPgFootJs(); ?>

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->

</body>
</html>