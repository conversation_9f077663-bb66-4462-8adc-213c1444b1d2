<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB0_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library


$picCnt = 0;

?>
<!DOCTYPE html>

<html>
<head>
   <meta charset="utf-8">
   <meta http-equiv="X-UA-Compatible" content="IE=edge"> 
	<title>Data Backup Solutions for Server and Workstations</title>
   <meta name="description" content="<?php echo $SITE_NAME;?>offers, simple, reliable and affordable backup and disaster recovery solutions for physical, virtual and cloud environments.">
   <meta name="keywords" content="backup solutions,data backup solutions, pc data backup, server backup, recovery solutions">
   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">

   <link rel="canonical" href="https://www.bkk-it.com/en/solutions/backup-solutions.html" />

<?php echo getPgCss(); ?>
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-backup-solutions_en.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
      <h1>Backup Solutions</h1>
         <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >

            <p>
               <?php echo $SITE_NAME?> offers the following backup solutions to help protect your data.
            </p>
           <h4>1. For Home /  Home Office</h4>
            <p>PC backup &amp;  recovery, cloud storage, mobile file access, device synchronization, disk  partitioning, data migration, and more. Protect your digital life with  personal solutions</p>
            <p>Our services include:</p>
            <ul class="me-list list-circle-check">
               <li>PC Backup &amp; Recovery</li>
               <li>Data Syncrhonization</li>
               <li>Cloud Storage</li>
               <li>Disk Partitioning</li>
            </ul>


            <h4>2. For Small  Business </h4>
            <p>
               Simple,  reliable and affordable backup and disaster recovery solutions for physical,  virtual and cloud environments to keep your business up-and-running at all  times.
            </p>
            <ul class="me-list list-circle-check">
              <li>Data Replication</li>
              <li>Remote/Cloud Backup</li>
              <li>Backup &amp; Recovery</li>
              <li>Virtual Backup</li>
            </ul>


            <h4>3. For Large  Business / Enterprise </h4>
            <p>
               A unified  platform for backup, disaster recovery and data protection for heterogeneous  Windows and Linux environments. Get support for cloud backup, multiple  hypervisors, flexible migration, and more
            </p>
            <ul class="me-list list-circle-check">
              <li>Virtual Environment Backup</li>
              <li>Unified Platform</li>
              <li>Disaster Recovery with Imaging</li>
            </ul>




         </div>

            <!--Header_section-->
               <?php include('menu-solutions.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->

      <?php echo getPgFootJs(); ?>

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->

 
</body>
</html>