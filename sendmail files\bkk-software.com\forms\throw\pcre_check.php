<?php
	$strTo = "<EMAIL>";
	$strSubject = "=?UTF-8?B?".base64_encode("IT request installation for ".$_POST["com_name"]." ")."?=";
	$strHeader = "MIME-Version: 1.0' . \r\n";
	$strHeader .= "Content-type: text/html; charset=utf-8\r\n";
	$strHeader .= "From: <EMAIL>";
	$appList = "";
	for( $i = 1; $i <= 7; $i++ ) {
		$app = 'app' . $i;
		if( isset($_POST[$app]) ){
			$appList .= $_POST[$app] . " / ";
		}
	} 	
	$msg = ' <html><head>
<style type="text/css">
.myTable { background-color:#FFFFFF;border-collapse:collapse; }
.myTable th { background-color:#585858;color:white; }
.myTable td, .myTable th { padding:5px;border:1px solid #000000; }
</style>
<style type="text/css">
.myStaff { background-color:#00000;border-collapse:collapse; }
.myStaff th { background-color:#000000;color:white; }
.myStaff td, .myStaff th { padding:5px;border:1px solid #000000; }
.style3 {font-size: large; font-weight: bold; }
</style>
<style type="text/css" media="screen">
body {
	font: .75em/1.5 "Lucida Grande", "Lucida Sans Unicode", helvetica, verdana, arial, sans-serif;
  	margin: 0 auto;
  	padding: 0 10px;
  	width: 610px;
}
strong {
  background: #ffc;
}
</style></head><body>
<th><p class="style3">Re-Installation Request</p></th>
<table width="700" class="myTable">
<tr><th colspan="2" align="left"><p>Company Details</p></th></tr>
<tr><td colspan="2">' . $_POST[com_name].  ' (Request by K. ' . $_POST[request]. ') 
<br>Contact +66 '. $_POST[contact].' &nbsp;E-mail : '. $_POST[email].' 
<tr><th colspan="2" align="left"><p>Machine Details</p></th></tr>
<tr><td colspan="2">Machine ID : '. $_POST[machine]. ' | Username : '. $_POST[username]. '
<br>Note : '.$_POST[textarea2]. '</td></tr>

<tr><th colspan="2" align="left"><p>Re-Installation Details </p></th></tr>

<tr><td width="150">Operation System :</td>
<td width="550"> ' . $_POST[os].  '</td></tr>
<tr>
  <td>Microsoft Office :</td>
  <td> ' . $_POST[office].  ' </td>
</tr>
<tr>
	<td> Default Language :</td>
	<td> ' . $_POST[lang]. ' </td>
</tr>
<tr>
  <td>Application :</td>
  <td> ' . $appList. ' </td>
</tr>
  <tr>
    <th colspan="2" align="left"><p>Additional Note </p></th>
  </tr>
  <tr>
    <td colspan="2"> ' . $_POST[content].  ' </td>
  </tr></table>
<th><p class="style3">For Internal Use. Pre-installation Check</p></th>
<table width="700" class="myStaff">
  <tr><td width="150">&nbsp;Operation System :</td> <td width="550">(&nbsp; ) '. $_POST[os].' </td></tr>
  <tr><td>&nbsp;Microsoft Office :</td><td> (&nbsp; ) '. $_POST[office].' </td></tr>
  <tr><td>&nbsp;Default Language :</td><td> (&nbsp; ) '. $_POST[lang].' </td></tr>
  <tr><td valign="top">&nbsp;Application : <br> <td> (&nbsp; )  ' . $appList. ' </td></tr>
  
  <tr><td colspan="2">&nbsp;Note :<br><br><br><br><br><br><br></td></tr>
</table>
</body>
</html>
';
	
	$flgSend = @mail($strTo,$strSubject,$msg,$strHeader);
	if($flgSend)
	{
		echo "<h1>Email Sending.</h1>";
		
	}
	else
	{
		echo "<h1>Email Can Not Send.</h1>";
	}
?>
<Meta http-equiv="refresh"content="2;URL=http://www.techspace.co.th/">

