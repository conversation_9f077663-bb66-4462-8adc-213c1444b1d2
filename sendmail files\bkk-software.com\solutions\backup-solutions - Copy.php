<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';

$picCnt = 0;

?>
<!DOCTYPE html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html lang="en" class="no-js"> <!--<![endif]-->
<head>
   <meta charset="utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta name="description" content="<?php echo $SITE_NAME;?>offers, simple, reliable and affordable backup and disaster recovery solutions for physical, virtual and cloud environments.">
   <meta name="keywords" content="backup solutions,data backup solutions, pc data backup, server backup, recovery solutions">
   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">

   <title>Data Backup Solutions for Server and Workstations</title>

   <!-- retina Bookmark Icon -->
   <link rel="apple-touch-icon-precomposed" href="apple-icon.png" />

   <!-- CSS -->
   <link href="<?php echo CSS_URL?>foundstrap.css" rel="stylesheet" />
   <link href="<?php echo CSS_URL?>font-awesome.min.css" rel="stylesheet" />

   <!--[if (lt IE 9) & (!IEMobile)]>
      <script src="https://cdnjs.cloudflare.com/ajax/libs/respond.js/1.4.2/respond.min.js"></script>
   <![endif]-->
   
   <!-- CSS Plugin -->   
   <link href="<?php echo JSLIB_URL?>rs-plugin/css/settings.css" rel="stylesheet" media="screen">

   <!-- theme Stylesheet -->
   <link href="<?php echo CSS_URL?>style.css" rel="stylesheet">
   <link href="<?php echo CSS_URL?>theme-responsive.css" rel="stylesheet">   
   
 
   <!-- favicon -->
   <link rel="shortcut icon" href="<?php echo IMG_URL;?>favicon.ico">
   
   <!-- modernizr -->
   <script src="<?php echo JSLIB_URL?>modernizr.js"></script>
   
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-main.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
      <h2>Backup Solutions</h2>
         <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >

            <p>
               <?php echo $SITE_NAME?> offers the following backup solutions to help protect your data.
            </p>
           <h4>1. For Home /  Home Office</h4>
            <p>PC backup &amp;  recovery, cloud storage, mobile file access, device synchronization, disk  partitioning, data migration, and more. Protect your digital life with  personal solutions</p>
            <p>Our services include:</p>
            <ul class="me-list list-circle-check">
               <li>PC Backup &amp; Recovery</li>
               <li>Data Syncrhonization</li>
               <li>Cloud Storage</li>
               <li>Disk Partitioning</li>
            </ul>


            <h4>2. For Small  Business </h4>
            <p>
               Simple,  reliable and affordable backup and disaster recovery solutions for physical,  virtual and cloud environments to keep your business up-and-running at all  times.
            </p>
            <ul class="me-list list-circle-check">
              <li>Data Replication</li>
              <li>Remote/Cloud Backup</li>
              <li>Backup &amp; Recovery</li>
              <li>Virtual Backup</li>
            </ul>


            <h4>3. For Large  Business / Enterprise </h4>
            <p>
               A unified  platform for backup, disaster recovery and data protection for heterogeneous  Windows and Linux environments. Get support for cloud backup, multiple  hypervisors, flexible migration, and more
            </p>
            <ul class="me-list list-circle-check">
              <li>Virtual Environment Backup</li>
              <li>Unified Platform</li>
              <li>Disaster Recovery with Imaging</li>
            </ul>




         </div>

            <!--Header_section-->
               <?php include('menu-solutions.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->

   

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->

   <!-- javascript -->
   <script src="<?php echo JSLIB_URL?>jquery.min.js"></script>
   <script src="<?php echo JSLIB_URL?>foundstrap.js"></script>
   <script src="<?php echo JSLIB_URL?>jquery.sscr.js"></script>
   <script src="<?php echo JSLIB_URL?>jquery.waypoints.min.js"></script>
   <script src="<?php echo JSLIB_URL?>owl.carousel.min.js"></script>
   <script src="<?php echo JSLIB_URL?>jquery.scrollUp.js"></script>
   <script src="<?php echo JSLIB_URL?>jquery.retina.js"></script>

   <!-- javascript plugin - popup images  -->
   <script src="<?php echo JSLIB_URL?>jquery.fancybox.js"></script>
   <script src="<?php echo JSLIB_URL?>jquery.fancybox-media.js"></script>
   <script src="<?php echo JSLIB_URL?>jquery.isotope.js"></script>


   <!-- javascript plugin -->
   <script src="<?php echo JSLIB_URL?>rs-plugin/js/jquery.themepunch.tools.min.js"></script>
   <script src="<?php echo JSLIB_URL?>rs-plugin/js/jquery.themepunch.revolution.min.js"></script>

   <!-- javascript core -->
   <script src="<?php echo JSLIB_URL?>theme-script.js"></script>
   <script src="<?php echo JSLIB_URL?>jquery.cookie.js"></script>
   <script src="<?php echo JSLIB_URL?>theme-switcher.js"></script>

   <script type="text/javascript">
      jQuery(document).ready(function($) { 
         Foundstrap.theme();

         // revolution slider configuration here
         $('.slideshow').revolution({
            delay:8000,
            startwidth:1080,
            startheight:520,
            hideThumbs:1,
            navigationType:"none",                  // bullet, thumb, none
            navigationArrows:"solo",                // nexttobullets, solo (old name verticalcentered), none
            navigationStyle:"square",               // round,square,navbar,round-old,square-old,navbar-old, or any from the list in the docu (choose between 50+ different item), custom
            navigationHAlign:"center",              // Vertical Align top,center,bottom
            navigationVAlign:"bottom",              // Horizontal Align left,center,right
            navigationHOffset:0,
            navigationVOffset:0,
            soloArrowLeftHalign:"left",
            soloArrowLeftValign:"center",
            soloArrowLeftHOffset:25,
            soloArrowLeftVOffset:0,
            soloArrowRightHalign:"right",
            soloArrowRightValign:"center",
            soloArrowRightHOffset:25,
            soloArrowRightVOffset:0,
            touchenabled:"on",                      // Enable Swipe Function : on/off
            onHoverStop:"on",                       // Stop Banner Timet at Hover on Slide on/off
            stopAtSlide:-1,                         // Stop Timer if Slide "x" has been Reached. If stopAfterLoops set to 0, then it stops already in the first Loop at slide X which defined. -1 means do not stop at any slide. stopAfterLoops has no sinn in this case.
            stopAfterLoops:-1,                      // Stop Timer if All slides has been played "x" times. IT will stop at THe slide which is defined via stopAtSlide:x, if set to -1 slide never stop automatic
            hideCaptionAtLimit:0,                   // It Defines if a caption should be shown under a Screen Resolution ( Basod on The Width of Browser)
            hideAllCaptionAtLilmit:0,               // Hide all The Captions if Width of Browser is less then this value
            hideSliderAtLimit:0,                    // Hide the whole slider, and stop also functions if Width of Browser is less than this value
            shadow:0,                               // 0 = no Shadow, 1,2,3 = 3 Different Art of Shadows  (No Shadow in Fullwidth Version !)
            fullWidth:"off",                        // Turns On or Off the Fullwidth Image Centering in FullWidth Modus
            fullScreen:"off"
         });
      });
   </script>
</body>
</html>