<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB0_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library


$picCnt = 0;

?>
<!DOCTYPE html>

<html>
<head>
   <meta charset="utf-8">
   <meta http-equiv="X-UA-Compatible" content="IE=edge"> 
   <meta name="description" content="<?php echo $SITE_NAME;?> เป็นบริษัทให้ บริการดูแลระบบคอมพิวเตอร์ หรือ IT Outsource Service เพื่อดูแลและป้องกันให้ระบบคอมพิวเตอร์ของลูกค้าทำงานได้อย่างราบรื่นและเต็มประสิทธิภาพ.">
   <meta name="keywords" content="IT Maintenance, IT Outsource, บริการดูแลระบบคอมพิวเตอร์รายเดือน, รับวางระบบคอมพิวเตอร์, IT solution, รับดูแลคอมพิวเตอร์, รับดูแลระบบไอที, บริการดูแลระบบไอทีรายเดือน, ซ่อมบำรุงเครื่องคอมพิวเตอร์, รับวางระบบคอมพิวเตอร์, IT Services, บริษัทดูแลระบบคอมพิวเตอร์">
   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">

   <title>IT Outsource & บริการดูแลระบบคอมพิวเตอร์</title>


<?php echo getPgCss(); ?>  
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-services-it-support_th.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
            <h1>IT Support &amp; Maintenance Services</h1>
            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
              <p>
                <?php echo $SITE_NAME;?> เป็นบริษัทดูแลระบบคอมพิวเตอร์ หรือ IT Outsource Service โดยหน้าที่หลักของเรา คือการดูแลและป้องกันให้ระบบคอมพิวเตอร์ของลูกค้าไม่ว่าจะ เป็นระบบ Server, 
                ระบบ Network และเครื่องคอมพิวเตอร์ ทำงานได้อย่างราบรื่นและเต็มประสิทธิภาพ เรามีทีมผู้เชี่ยวชาญที่ให้บริการแก้ไขปัญหาต่างๆในองค์กร บริการดูแลระบบคอมพิวเตอร์ หรือ IT Outsource Service ช่วยลดปัญหาด้านการจ้างงานและการฝึกอบรมพนักงานด้านไอที 
                <?php echo $SITE_NAME;?> คัดสรรบุคลากรที่มีคุณภาพเสมือนเราเป็นทีมงานในองค์กร รวมทั้งให้คำปรึกษาแนะนำการพัฒนาองค์กรให้ก้าวทันกับเทคโนโลยี เพื่อเพิ่มศักยภาพในการทำงานระบบคอมพิวเตอร์ของคุณอย่างสมบูรณ์ ด้วยบริการดูแลระบบคอมพิวเตอร์ (IT Outsource) จากเทคสเปซ
              </p>

               <img src="<?php echo IMG_URL?>services/it-support-specialist.jpg" width="700" height="160" alt="บริการดูแลระบบคอมพิวเตอร์  IT Outsource" title="บริการดูแลระบบคอมพิวเตอร์ IT Outsource " />              


              <p>
                เรายังจัดเตรียมบริการทางด้านไอทีแบบครบวงจรไม่ว่าจะเป็นจัดจำหน่ายอุปกรณ์ คอมพิวเตอร์ (Hardware) ซอฟท์แวร์ (Software) รวมถึงการออกแบบ, ติดตั้งระบบคอมพิวเตอร์และเน็ตเวิร์ค ด้วยบริการดูแลระบบคอมพิวเตอร์ 
                (IT Outsource) จากเทคสเปซ เราให้บริการครอบคลุมด้วยทีมงานที่มีความเชี่ยวชาญ อย่างมืออาชีพ 

              </p>


               <h2>บริการดูแลระบบคอมพิวเตอร์ IT Outsource Services</h2>
               <ul class="me-list list-circle-check">
                  <li>บริการดูแล แก้ไขปัญหาอุปกรณ์ฮาร์ดแวร์ทั้งเครื่อง Server, Computer, Printer และอุปกรณ์ต่อพวงต่างๆ เป็นต้น</li>
                  <li>บริการดูแล แก้ไขปัญหาระบบ Internet, Network, Firewall เป็นต้น</li>
                  <li>บริการซ่อมแซมอุปกรณ์คอมพิวเตอร์ </li>
                  <li>บริการติดตั้ง และอัพเดท ซอฟท์แวร์ต่างๆ เช่น Windows, MS Office, Antivirus เป็นต้น</li>
                  <li>บริการออกแบบ ปรับปรุง และรับวางระบบคอมพิวเตอร์และเน็ตเวิร์คภายในองค์กรหรือเชื่อมต่อระหว่างองค์กร</li>
                  <li>บริการให้คำปรึกษา และแก้ไขปัญหาทางโทรศัพท์</li>
                  <li>บริการแก้ไขปัญหาผ่านทางระบบ Remote Access</li>
                  <li>จัดเจ้าหน้าที่ไอทีดูแลประจำแต่ละลูกค้าเพื่อความต่อเนื่องในการให้บริการ</li>
                  <li>บริการจัดหาอุปกรณ์คอมพิวเตอร์ (Hardware & Software) ที่มีคุณภาพ ในราคาถูก</li>
               </ul>


               <h2>ทำไมต้องเลือกใช้บริการดูแลระบบคอมพิวเตอร์ IT Outsource จาก <?php echo $SITE_NAME;?></h2>
               <ul class="me-list list-circle-check">
                  <li>เรามีบุคลากรที่มีประสบการณ์ และทีมงานมืออาชีพในการแก้ไขปัญหา</li>
                  <li>มีความรวดเร็วในการแก้ไขปัญหา และสามารถแก้ปัญหาได้อย่างเด็ดขาด</li>
                  <li>ช่วยลดปัญหาในการเสียเวลาการจ้างงานและการฝึกอบรมพนักงาน</li>
                  <li>ประหยัดค่าใช้จ่าย โดยจ่ายค่าบริการในราคา IT Support แต่ได้บริการใน Skill System Engineer</li>
                  <li>จัดทำ Network Diagram และ IT Profile เพื่อให้ลูกค้าทราบถึงข้อมูลการเชื่อมโยงของระบบและอุปกรณ์ที่ลูกค้ามีอยู่</li>
                  <li>ตรวจสอบและสำรองข้อมูลที่สำคัญ (Data Backup) เพื่อพร้อมรองรับในสถานะการฉุกเฉินตลอดเวลา</li>
                  <li>มีการตรวจเช็ค บำรุงรักษาสภาพเครื่องคอมพิวเตอร์และซอฟท์แวร์ โดยละเอียดทุกครั้งที่เข้าให้บริการ</li>
                  <li>มีรายงานสรุป การแก้ปัญหา และแนวทางแก้ไข พร้อมแนะนำ IT Solution ให้องค์กรได้รับทราบ เพื่อการพัฒนาระบบต่อไป</li>
                  <li>อัพเดทข่าวสารและข้อมูลหรือการแจ้งเตือนภัยเกี่ยวกับระบบคอมพิวเตอร์ให้ท่านตลอดเวลา</li>
                  <li>มีการนำ Application ที่ทันสมัยมาใช้ในงาน Support เพื่อบันทึก และการติดตาม</li>
                  <li>เป็นตัวกลางช่วยประสานงานกับระบบหรือโปรแกรมอื่นๆที่เกี่ยวข้อง</li>

               </ul>

               <?php include('it-support-plan.php');?>
              <p>If you are ready to begin enjoying a new level of consistent, proactive IT support for your company,  please contact our friendly sales associates at <strong>02 381-9075</strong> or <a href="<?php echo LANG_URL?>coy/contact-us.html">submit your enquiry</a> to us. </p>

            </div>

            <!--Header_section-->
               <?php include('menu-services.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->

      <?php echo getPgFootJs(); ?>

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->

</body>
</html>