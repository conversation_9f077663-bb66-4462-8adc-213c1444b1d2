<!DOCTYPE html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html lang="en" class="no-js"> <!--<![endif]-->
<head>
   <meta charset="utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta name="keywords" content="corporate, minimalist, html5, responsive, simple" />
   <meta name="description" content="Enix - Minimalist Business Template">
   <meta name="indonez" content="indonez.com">

   <title>Enix - Minimalist Business Template</title>
   
   <!-- retina Bookmark Icon -->
   <link rel="apple-touch-icon-precomposed" href="apple-icon.png" />

   <!-- CSS -->
   <link href="css/foundstrap.css" rel="stylesheet" />
   <link href="css/font-awesome.min.css" rel="stylesheet" />

   <!--[if (lt IE 9) & (!IEMobile)]>
      <script src="https://cdnjs.cloudflare.com/ajax/libs/respond.js/1.4.2/respond.min.js"></script>
   <![endif]-->
   
   <!-- CSS Plugin -->   
   <link href="js/rs-plugin/css/settings.css" rel="stylesheet" media="screen">

   <!-- theme Stylesheet -->
   <link href="css/style.css" rel="stylesheet">
   <link href="css/theme-responsive.css" rel="stylesheet">   
   
   <!-- theme Option -->
   <link href="css/theme-switcher.css" rel="stylesheet" />
   <link href="css/theme/default.css" id="theme" rel="stylesheet">

   <!-- favicon -->
   <link rel="shortcut icon" href="img/favicon.ico">
   
   <!-- modernizr -->
   <script src="js/modernizr.js"></script>
   
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!-- headaer -->
      <header id="me-header" class="header-version2">
      
         <div class="row">
            <div class="large-12 column">
               <!-- logo container -->
               <div class="logo-container">
                  <a href="index.html">
                     <img class="retina" src="img/logo.png" alt="Logo">
                  </a>
               </div> 
               <!-- logo container end here -->
               
               <!-- menu navigation -->
               <div class="navigation-container">

                  <div class="form-search-trigger">
                     <i class="fa fa-search"></i>
                     <form action="#" class="form-search">
                        <div class="input-group-placeholder addon-right">
                           <input name="search" type="text" class="form-control" placeholder="Search here..">
                           <span class="input-group-icon"><i class="fa fa-search"></i></span>
                        </div>
                     </form>
                  </div>

                  
                  <nav class="menu-container">
                     <ul id="menu" class="sm me-menu">
                        <li class="active"><a href="index.html">Home</a>
                           <ul>
                              <li class="active"><a href="index-version2.html">Home - Version 2</a></li>
                              <li><a href="index-version3.html">Home - Version 3</a></li>
                           </ul>
                        </li>
                        <li><a href="#">Pages</a>
                           <ul>
                              <li><a href="about.html">About Us</a></li>
                              <li><a href="services.html">Services</a></li>
                              <li><a href="testimonials.html">Testimonials</a></li>
                              <li><a href="pricing-plan.html">Pricing Plan</a></li>
                              <li><a href="team.html">Our Team</a></li>
                              <li><a href="single.html">Blog Post</a></li>
                              <li><a href="portfolio-single.html">Portfolio Single</a></li>
                              <li><a href="faq.html">FAQ Page</a></li>
                              <li><a href="sitemap.html">Sitemap</a></li>
                              <li><a href="404.html">404 Error</a></li>
                           </ul>
                        </li>
                        <li><a href="#">Shortcode</a>
                           <ul>
                              <li><a href="column.html">Column</a></li>
                              <li><a href="button-list.html">Button &amp; List</a></li>
                              <li><a href="icon-list.html">Icon List</a></li>
                              <li><a href="icon-use.html">Icon use</a></li>
                              <li><a href="typography.html">Typography</a></li>
                              <li><a href="table.html">Table</a></li>
                              <li><a href="panel-promo.html">Panel &amp; Promobox</a></li>
                              <li><a href="tab-accordion.html">Tab &amp; Accordion</a></li>
                              <li><a href="alert-progress.html">Alert &amp; Progress</a></li>
                           </ul>
                        </li>
                        <li><a href="#">Portfolio</a>
                           <ul>
                              <li><a href="portfolio-2column.html">Portfolio 2 Column</a></li>
                              <li><a href="portfolio-3column.html">Portfolio 3 Column</a></li>
                              <li><a href="portfolio-4column.html">Portfolio 4 Column</a></li>
                              <li><a href="#">Dropdown Level 1</a>
                                 <ul>
                                    <li><a href="#">Dropdown Level 2</a></li>
                                    <li><a href="#">Dropdown Level 2</a></li>
                                    <li><a href="#">Dropdown Level 2</a>
                                       <ul>
                                          <li><a href="#">Dropdown Level 3</a></li>
                                          <li><a href="#">Dropdown Level 3</a></li>
                                          <li><a href="#">Dropdown Level 3</a></li>
                                       </ul>
                                    </li>
                                 </ul>
                              </li>
                           </ul>
                        </li>
                        <li><a href="blog.html">Blog</a></li>
                        <li><a href="contact.html">Contact</a></li>
                     </ul>
                  </nav>
                  <!-- menu navigation end here -->
               </div>
            </div>
         </div>
      </header>
      <!-- header end here -->  
      
      <!-- slideshow -->
      <div class="container" id="slideshow-container">
         <div class="slideshow">
            <ul>
               <li data-transition="fade" data-masterspeed="1500">
                  <img src="img/slideshow/slide2-bg.jpg" alt="slider background">
                  
                  <!-- caption slider 2 -->
                  <div class="tp-caption customin customout tp-resizeme slider-title slider2-title" 
                     data-x="305" data-hoffset="0"
                     data-y="211" data-voffset="0"
                     data-customin="x:0;y:0;z:0;rotationX:0;rotationY:0;rotationZ:0;scaleX:0;scaleY:0;skewX:0;skewY:0;opacity:0;transformPerspective:600;transformOrigin:50% 50%;"
                     data-customout="x:0;y:0;z:0;rotationX:0;rotationY:0;rotationZ:0;scaleX:0;scaleY:0;skewX:0;skewY:0;opacity:0;transformPerspective:600;transformOrigin:50% 50%;"
                     data-speed="1500"
                     data-start="2000"
                     data-easing="Power3.easeInOut"
                     data-splitin="chars"
                     data-splitout="chars"
                     data-elementdelay="0.08"
                     data-endelementdelay="0.08"
                     data-end="8500"
                     data-endspeed="500">
                     Get Ready For
                  </div>
                  <div class="tp-caption lfb ltb slider-caption slider2-caption" 
                     data-x="333" 
                     data-y="262"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="3500" 
                     data-end="8500" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                     Great Solution for Online Business
                  </div>
                  <div class="tp-caption lfb ltb slider2-button" 
                     data-x="444" 
                     data-y="320"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="4000" 
                     data-end="8250" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                     <a class="slider-button" href="#">Purchase Now 
                        <i class="fa fa-angle-double-right"></i>
                     </a>
                  </div>
               </li>

               <li data-transition="fade" data-masterspeed="1500">
                  <img src="img/slideshow/slide3-bg.png" alt="slider background">

                  <div class="tp-caption customin customout tp-resizeme slider-title slide3-title1" 
                     data-x="-15" data-hoffset="0"
                     data-y="124" data-voffset="0"
                     data-customin="x:0;y:0;z:0;rotationX:0;rotationY:0;rotationZ:0;scaleX:0;scaleY:0;skewX:0;skewY:0;opacity:0;transformPerspective:600;transformOrigin:50% 50%;"
                     data-customout="x:0;y:0;z:0;rotationX:0;rotationY:0;rotationZ:0;scaleX:0;scaleY:0;skewX:0;skewY:0;opacity:0;transformPerspective:600;transformOrigin:50% 50%;"
                     data-speed="1500"
                     data-start="1500"
                     data-easing="Power3.easeInOut"
                     data-splitin="chars"
                     data-splitout="chars"
                     data-elementdelay="0.08"
                     data-endelementdelay="0.08"
                     data-end="8500"
                     data-endspeed="500">
                     Makes You
                  </div>
                  <div class="tp-caption customin customout tp-resizeme slider-title slide3-title2" 
                     data-x="-15" data-hoffset="0"
                     data-y="180" data-voffset="0"
                     data-customin="x:0;y:0;z:0;rotationX:0;rotationY:0;rotationZ:0;scaleX:0;scaleY:0;skewX:0;skewY:0;opacity:0;transformPerspective:600;transformOrigin:50% 50%;"
                     data-customout="x:0;y:0;z:0;rotationX:0;rotationY:0;rotationZ:0;scaleX:0;scaleY:0;skewX:0;skewY:0;opacity:0;transformPerspective:600;transformOrigin:50% 50%;"
                     data-speed="1500"
                     data-start="2500"
                     data-easing="Power3.easeInOut"
                     data-splitin="chars"
                     data-splitout="chars"
                     data-elementdelay="0.08"
                     data-endelementdelay="0.08"
                     data-end="8500"
                     data-endspeed="500">
                     Confident
                  </div>
                  <div class="tp-caption lfl ltl slider-list slide3-caption1" 
                     data-x="0" 
                     data-y="260"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="4000" 
                     data-end="8000" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                     Complete HTML5 Pages for Business
                  </div>
                  <div class="tp-caption lfl ltl slider-list slide3-caption2" 
                     data-x="0" 
                     data-y="309"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="4500" 
                     data-end="8250" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                     Powered by Revolution Slider ($15 value)
                  </div>
                  <div class="tp-caption lfl ltl slider-list slide3-caption3" 
                     data-x="0" 
                     data-y="359"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="5000" 
                     data-end="8500" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                     Responsive and Retina display ready
                  </div>
               </li>

               <li data-transition="fade" data-masterspeed="1000">
                  <img src="img/slideshow/slide1-bg.png" alt="slider background">
                  
                  <!-- caption slider 1 -->
                  <div class="tp-caption lfb ltb slide1-image" 
                     data-x="540" 
                     data-y="55"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="1750" 
                     data-end="8500" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                     <img src="img/slideshow/slide1-img1.png" alt="slider image">
                  </div>
                  <div class="tp-caption customin customout tp-resizeme slider-title slide1-title" 
                     data-x="-17" data-hoffset="0"
                     data-y="218" data-voffset="0"
                     data-customin="x:0;y:0;z:0;rotationX:0;rotationY:0;rotationZ:0;scaleX:0;scaleY:0;skewX:0;skewY:0;opacity:0;transformPerspective:600;transformOrigin:50% 50%;"
                     data-customout="x:0;y:0;z:0;rotationX:0;rotationY:0;rotationZ:0;scaleX:0;scaleY:0;skewX:0;skewY:0;opacity:0;transformPerspective:600;transformOrigin:50% 50%;"
                     data-speed="1500"
                     data-start="2000"
                     data-easing="Power3.easeInOut"
                     data-splitin="chars"
                     data-splitout="chars"
                     data-elementdelay="0.08"
                     data-endelementdelay="0.08"
                     data-end="8500"
                     data-endspeed="500">
                     Get Ready For
                  </div>
                  <div class="tp-caption lfl ltb slider-caption slide1-caption" 
                     data-x="10" 
                     data-y="268"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="3500" 
                     data-end="8500" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                     Great Solution for Online Business
                  </div>
                  <div class="tp-caption lft ltt slider-separator" 
                     data-x="0" 
                     data-y="160"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="4000" 
                     data-end="8000" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                  </div>
                  <div class="tp-caption lfb ltb slider-separator" 
                     data-x="0" 
                     data-y="305"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="4000" 
                     data-end="8000" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                  </div>
               </li>
            </ul>
         </div>
      </div>
      <!-- slideshow end here -->

      <!-- container -->
      <section class="container">
         <div class="row">
            <div class="large-8 large-push-2 column text-center me-animate" data-animate="fadeIn">
               <h3 class="heading-light">Why You Must Choose This Template</h3>
               <p class="gap" data-gap-bottom="38">Inventore veritatis etarom quasi architecto beatae vitae dicta sunt explicabo nemo enimata Error sit voluptatem accusantium doloremque laudantium  quasi architecto</p>
            </div>
         </div>

         <div class="row">
            <div class="large-4 medium-4 column">
               <div class="me-box-icon me-animate" data-animate="fadeIn">
                  <div class="icon-container">
                     <div class="icon-shape radius">  
                        <i class="linea-basic_gear"></i>
                     </div>
                  </div>
                  <div class="text-container">
                     <h4 class="title-box">Easy to Custom</h4>
                     <p>Error sit voluptatem accusantium doloremque laudantium, totam rem aperiam eaque.</p>
                  </div>
               </div>
            </div>
            <div class="large-4 medium-4 column">
               <div class="me-box-icon me-animate" data-animate="fadeIn">
                  <div class="icon-container">
                     <div class="icon-shape radius">  
                        <i class="linea-software_layers2"></i>
                     </div>
                  </div>
                  <div class="text-container">
                     <h4 class="title-box">Smart and Flexible</h4>
                     <p>Error sit voluptatem accusantium doloremque laudantium, totam rem aperiam eaque.</p>
                  </div>
               </div>
            </div>
            <div class="large-4 medium-4 column">
               <div class="me-box-icon me-animate" data-animate="fadeIn">
                  <div class="icon-container">
                     <div class="icon-shape radius">  
                        <i class="linea-basic_smartphone"></i>
                     </div>
                  </div>
                  <div class="text-container">
                     <h4 class="title-box">Responsive Design</h4>
                     <p>Error sit voluptatem accusantium doloremque laudantium, totam rem aperiam eaque.</p>
                  </div>
               </div>
            </div>
            <div class="large-4 medium-4 column">
               <div class="me-box-icon me-animate" data-animate="fadeIn">
                  <div class="icon-container">
                     <div class="icon-shape radius">  
                        <i class="linea-arrows_circle_check"></i>
                     </div>
                  </div>
                  <div class="text-container">
                     <h4 class="title-box">HTML5 Valid</h4>
                     <p>Error sit voluptatem accusantium doloremque laudantium, totam rem aperiam eaque.</p>
                  </div>
               </div>
            </div>
            <div class="large-4 medium-4 column">
               <div class="me-box-icon me-animate" data-animate="fadeIn">
                  <div class="icon-container">
                     <div class="icon-shape radius">  
                        <i class="linea-basic_watch"></i>
                     </div>
                  </div>
                  <div class="text-container">
                     <h4 class="title-box">Save Times</h4>
                     <p>Error sit voluptatem accusantium doloremque laudantium, totam rem aperiam eaque.</p>
                  </div>
               </div>
            </div>
            <div class="large-4 medium-4 column">
               <div class="me-box-icon me-animate" data-animate="fadeIn">
                  <div class="icon-container">
                     <div class="icon-shape radius">  
                        <i class="linea-basic_book_pencil"></i>
                     </div>
                  </div>
                  <div class="text-container">
                     <h4 class="title-box">Latest Tech</h4>
                     <p>Error sit voluptatem accusantium doloremque laudantium, totam rem aperiam eaque.</p>
                  </div>
               </div>
            </div>
         </div>
      </section>
      <!-- container end here -->

      <!-- container -->
      <section class="container container-gray container-border no-padding-bottom container-image-left">
         <div class="row">
            <div class="large-5 medium-6 medium-portrait-12 column me-animate" data-animate="fadeInUp">
               <img class="image-left-margin" src="img/ipad-air.png" alt="image content">
            </div>
            <div class="large-7 medium-6 medium-portrait-12 column me-animate" data-animate="fadeIn">
               <h3 class="heading-light">Powerful Packed Designs Clean <br>and Professional Theme</h3>
               <p>Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur illum qui dolorem eum fugiat quo voluptas nulla pariatur.</p>
               <div class="row">
                  <div class="large-6 medium-6 column">
                     <ul class="me-list list-circle-check">
                        <li>Diffrent Website Layouts</li>
                        <li>Exclusive Slideshows</li>
                        <li>Multiple Header Options</li>
                        <li>Boxed &amp; Full Layouts</li>
                        <li>Clean Code &amp; Easy to Use</li>
                     </ul>
                  </div>
                  <div class="large-6 medium-6 column">
                     <ul class="me-list list-circle-check">
                        <li>Contact Form Ready</li>
                        <li>Layered PSD Files</li>
                        <li>Well Structured Pages</li>
                        <li>Mobile Friendly Theme</li>
                        <li>Cross Browsere Check</li>
                     </ul>
                  </div>
               </div>
               <a class="button half-block radius" href="#">View all Features
                  <i class="fa fa-angle-double-right"></i>
               </a>
            </div>
         </div>
      </section>
      <!-- container end here -->

      <!-- container -->
      <section class="container parallax3 parallax">
         <div class="parallax-overlay"></div>
         <div class="row">
            <div class="large-6 medium-6 column  me-animate" data-animate="fadeIn">
               <h2 class="gap text-white" data-gap-bottom="10">Professional HTML</h2>
               <h3 class="heading-light text-white">for Multipurpose Business.</h3>
               <p class="text-white">Excepteur sint occaecat cupidatat non proident sunt in culpa qui officia deserunt mollit libero tempore optio cumquai nihil</p>
               
               <a class="button button-border white half-block radius" href="#">Buy this Template
                  <i class="fa fa-angle-double-right"></i>
               </a>
            </div>
         </div>
      </section>
      <!-- container end here -->

      <!-- container -->
      <section class="container">
         <div class="row">
            <div class="large-3 medium-3 medium-portrait-12 column me-animate" data-animate="fadeIn">   
               <h3 class="heading-light">We Have Great Experience</h3>
               <p>Namsint libero tempore optio cumquai nihil ratione dolorem eum fugiat quo voluptas nulla pariatur consequatur</p>
            </div>
            <div class="large-3 medium-3 medium-portrait-4 column">
               <div class="box-counter2 me-animate" data-animate="fadeIn">
                  <span class="count-me timer" data-to="265" data-speed="2500">265</span>
                  <h5>Finished Project</h5>
                  <div class="icon-counter">
                     <i class="linea-basic_case"></i>
                  </div>
               </div>
            </div>
            <div class="large-3 medium-3 medium-portrait-4 column">
               <div class="box-counter2 me-animate" data-animate="fadeIn" data-animate-delay="250">
                  <span class="count-me timer" data-to="14" data-speed="2500">14</span>
                  <h5>Awards Won</h5>
                  <div class="icon-counter">
                     <i class="linea-basic_cup"></i>
                  </div>
               </div>
            </div>
            <div class="large-3 medium-3 medium-portrait-4 column">
               <div class="box-counter2 me-animate" data-animate="fadeIn" data-animate-delay="500">
                  <span class="count-me timer" data-to="520" data-speed="2500">520</span>
                  <h5>Happy Customers</h5>
                  <div class="icon-counter">
                     <i class="linea-basic_elaboration_message_happy"></i>
                  </div>
               </div>
            </div>
         </div>
      </section>
      <!-- container end here -->
      
      <!-- footer -->
      <footer id="me-footer">
         <div class="row">
            <div class="large-3 medium-3 column">
               <div class="logo-footer">
                  <a href="index.html">
                     <img class="retina" src="img/logo-footer.png" alt="Logo">
                  </a>
               </div>
            </div>
            <div class="large-3 medium-3 column">
               <h5 class="heading-footer">Come Visit Us</h5>
               <p>
                  Enix Headquarter <br>
                  765 Roosevelt Trail, Suite 301 <br>
                  Windham, Maine 04062
               </p>
            </div>
            <div class="large-3 medium-3 column">
               <h5 class="heading-footer">Talk With Us</h5>
               <ul>
                  <li><strong>E</strong> <a href="#"><EMAIL></a></li>
                  <li><strong>T</strong> +1 207 892 1255</li> 
                  <li><strong>M</strong> +1 207 513 2871</li>
               </ul>
            </div>
            <div class="large-3 medium-3 column">
               <h5 class="heading-footer">Get Social</h5>
               <ul>
                  <li><a href="#">Facebook</a></li>
                  <li><a href="#">Twitter</a></li>
                  <li><a href="#">Instagram</a></li>
               </ul>
            </div>
         </div>
         <div class="row footer-information">
            <div class="large-4 medium-4 column">
               <div class="copyright">
                  <span>&copy; All rights reserved 2014. <span>Enix Inc.</span></span>
               </div>
            </div>
            <div class="large-8 medium-8 column text-right">
               <ul class="inline-list footer-menu">
                  <li><a href="#">About Us</a></li>
                  <li><a href="#">Privacy</a></li>
                  <li><a href="#">Term of Use</a></li>
                  <li><a href="#">Pricing</a></li>
                  <li><a href="#">DMCA Notice</a></li>
                  <li><a class="active" href="#">Contact</a></li>
               </ul>
            </div>
         </div>
      </footer>
      <!-- footer end here -->

   </div>
   <!-- main-container end here -->

   <!-- javascript -->
   <script src="js/jquery.min.js"></script>
   <script src="js/foundstrap.js"></script>
   <script src="js/jquery.sscr.js"></script>
   <script src="js/jquery.waypoints.min.js"></script>
   <script src="js/owl.carousel.min.js"></script>
   <script src="js/jquery.scrollUp.js"></script>
   <script src="js/jquery.retina.js"></script>

   <!-- javascript plugin -->
   <script src="js/rs-plugin/js/jquery.themepunch.tools.min.js"></script>
   <script src="js/rs-plugin/js/jquery.themepunch.revolution.min.js"></script>
   <script src="js/jquery.countTo.js"></script>

   <!-- javascript core -->
   <script src="js/theme-script.js"></script>
   <script src="js/jquery.cookie.js"></script>
   <script src="js/theme-switcher.js"></script>

   <script type="text/javascript">
      jQuery(document).ready(function($) { 
         Foundstrap.theme();

         // revolution slider configuration here
         $('.slideshow').revolution({
            delay:8000,
            startwidth:1080,
            startheight:520,
            hideThumbs:1,
            navigationType:"none",                  // bullet, thumb, none
            navigationArrows:"solo",                // nexttobullets, solo (old name verticalcentered), none
            navigationStyle:"square",               // round,square,navbar,round-old,square-old,navbar-old, or any from the list in the docu (choose between 50+ different item), custom
            navigationHAlign:"center",              // Vertical Align top,center,bottom
            navigationVAlign:"bottom",              // Horizontal Align left,center,right
            navigationHOffset:0,
            navigationVOffset:0,
            soloArrowLeftHalign:"left",
            soloArrowLeftValign:"center",
            soloArrowLeftHOffset:25,
            soloArrowLeftVOffset:0,
            soloArrowRightHalign:"right",
            soloArrowRightValign:"center",
            soloArrowRightHOffset:25,
            soloArrowRightVOffset:0,
            touchenabled:"on",                      // Enable Swipe Function : on/off
            onHoverStop:"on",                       // Stop Banner Timet at Hover on Slide on/off
            stopAtSlide:-1,                         // Stop Timer if Slide "x" has been Reached. If stopAfterLoops set to 0, then it stops already in the first Loop at slide X which defined. -1 means do not stop at any slide. stopAfterLoops has no sinn in this case.
            stopAfterLoops:-1,                      // Stop Timer if All slides has been played "x" times. IT will stop at THe slide which is defined via stopAtSlide:x, if set to -1 slide never stop automatic
            hideCaptionAtLimit:0,                   // It Defines if a caption should be shown under a Screen Resolution ( Basod on The Width of Browser)
            hideAllCaptionAtLilmit:0,               // Hide all The Captions if Width of Browser is less then this value
            hideSliderAtLimit:0,                    // Hide the whole slider, and stop also functions if Width of Browser is less than this value
            shadow:0,                               // 0 = no Shadow, 1,2,3 = 3 Different Art of Shadows  (No Shadow in Fullwidth Version !)
            fullWidth:"off",                        // Turns On or Off the Fullwidth Image Centering in FullWidth Modus
            fullScreen:"off"
         });
      });
   </script>
</body>
</html>