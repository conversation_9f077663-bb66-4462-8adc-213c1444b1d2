/*-----------------------------------------------------------------

Template Name: Gratech - IT Service PHP Template
Author:  Gramentheme
Author URI: https://themeforest.net/user/gramentheme/portfolio
Version: 1.0.0
Description: Gratech - IT Service PHP Template

-------------------------------------------------------------------
CSS TABLE OF CONTENTS
-------------------------------------------------------------------

01. abstracts
    1.01 --> mixins
    1.02 --> variable

02. base
    2.01 --> typography
    2.02 --> animation
    2.03 --> responsive

03. components
    3.01 --> buttons
    3.02 --> progress

04. layout
    4.01 --> header
    4.02 --> banner
    4.03 --> section
    4.04 --> footer

------------------------------------------------------------------*/
@import url("https://fonts.googleapis.com/css2?family=Kumbh+Sans:wght@100;200;300;400;500;600;700;800;900&display=swap");
:root {
  --kumbh: "Kumbh Sans", sans-serif;
  --primary-color: #3c72fc;
  --primary10: rgba(60, 114, 252, 0.1);
  --gradient-bg: linear-gradient(90deg, #3c72fc -10.59%, #00060c 300.59%);
  --secondary-color: #0f0d1d;
  --main-bg: #ffffff;
  --sub-bg: #f3f7fb;
  --heading-color: #0f0d1d;
  --paragraph: #585858;
  --span: #585858;
  --border: #e3e3e3;
  --white: #ffffff;
  --black: #000000;
  --border-1px: 1px solid #eaecf0;
  --border-2px: 2px solid #eaecf0;
  --transition: all 0.3s ease-in-out;
  --shadow: 0px 4px 25px 0px #0000000f;
  --black-2: #000000;
  --blue: #007bff;
}

:root[data-theme="dark"] {
  --primary-color: #3c72fc;
  --primary10: rgba(60, 114, 252, 0.1);
  --gradient-bg: linear-gradient(90deg, #3c72fc -10.59%, #00060c 300.59%);
  --secondary-color: #0f0d1d;
  --main-bg: #151327;
  --sub-bg: #16142c;
  --heading-color: #fff;
  --paragraph: rgba(255, 255, 255, 0.8);
  --span: rgba(255, 255, 255, 0.8);
  --border: #e3e3e3;
  --white: #ffffff;
  --black: #000000;
}

:root[data-theme="dark"] .light-area {
  --primary-color: #3c72fc;
  --primary10: rgba(60, 114, 252, 0.1);
  --gradient-bg: linear-gradient(90deg, #3c72fc -10.59%, #00060c 300.59%);
  --secondary-color: #0f0d1d;
  --main-bg: #ffffff;
  --sub-bg: #f3f7fb;
  --heading-color: #0f0d1d;
  --paragraph: #585858;
  --span: #585858;
  --border: #e3e3e3;
  --white: #ffffff;
  --black: #000000;
}

* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  outline: none;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--kumbh);
  color: var(--paragraph);
  background-color: var(--main-bg);
  line-height: 28px;
  font-weight: 400;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
  color: var(--heading-color);
  font-family: var(--kumbh);
}

h1 {
  font-size: 90px;
  font-weight: 700;
  line-height: 100px;
}

h2 {
  font-size: 40px;
  font-weight: 700;
  line-height: 48px;
}
@media (max-width: 767px) {
  h2 {
    font-size: 30px;
    line-height: 40px;
  }
}

h3 {
  font-size: 24px;
  line-height: 32px;
  font-weight: 600;
}

h4 {
  font-size: 20px;
  line-height: 32px;
  font-weight: 700;
}

h5 {
  font-size: 16px;
  font-weight: 600;
  line-height: 28px;
}

h6 {
  font-size: 14px;
  font-weight: 600;
}

p {
  margin: 0;
  padding: 0;
  line-height: 28px;
  font-size: 16px;
}

span {
  display: inline-block;
  color: var(--span);
}

a {
  text-decoration: none;
  display: inline-block;
  color: var(--heading-color);
  transition: var(--transition);
}

a:hover {
  color: var(--heading-color);
}

ul {
  margin: 0;
  padding: 0;
  text-decoration: none;
}

li {
  list-style: none;
}

button {
  border: none;
  background-color: transparent;
}

@media (max-width: 991px) {
  br {
    display: none;
  }
}

::placeholder {
  color: var(--span);
}

::selection {
  color: var(--white);
  background-color: var(--primary-color);
}

.mt-5 {
  margin-top: 5px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-25 {
  margin-top: 25px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-35 {
  margin-top: 35px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-45 {
  margin-top: 45px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-55 {
  margin-top: 55px;
}

.mt-60 {
  margin-top: 60px;
}

.mt-65 {
  margin-top: 65px;
}

.mt-70 {
  margin-top: 70px;
}

.mt-75 {
  margin-top: 75px;
}

.mt-80 {
  margin-top: 80px;
}

.mt-85 {
  margin-top: 85px;
}

.mt-90 {
  margin-top: 90px;
}

.mt-95 {
  margin-top: 95px;
}

.mt-100 {
  margin-top: 100px;
}

.mt-105 {
  margin-top: 105px;
}

.mt-110 {
  margin-top: 110px;
}

.mt-115 {
  margin-top: 115px;
}

.mt-120 {
  margin-top: 120px;
}

.mt-125 {
  margin-top: 125px;
}

.mt-130 {
  margin-top: 130px;
}

.mt-135 {
  margin-top: 135px;
}

.mt-140 {
  margin-top: 140px;
}

.mt-145 {
  margin-top: 145px;
}

.mt-150 {
  margin-top: 150px;
}

.mt-155 {
  margin-top: 155px;
}

.mt-160 {
  margin-top: 160px;
}

.mt-165 {
  margin-top: 165px;
}

.mt-170 {
  margin-top: 170px;
}

.mt-175 {
  margin-top: 175px;
}

.mt-180 {
  margin-top: 180px;
}

.mt-185 {
  margin-top: 185px;
}

.mt-190 {
  margin-top: 190px;
}

.mt-195 {
  margin-top: 195px;
}

.mt-200 {
  margin-top: 200px;
}

/*-- Margin Bottom --*/
.mb-5 {
  margin-bottom: 5px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-35 {
  margin-bottom: 35px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-45 {
  margin-bottom: 45px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-55 {
  margin-bottom: 55px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mb-65 {
  margin-bottom: 65px;
}

.mb-70 {
  margin-bottom: 70px;
}

.mb-75 {
  margin-bottom: 75px;
}

.mb-80 {
  margin-bottom: 80px;
}

.mb-85 {
  margin-bottom: 85px;
}

.mb-90 {
  margin-bottom: 90px;
}

.mb-95 {
  margin-bottom: 95px;
}

.mb-100 {
  margin-bottom: 100px;
}

.mb-105 {
  margin-bottom: 105px;
}

.mb-110 {
  margin-bottom: 110px;
}

.mb-115 {
  margin-bottom: 115px;
}

.mb-120 {
  margin-bottom: 120px;
}

.mb-125 {
  margin-bottom: 125px;
}

.mb-130 {
  margin-bottom: 130px;
}

.mb-135 {
  margin-bottom: 135px;
}

.mb-140 {
  margin-bottom: 140px;
}

.mb-145 {
  margin-bottom: 145px;
}

.mb-150 {
  margin-bottom: 150px;
}

.mb-155 {
  margin-bottom: 155px;
}

.mb-160 {
  margin-bottom: 160px;
}

.mb-165 {
  margin-bottom: 165px;
}

.mb-170 {
  margin-bottom: 170px;
}

.mb-175 {
  margin-bottom: 175px;
}

.mb-180 {
  margin-bottom: 180px;
}

.mb-185 {
  margin-bottom: 185px;
}

.mb-190 {
  margin-bottom: 190px;
}

.mb-195 {
  margin-bottom: 195px;
}

.mb-200 {
  margin-bottom: 200px;
}

.ml-5 {
  margin-left: 5px;
}

.ml-10 {
  margin-left: 10px;
}

.ml-15 {
  margin-left: 15px;
}

.ml-20 {
  margin-left: 20px;
}

.ml-25 {
  margin-left: 25px;
}

.ml-30 {
  margin-left: 30px;
}

.ml-35 {
  margin-left: 35px;
}

.ml-40 {
  margin-left: 40px;
}

.ml-45 {
  margin-left: 45px;
}

.ml-50 {
  margin-left: 50px;
}

.ml-55 {
  margin-left: 55px;
}

.ml-60 {
  margin-left: 60px;
}

.ml-65 {
  margin-left: 65px;
}

.ml-70 {
  margin-left: 70px;
}

.ml-75 {
  margin-left: 75px;
}

.ml-80 {
  margin-left: 80px;
}

.ml-85 {
  margin-left: 85px;
}

.ml-90 {
  margin-left: 90px;
}

.ml-95 {
  margin-left: 95px;
}

.ml-100 {
  margin-left: 100px;
}

.ml-105 {
  margin-left: 105px;
}

.ml-110 {
  margin-left: 110px;
}

.ml-115 {
  margin-left: 115px;
}

.ml-120 {
  margin-left: 120px;
}

.ml-125 {
  margin-left: 125px;
}

.ml-130 {
  margin-left: 130px;
}

.ml-135 {
  margin-left: 135px;
}

.ml-140 {
  margin-left: 140px;
}

.ml-145 {
  margin-left: 145px;
}

.ml-150 {
  margin-left: 150px;
}

.ml-155 {
  margin-left: 155px;
}

.ml-160 {
  margin-left: 160px;
}

.ml-165 {
  margin-left: 165px;
}

.ml-170 {
  margin-left: 170px;
}

.ml-175 {
  margin-left: 175px;
}

.ml-180 {
  margin-left: 180px;
}

.ml-185 {
  margin-left: 185px;
}

.ml-190 {
  margin-left: 190px;
}

.ml-195 {
  margin-left: 195px;
}

.ml-200 {
  margin-left: 200px;
}

.mr-5 {
  margin-right: 5px;
}

.mr-10 {
  margin-right: 10px;
}

.mr-15 {
  margin-right: 15px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-25 {
  margin-right: 25px;
}

.mr-30 {
  margin-right: 30px;
}

.mr-35 {
  margin-right: 35px;
}

.mr-40 {
  margin-right: 40px;
}

.mr-45 {
  margin-right: 45px;
}

.mr-50 {
  margin-right: 50px;
}

.mr-55 {
  margin-right: 55px;
}

.mr-60 {
  margin-right: 60px;
}

.mr-65 {
  margin-right: 65px;
}

.mr-70 {
  margin-right: 70px;
}

.mr-75 {
  margin-right: 75px;
}

.mr-80 {
  margin-right: 80px;
}

.mr-85 {
  margin-right: 85px;
}

.mr-90 {
  margin-right: 90px;
}

.mr-95 {
  margin-right: 95px;
}

.mr-100 {
  margin-right: 100px;
}

.mr-105 {
  margin-right: 105px;
}

.mr-110 {
  margin-right: 110px;
}

.mr-115 {
  margin-right: 115px;
}

.mr-120 {
  margin-right: 120px;
}

.mr-125 {
  margin-right: 125px;
}

.mr-130 {
  margin-right: 130px;
}

.mr-135 {
  margin-right: 135px;
}

.mr-140 {
  margin-right: 140px;
}

.mr-145 {
  margin-right: 145px;
}

.mr-150 {
  margin-right: 150px;
}

.mr-155 {
  margin-right: 155px;
}

.mr-160 {
  margin-right: 160px;
}

.mr-165 {
  margin-right: 165px;
}

.mr-170 {
  margin-right: 170px;
}

.mr-175 {
  margin-right: 175px;
}

.mr-180 {
  margin-right: 180px;
}

.mr-185 {
  margin-right: 185px;
}

.mr-190 {
  margin-right: 190px;
}

.mr-195 {
  margin-right: 195px;
}

.mr-200 {
  margin-right: 200px;
}

.pt-5 {
  padding-top: 5px;
}

.pt-10 {
  padding-top: 10px;
}

.pt-15 {
  padding-top: 15px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-25 {
  padding-top: 25px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-35 {
  padding-top: 35px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-45 {
  padding-top: 45px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-55 {
  padding-top: 55px;
}

.pt-60 {
  padding-top: 60px;
}

.pt-65 {
  padding-top: 65px;
}

.pt-70 {
  padding-top: 70px;
}

.pt-75 {
  padding-top: 75px;
}

.pt-80 {
  padding-top: 80px;
}

.pt-85 {
  padding-top: 85px;
}

.pt-90 {
  padding-top: 90px;
}

.pt-95 {
  padding-top: 95px;
}

.pt-100 {
  padding-top: 100px;
}

.pt-105 {
  padding-top: 105px;
}

.pt-110 {
  padding-top: 110px;
}

.pt-115 {
  padding-top: 115px;
}

.pt-120 {
  padding-top: 120px;
}

.pt-125 {
  padding-top: 125px;
}

.pt-130 {
  padding-top: 130px;
}

.pt-135 {
  padding-top: 135px;
}

.pt-140 {
  padding-top: 140px;
}

.pt-145 {
  padding-top: 145px;
}

.pt-150 {
  padding-top: 150px;
}

.pt-155 {
  padding-top: 155px;
}

.pt-160 {
  padding-top: 160px;
}

.pt-165 {
  padding-top: 165px;
}

.pt-170 {
  padding-top: 170px;
}

.pt-175 {
  padding-top: 175px;
}

.pt-180 {
  padding-top: 180px;
}

.pt-185 {
  padding-top: 185px;
}

.pt-190 {
  padding-top: 190px;
}

.pt-195 {
  padding-top: 195px;
}

.pt-200 {
  padding-top: 200px;
}

.pb-5 {
  padding-bottom: 5px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-25 {
  padding-bottom: 25px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pb-35 {
  padding-bottom: 35px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-45 {
  padding-bottom: 45px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-55 {
  padding-bottom: 55px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pb-65 {
  padding-bottom: 65px;
}

.pb-70 {
  padding-bottom: 70px;
}

.pb-75 {
  padding-bottom: 75px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pb-85 {
  padding-bottom: 85px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pb-95 {
  padding-bottom: 95px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pb-105 {
  padding-bottom: 105px;
}

.pb-110 {
  padding-bottom: 110px;
}

.pb-115 {
  padding-bottom: 115px;
}

.pb-120 {
  padding-bottom: 120px;
}

.pb-125 {
  padding-bottom: 125px;
}

.pb-130 {
  padding-bottom: 130px;
}

.pb-135 {
  padding-bottom: 135px;
}

.pb-140 {
  padding-bottom: 140px;
}

.pb-145 {
  padding-bottom: 145px;
}

.pb-150 {
  padding-bottom: 150px;
}

.pb-155 {
  padding-bottom: 155px;
}

.pb-160 {
  padding-bottom: 160px;
}

.pb-165 {
  padding-bottom: 165px;
}

.pb-170 {
  padding-bottom: 170px;
}

.pb-175 {
  padding-bottom: 175px;
}

.pb-180 {
  padding-bottom: 180px;
}

.pb-185 {
  padding-bottom: 185px;
}

.pb-190 {
  padding-bottom: 190px;
}

.pb-195 {
  padding-bottom: 195px;
}

.pb-200 {
  padding-bottom: 200px;
}

.pl-5 {
  padding-left: 5px;
}

.pl-10 {
  padding-left: 10px;
}

.pl-15 {
  padding-left: 15px;
}

.pl-20 {
  padding-left: 20px;
}

.pl-25 {
  padding-left: 25px;
}

.pl-30 {
  padding-left: 30px;
}

.pl-35 {
  padding-left: 35px;
}

.pl-40 {
  padding-left: 40px;
}

.pl-45 {
  padding-left: 45px;
}

.pl-50 {
  padding-left: 50px;
}

.pl-55 {
  padding-left: 55px;
}

.pl-60 {
  padding-left: 60px;
}

.pl-65 {
  padding-left: 65px;
}

.pl-70 {
  padding-left: 70px;
}

.pl-75 {
  padding-left: 75px;
}

.pl-80 {
  padding-left: 80px;
}

.pl-85 {
  padding-left: 85px;
}

.pl-90 {
  padding-left: 90px;
}

.pl-95 {
  padding-left: 95px;
}

.pl-100 {
  padding-left: 100px;
}

.pl-105 {
  padding-left: 105px;
}

.pl-110 {
  padding-left: 110px;
}

.pl-115 {
  padding-left: 115px;
}

.pl-120 {
  padding-left: 120px;
}

.pl-125 {
  padding-left: 125px;
}

.pl-130 {
  padding-left: 130px;
}

.pl-135 {
  padding-left: 135px;
}

.pl-140 {
  padding-left: 140px;
}

.pl-145 {
  padding-left: 145px;
}

.pl-150 {
  padding-left: 150px;
}

.pl-155 {
  padding-left: 155px;
}

.pl-160 {
  padding-left: 160px;
}

.pl-165 {
  padding-left: 165px;
}

.pl-170 {
  padding-left: 170px;
}

.pl-175 {
  padding-left: 175px;
}

.pl-180 {
  padding-left: 180px;
}

.pl-185 {
  padding-left: 185px;
}

.pl-190 {
  padding-left: 190px;
}

.pl-195 {
  padding-left: 195px;
}

.pl-200 {
  padding-left: 200px;
}

.pr-5 {
  padding-right: 5px;
}

.pr-10 {
  padding-right: 10px;
}

.pr-15 {
  padding-right: 15px;
}

.pr-20 {
  padding-right: 20px;
}

.pr-25 {
  padding-right: 25px;
}

.pr-30 {
  padding-right: 30px;
}

.pr-35 {
  padding-right: 35px;
}

.pr-40 {
  padding-right: 40px;
}

.pr-45 {
  padding-right: 45px;
}

.pr-50 {
  padding-right: 50px;
}

.pr-55 {
  padding-right: 55px;
}

.pr-60 {
  padding-right: 60px;
}

.pr-65 {
  padding-right: 65px;
}

.pr-70 {
  padding-right: 70px;
}

.pr-75 {
  padding-right: 75px;
}

.pr-80 {
  padding-right: 80px;
}

.pr-85 {
  padding-right: 85px;
}

.pr-90 {
  padding-right: 90px;
}

.pr-95 {
  padding-right: 95px;
}

.pr-100 {
  padding-right: 100px;
}

.pr-105 {
  padding-right: 105px;
}

.pr-110 {
  padding-right: 110px;
}

.pr-115 {
  padding-right: 115px;
}

.pr-120 {
  padding-right: 120px;
}

.pr-125 {
  padding-right: 125px;
}

.pr-130 {
  padding-right: 130px;
}

.pr-135 {
  padding-right: 135px;
}

.pr-140 {
  padding-right: 140px;
}

.pr-145 {
  padding-right: 145px;
}

.pr-150 {
  padding-right: 150px;
}

.pr-155 {
  padding-right: 155px;
}

.pr-160 {
  padding-right: 160px;
}

.pr-165 {
  padding-right: 165px;
}

.pr-170 {
  padding-right: 170px;
}

.pr-175 {
  padding-right: 175px;
}

.pr-180 {
  padding-right: 180px;
}

.pr-185 {
  padding-right: 185px;
}

.pr-190 {
  padding-right: 190px;
}

.pr-195 {
  padding-right: 195px;
}

.pr-200 {
  padding-right: 200px;
}

@media (max-width: 767px) {
  .pt-120 {
    padding-top: 60px;
  }
  .pb-120 {
    padding-bottom: 60px;
  }
  .mt-120 {
    margin-top: 60px;
  }
  .mb-120 {
    margin-bottom: 60px;
  }
  .pt-100 {
    padding-top: 50px;
  }
  .pb-100 {
    padding-bottom: 50px;
  }
  .mt-100 {
    margin-top: 50px;
  }
  .mb-100 {
    margin-bottom: 50px;
  }
  .pt-60 {
    padding-top: 30px;
  }
  .pb-60 {
    padding-bottom: 30px;
  }
  .mt-60 {
    margin-top: 30px;
  }
  .mb-60 {
    margin-bottom: 30px;
  }
}
.bor {
  border: 1px solid var(--border);
}

.bor-top {
  border-top: 1px solid var(--border);
}

.bor-left {
  border-left: 1px solid var(--border);
}

.bor-bottom {
  border-bottom: 1px solid var(--border);
}

.bor-right {
  border-right: 1px solid var(--border);
}

.border-none {
  border: none !important;
}

.text-justify {
  text-align: justify;
}

.image img {
  width: 100%;
}

.primary-color {
  color: var(--primary-color) !important;
}

.primary-hover:hover {
  color: var(--primary-color) !important;
}

.primary-bg {
  background-color: var(--primary-color) !important;
}

.gradient-bg {
  background: var(--gradient-bg) !important;
}

.secondary-color {
  color: var(--secondary-color);
}

.secondary-bg {
  background-color: var(--secondary-color);
}

.sub-bg {
  background-color: var(--sub-bg);
}

.sm-font {
  font-size: 14px !important;
}

.bg-image {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.transition {
  transition: var(--transition);
}

.fw-600 {
  font-weight: 600 !important;
}

.fw-700 {
  font-weight: 700 !important;
}

.overlay,
.banner-video__wrp,
.brand-three-area,
.case__image,
.banner__inner-page,
.header__main .main-menu ul li .sub-menu.menu-image .image {
  position: relative;
}
.overlay::before,
.banner-video__wrp::before,
.brand-three-area::before,
.case__image::before,
.banner__inner-page::before,
.header__main .main-menu ul li .sub-menu.menu-image .image::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.nice-select {
  width: 100%;
  border-radius: 10px;
  height: 50px;
  line-height: 50px;
}
.nice-select:focus {
  border: 1px solid var(--primary-color);
}
.nice-select .list {
  width: 100%;
}

.star i {
  color: var(--primary-color);
}
.star i.disable {
  color: var(--span);
  opacity: 40%;
}

.pegi {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}
.pegi a {
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  background-color: var(--sub-bg);
  color: var(--heading-color);
  font-weight: 700;
  border-radius: 0;
}
.pegi a:hover {
  background-color: var(--primary-color);
  color: var(--white);
}
.pegi a:hover i {
  color: var(--white) !important;
}
.pegi a.active {
  background-color: var(--primary-color);
  color: var(--white);
}

.accordion .accordion-item {
  border: var(--border-1px);
  border-radius: 0px;
  margin-bottom: 10px;
}
.accordion .accordion-item.dark-mode {
  background-color: var(--secondary-color);
}
.accordion .accordion-item h2 button {
  font-size: 15px;
  line-height: 28px;
  font-weight: 700;
  box-shadow: none;
  border-radius: 0 !important;
  padding: 20px 30px;
}
.accordion .accordion-item .accordion-body {
  padding: 20px 30px;
  padding-top: 0;
}
.accordion .accordion-item .accordion-body p {
  color: var(--paragraph);
}
.accordion .accordion-button {
  background-color: transparent;
  color: var(--primary-color);
}
.accordion .accordion-button::after {
  display: none;
}
.accordion .accordion-button::before {
  position: absolute;
  content: "\f068";
  font-family: "Font Awesome 6 Pro";
  font-weight: 700;
  top: 20px;
  right: 25px;
  font-size: 15px;
  width: 30px;
  height: 30px;
  line-height: 30px;
  border-radius: 0px;
  color: var(--white);
  background-color: var(--primary-color);
  text-align: center;
  transition: var(--transition);
}
.accordion .accordion-button.collapsed {
  background-color: transparent;
  color: var(--heading-color);
}
.accordion .accordion-button.collapsed::before {
  content: "+";
  background-color: var(--sub-bg);
  color: var(--primary-color);
}

.search-wrap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  background: rgba(60, 114, 252, 0.9);
}
.search-wrap .search-inner {
  position: relative;
  width: 100%;
  height: 100%;
}
.search-wrap .search-cell {
  position: absolute;
  top: 50%;
  width: 100%;
  transform: translateY(-50%);
}
.search-wrap .search-field-holder {
  width: 50%;
  margin: auto;
  position: relative;
  animation: slideInUp 0.3s;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .search-wrap .search-field-holder {
    width: 70%;
  }
}
@media (max-width: 575px) {
  .search-wrap .search-field-holder {
    width: 80%;
  }
}
.search-wrap .main-search-input {
  width: 100%;
  height: 70px;
  border: 0;
  padding: 0 50px;
  background: transparent;
  font-size: 25px;
  color: var(--white);
  border-bottom: 1px solid var(--white);
  text-align: center;
  letter-spacing: 2px;
}

@media (max-width: 575px) {
  .search-wrap .main-search-input {
    height: 50px;
    padding: 0 0;
    line-height: 50px;
    font-size: 18px;
  }
}
.search-wrap input.form-control,
.search-wrap input.form-control:focus {
  background-color: var(--white);
}

input.main-search-input::placeholder {
  color: var(--white);
  opacity: 1;
  font-size: 25px;
}

@media (max-width: 575px) {
  input.main-search-input::placeholder {
    font-size: 18px;
  }
}
.search-close {
  position: absolute;
  top: 50px;
  right: 50px;
  font-size: 30px;
  color: var(--white);
  cursor: pointer;
}

.pace {
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  z-index: 99999999999999;
  position: fixed;
  margin: auto;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 400px;
  border: 0px;
  height: 1px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.05);
  -webkit-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
}
.pace .pace-progress {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  max-width: 300px;
  position: fixed;
  z-index: 99999999999999;
  display: block;
  position: absolute;
  top: 0;
  right: 100%;
  height: 100%;
  width: 100%;
  background: var(--primary-color);
}

.pace.pace-inactive {
  width: 100vw;
  opacity: 0;
}
.pace.pace-inactive .pace-progress {
  max-width: 100vw;
}

#preloader {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  z-index: 9999999;
  /* Add animation to auto-hide preloader after 10 seconds as fallback */
  animation: hidePreloader 10s forwards;
}
#preloader:after {
  content: "";
  position: fixed;
  left: 0;
  height: 50%;
  width: 100%;
  background: var(--black);
  -webkit-transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
  -o-transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
  transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
  bottom: 0;
}
#preloader:before {
  content: "";
  position: fixed;
  left: 0;
  height: 50%;
  width: 100%;
  background: var(--black);
  -webkit-transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
  -o-transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
  transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
  top: 0;
}

/* iOS-specific preloader styles */
@supports (-webkit-touch-callout: none) {
  #preloader {
    animation: hidePreloader 5s forwards; /* Shorter timeout for iOS */
  }
}

#preloader.isdone {
  visibility: hidden;
  -webkit-transition-delay: 1.5s;
  -o-transition-delay: 1.5s;
  transition-delay: 1.5s;
}
#preloader.isdone:after {
  height: 0;
  -webkit-transition: all 0.7s cubic-bezier(1, 0, 0.55, 1);
  -o-transition: all 0.7s cubic-bezier(1, 0, 0.55, 1);
  transition: all 0.7s cubic-bezier(1, 0, 0.55, 1);
  -webkit-transition-delay: 1s;
  -o-transition-delay: 1s;
  transition-delay: 1s;
}
#preloader.isdone:before {
  height: 0;
  -webkit-transition: all 0.7s cubic-bezier(1, 0, 0.55, 1);
  -o-transition: all 0.7s cubic-bezier(1, 0, 0.55, 1);
  transition: all 0.7s cubic-bezier(1, 0, 0.55, 1);
  -webkit-transition-delay: 1s;
  -o-transition-delay: 1s;
  transition-delay: 1s;
}

.loading {
  position: fixed;
  width: 100%;
  text-align: center;
  left: 50%;
  top: calc(50% - 40px);
  -webkit-transform: translateX(-50%) translateY(-50%);
  -ms-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
  font-weight: 400;
  font-size: 24px;
  text-transform: lowercase;
  letter-spacing: 5px;
  z-index: 9999999999;
}
.loading span {
  -webkit-animation: loading 1.4s infinite alternate;
  animation: loading 1.4s infinite alternate;
}
.loading span:nth-child(1) {
  -webkit-animation-delay: 0s;
  animation-delay: 0s;
}
.loading span:nth-child(2) {
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}
.loading span:nth-child(3) {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}
.loading span:nth-child(4) {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}
.loading span:nth-child(5) {
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}
.loading span:nth-child(6) {
  -webkit-animation-delay: 0.5s;
  animation-delay: 0.5s;
}
.loading span:nth-child(7) {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
}

.loading.isdone {
  top: 50%;
  opacity: 0;
  -webkit-transition: all 0.8s;
  -o-transition: all 0.8s;
  transition: all 0.8s;
  -webkit-transition-delay: 0.5s;
  -o-transition-delay: 0.5s;
  transition-delay: 0.5s;
  visibility: hidden;
}

@-webkit-keyframes loading {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes loading {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

/* Add keyframes for auto-hiding preloader */
@keyframes hidePreloader {
  0%,
  70% {
    visibility: visible;
    pointer-events: auto;
  }
  80% {
    visibility: visible;
    pointer-events: none; /* Allow interaction with page even if preloader is visible */
  }
  100% {
    visibility: hidden;
    pointer-events: none;
  }
}
.mouse-cursor {
  position: fixed;
  left: 0;
  top: 0;
  pointer-events: none;
  border-radius: 50%;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  visibility: hidden;
}

.cursor-inner {
  width: 6px;
  height: 6px;
  z-index: 10000001;
  background-color: var(--primary-color);
  -webkit-transition: width 0.3s ease-in-out, height 0.3s ease-in-out,
    margin 0.3s ease-in-out, opacity 0.3s ease-in-out;
  -o-transition: width 0.3s ease-in-out, height 0.3s ease-in-out,
    margin 0.3s ease-in-out, opacity 0.3s ease-in-out;
  transition: width 0.3s ease-in-out, height 0.3s ease-in-out,
    margin 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

.cursor-inner.cursor-hover {
  margin-left: -35px;
  margin-top: -35px;
  width: 70px;
  height: 70px;
  background-color: var(--primary-color);
  opacity: 0.3;
}

.cursor-outer {
  margin-left: -12px;
  margin-top: -12px;
  width: 30px;
  height: 30px;
  border: 1px solid var(--primary-color);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  z-index: 10000000;
  opacity: 0.5;
  -webkit-transition: all 0.08s ease-out;
  -o-transition: all 0.08s ease-out;
  transition: all 0.08s ease-out;
}

.cursor-outer.cursor-hover {
  opacity: 0;
}

.scroll-up {
  cursor: pointer;
  display: block;
  border-radius: 50px;
  box-shadow: inset 0 0 0 2px var(--border);
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  position: fixed;
  right: 25px;
  bottom: 35px;
  height: 50px;
  width: 50px;
  transition: var(--transition);
}
.scroll-up::after {
  position: absolute;
  font-family: "Font Awesome 6 Pro";
  content: "\f176";
  text-align: center;
  line-height: 50px;
  font-weight: 700;
  font-size: 18px;
  color: var(--primary-color);
  left: 0;
  top: 0;
  height: 50px;
  width: 50px;
  cursor: pointer;
  display: block;
  z-index: 1;
  transition: var(--transition);
}
.scroll-up svg path {
  fill: none;
}
.scroll-up svg.scroll-circle path {
  stroke: var(--primary-color);
  stroke-width: 4px;
  box-sizing: border-box;
  transition: var(--transition);
}
.scroll-up.active-scroll {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.btn-one {
  padding: 15px 25px;
  background: var(--gradient-bg);
  font-weight: 600;
  color: var(--white);
  transition: var(--transition);
  text-transform: capitalize;
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.btn-one i {
  margin-left: 8px;
  transition: var(--transition);
}
@media (max-width: 575px) {
  .btn-one i {
    margin-left: 3px;
    font-size: 12px;
  }
}
@media (max-width: 575px) {
  .btn-one {
    padding: 8px 18px;
    font-size: 14px;
  }
}
.btn-one::after {
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 0;
  content: "";
  background-color: var(--secondary-color);
  z-index: -1;
  transition: var(--transition);
}
.btn-one::before {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50%;
  height: 0;
  content: "";
  background-color: var(--secondary-color);
  z-index: -1;
  transition: var(--transition);
}
.btn-one:hover {
  color: var(--white);
}
.btn-one:hover::before {
  height: 100%;
}
.btn-one:hover::after {
  height: 100%;
}
.btn-one:hover i {
  transform: translate(5px);
}

.read-more-btn {
  text-transform: capitalize;
  font-weight: 600;
  color: var(--paragraph);
}
.read-more-btn i {
  margin-left: 5px;
  transition: var(--transition);
}
.read-more-btn:hover {
  color: var(--primary-color);
}
.read-more-btn:hover i {
  color: var(--primary-color);
  margin-left: 10px;
}

.arry-prev,
.arry-next {
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  color: var(--primary-color);
  transition: var(--transition);
  border: 1px solid var(--primary-color);
  font-size: 20px;
}
.arry-prev:hover,
.arry-next:hover {
  background-color: var(--primary-color);
  color: var(--white);
}
.arry-prev.active,
.active.arry-next {
  background-color: var(--primary-color);
  color: var(--white);
}

.dot .swiper-pagination-bullet,
.dot-light .swiper-pagination-bullet {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  transition: 0.6s;
  background-color: transparent;
  opacity: 1;
  position: relative;
  border: 1px solid transparent;
}
.dot .swiper-pagination-bullet::before,
.dot-light .swiper-pagination-bullet::before {
  position: absolute;
  content: "";
  top: 5px;
  left: 5px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--primary-color);
  transition: 0.6s;
}
.dot .swiper-pagination-bullet.swiper-pagination-bullet-active,
.dot-light .swiper-pagination-bullet.swiper-pagination-bullet-active {
  border: 1px solid var(--primary-color);
}
.dot .swiper-pagination-bullet.swiper-pagination-bullet-active::before,
.dot-light .swiper-pagination-bullet.swiper-pagination-bullet-active::before {
  background-color: var(--primary-color);
}

.dot-light .swiper-pagination-bullet {
  background-color: transparent;
  border: 1px solid transparent;
}
.dot-light .swiper-pagination-bullet::before {
  background-color: var(--white);
}
.dot-light .swiper-pagination-bullet.swiper-pagination-bullet-active {
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.dot-light .swiper-pagination-bullet.swiper-pagination-bullet-active::before {
  background-color: var(--primary-color);
}

.video-btn {
  position: relative;
  text-align: center;
  display: inline-block;
  z-index: 2;
}
.video-btn a {
  position: relative;
  color: var(--white);
  font-size: 20px;
  z-index: 1;
  background: var(--gradient-bg);
  width: 60px;
  height: 60px;
  line-height: 60px;
  border-radius: 50%;
  display: block;
}
.video-btn.video-pulse::after,
.video-btn.video-pulse::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  border: 10px solid var(--primary-color);
  opacity: 0.7;
  left: 0;
  top: 0;
  border-radius: 50%;
  -webkit-animation-duration: 2.5s;
  animation-duration: 2.5s;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  -webkit-animation-name: video-animation;
  animation-name: video-animation;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}
.video-btn.video-pulse::before {
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}

.progress-area .progress {
  background-color: var(--border);
  height: 12px;
  border-radius: 0px;
}
.progress-area .progress.dark-mode {
  background-color: var(--secondary-color);
}
.progress-area .progress .progress-bar {
  background-color: var(--primary-color);
}
.progress__title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.progress__title span {
  color: var(--primary-color);
  font-size: 14px;
  font-weight: 600;
}

.logo {
  display: block;
  width: 196px;
}
.logo img {
  width: 100%;
}

.header-top {
  padding: 16px 0;
  overflow: hidden;
  background-color: var(--heading-color);
}
.header-top .header-top-wrp {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header-top .header-top-wrp .info {
  color: var(--white);
}
.header-top .header-top-wrp .info span {
  color: var(--white);
}
.header-top .header-top-wrp .info a {
  color: var(--white);
  font-size: 13px;
}
/* .header-top .header-top-wrp .info a:hover {
  color: var(--primary-color);
} */
.header-top .header-top-wrp .info i {
  color: var(--primary-color);
  padding-right: 5px;
}
.header-top .header-top-wrp .info li {
  float: left;
  line-height: 0;
}
.header-top .header-top-wrp .link-info {
  position: relative;
  z-index: 2;
}
.header-top .header-top-wrp .link-info li {
  float: left;
  line-height: 0;
}
.header-top .header-top-wrp .link-info li a {
  width: 37px;
  color: var(--white);
  text-align: center;
}
.header-top .header-top-wrp .link-info li a:hover {
  color: var(--primary-color);
}

.header-area {
  position: relative;
}
.header-area::after {
  position: absolute;
  top: 0;
  left: 0;
  width: 30%;
  background: linear-gradient(270deg, #3c72fc 6.32%, #00060c 216.42%);
  height: 100%;
  content: "";
  z-index: -1;
  clip-path: polygon(0 0, 100% 0%, 90% 100%, 0% 100%);
}
@media (max-width: 1199px) {
  .header-area::after {
    width: 25%;
  }
}
@media (max-width: 991px) {
  .header-area::after {
    width: 30%;
  }
}
@media (max-width: 767px) {
  .header-area::after {
    width: 40%;
  }
}
@media (max-width: 575px) {
  .header-area::after {
    width: 50%;
  }
}
@media (max-width: 450px) {
  .header-area::after {
    width: 70%;
  }
}
.header-area.menu-fixed {
  position: fixed;
  width: 100%;
  top: 0;
  background-color: var(--white);
  z-index: 999;
  box-shadow: var(--shadow);
}
.header-area.menu-fixed .mega-menu {
  top: 105px !important;
}
.header__container {
  max-width: 1350px;
  padding: 0 15px;
}
.header__main {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}
.header__main .main-menu ul {
  display: flex;
  align-items: center;
  gap: 35px;
}
.header__main .main-menu ul li {
  position: relative;
}
.header__main .main-menu ul li.has-megamenu {
  position: static;
}
.header__main .main-menu ul li.has-megamenu:hover .sub-menu {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0px);
}
.header__main .main-menu ul li a {
  font-weight: 600;
  padding: 40px 0;
}
.header__main .main-menu ul li a i {
  font-size: 12px;
  transition: var(--transition);
}
.header__main .main-menu ul li .sub-menu {
  position: absolute;
  left: 0;
  top: 105px;
  z-index: 99;
  flex-direction: column;
  gap: 0;
  width: 250px;
  border: var(--border-1px);
  box-shadow: var(--shadow);
  background-color: var(--white);
  transition: var(--transition);
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
}
.header__main .main-menu ul li .sub-menu.mega-menu {
  max-width: 1170px;
  width: 100%;
  left: 50%;
  top: 105px;
  transform: translateX(-50%) translateY(10px);
  padding: 30px;
}
.header__main .main-menu ul li .sub-menu.mega-menu li {
  display: flex;
  align-items: center;
  gap: 20px;
  justify-content: space-around;
}
.header__main .main-menu ul li .sub-menu.menu-image .image {
  position: relative;
}
.header__main .main-menu ul li .sub-menu.menu-image .image::before {
  background-color: rgba(0, 0, 0, 0.5);
}
.header__main .main-menu ul li .sub-menu.menu-image .image h6 {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
}
.header__main .main-menu ul li .sub-menu.menu-image .image .btn__group {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
  margin-top: 20px;
}
.header__main
  .main-menu
  ul
  li
  .sub-menu.menu-image
  .image
  .btn__group
  .btn-one {
  color: var(--white) !important;
}
.header__main .main-menu ul li .sub-menu.menu-image .image:hover .btn__group {
  opacity: 1;
  visibility: visible;
  margin-top: 0;
}
.header__main .main-menu ul li .sub-menu li {
  width: 100%;
}
.header__main .main-menu ul li .sub-menu li:not(:last-child) {
  border-bottom: 1px solid var(--border);
}
.header__main .main-menu ul li .sub-menu li a {
  display: block;
  padding: 10px 20px;
  color: var(--heading-color) !important;
}
.header__main .main-menu ul li .sub-menu li a:hover {
  padding-left: 25px;
  color: var(--primary-color) !important;
}
.header__main .main-menu ul li:hover .sub-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0px);
}
.header__main .main-menu ul li:hover:hover a {
  color: var(--primary-color);
}
.header__main .main-menu ul li:hover:hover i {
  transform: rotate(-180deg);
  color: var(--primary-color);
}
@media (max-width: 991px) {
  .header__main {
    padding: 15px 0;
  }
}
.header__main .bars i {
  font-size: 18px;
  cursor: pointer;
  display: inline-block;
  color: var(--paragraph);
  padding: 10px;
  border-radius: 10px;
  border: var(--border-1px);
}
.header-two-area {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 999;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}
.header-two-area:after {
  display: none;
}
.header-two-area.menu-fixed {
  background: linear-gradient(90deg, #0f0d1d -76.72%, #3c72fc 191.51%);
  border-bottom: none;
}
.header-two-area .header__main .main-menu ul li a {
  color: var(--white);
}
.header-two-area .header__main .bars i {
  color: var(--white);
}
.header-three-area {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 999;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}
.header-three-area:after {
  display: none;
}
.header-three-area.menu-fixed {
  background: linear-gradient(90deg, #0f0d1d -76.72%, #3c72fc 191.51%);
  border-bottom: none;
}
.header-three-area .header__main .main-menu ul li a {
  color: var(--white);
  position: relative;
}
.header-three-area .header__main .main-menu ul li a::after {
  position: absolute;
  content: "";
  background: linear-gradient(
    0.49deg,
    #3c72fc -126.52%,
    rgba(60, 114, 252, 0) 92.35%
  );
  width: 85px;
  height: 100%;
  bottom: 0;
  left: -20px;
  opacity: 0;
  visibility: hidden;
}
.header-three-area .header__main .main-menu ul li a.search-trigger::after {
  display: none;
}
.header-three-area .header__main .main-menu ul li:hover a {
  color: var(--white);
}
.header-three-area .header__main .main-menu ul li:hover a::after {
  opacity: 1;
  visibility: visible;
}
.header-three-area .header__main .main-menu .sub-menu {
  left: -20px;
}
.header-three-area .header__main .main-menu .sub-menu a::after {
  display: none;
}
.header-three-area .header__main .bars i {
  color: var(--white);
}

.header__main .main-menu ul li .sub-menu li .sub-menu {
  inset-inline-start: 100%;
  top: 0;
  visibility: hidden;
  opacity: 0;
}
.header__main .main-menu ul li .sub-menu li:hover > .sub-menu {
  -webkit-transform: translateY(1);
  -moz-transform: translateY(1);
  -ms-transform: translateY(1);
  -o-transform: translateY(1);
  transform: translateY(1);
  visibility: visible;
  opacity: 1;
}

.mean-nav .mega-menu .image {
  padding: 20px;
  border: var(--border-1px);
  border-radius: 8px;
  margin-bottom: 20px;
  position: relative;
}
.mean-nav .mega-menu .image h6 {
  margin-top: 20px;
}
.mean-nav .btn__group {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.mean-nav .btn__group a {
  text-align: center !important;
  width: 100% !important;
}

.sidebar-area {
  position: fixed;
  top: 0;
  right: 0px;
  width: 400px;
  height: 100%;
  background-color: var(--heading-color);
  padding: 40px;
  padding-top: 30px;
  z-index: 9999;
  transition: var(--transition);
  overflow: hidden;
  overflow-y: auto;
}
@media (max-width: 575px) {
  .sidebar-area {
    width: 350px;
  }
}
.sidebar-area p {
  color: var(--white);
}
.sidebar-area .info {
  border-top: 1px solid rgba(255, 255, 255, 0.25);
}
.sidebar-area .info li {
  font-size: 15px;
}
.sidebar-area .info li i {
  margin-right: 8px;
}
.sidebar-area .info li a {
  color: var(--white);
}
.sidebar-area .sidebar__overlay {
  position: fixed;
  right: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  z-index: -1;
}
.sidebar-area button {
  position: absolute;
  right: 30px;
  top: 30px;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 20px;
  background-color: var(--primary-color);
  border-radius: 50%;
  transition: var(--transition);
}
.sidebar-area button:hover {
  transform: rotate(90deg);
}
.sidebar-area .social-icon a {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  border: 1px solid rgba(255, 255, 255, 0.25);
  color: var(--primary-color);
  margin-right: 10px;
}
.sidebar-area .social-icon a:hover {
  background-color: var(--primary-color);
  color: var(--heading-color);
  border: 1px solid var(--primary-color);
}
.sidebar-area.sidebar__hide {
  visibility: hidden;
  opacity: 0;
  right: -30px;
}
.sidebar-area .sidebar__search {
  position: relative;
}
.sidebar-area .sidebar__search input {
  width: 100%;
  padding: 8px 20px;
  padding-right: 40px;
}
.sidebar-area .sidebar__search i {
  position: absolute;
  top: 16px;
  right: 16px;
  cursor: pointer;
  color: var(--heading-color);
}

.mobile-menu .meanmenu-reveal {
  display: none !important;
}
.mobile-menu.mean-container .mean-nav > ul {
  width: 100%;
  display: block !important;
}
.mobile-menu ul li a i {
  display: none;
}

.banner-area,
.banner-two-area {
  overflow: hidden;
  position: relative;
}
.banner__dot-wrp {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translate(-50%);
  z-index: 2;
}
.banner__shape-left1 {
  position: absolute;
  top: 30px;
  left: 0;
}
@media (max-width: 575px) {
  .banner__shape-left1 {
    display: none;
  }
}
.banner__shape-left2 {
  position: absolute;
  top: 60px;
  left: 0;
}
@media (max-width: 575px) {
  .banner__shape-left2 {
    display: none;
  }
}
.banner__shape-left3 {
  position: absolute;
  bottom: 0px;
  left: 0;
}
.banner__shape-right1 {
  position: absolute;
  bottom: 0px;
  right: 0;
}
@media (max-width: 767px) {
  .banner__shape-right1 {
    display: none;
  }
}
.banner__shape-right2 {
  position: absolute;
  bottom: 0px;
  right: 0;
}
@media (max-width: 767px) {
  .banner__shape-right2 {
    display: none;
  }
}
.banner__line {
  position: absolute;
  bottom: 25%;
  left: 33%;
  z-index: 2;
}
.banner__right-line1,
.banner__right-line4,
.banner__right-line3,
.banner__right-line2 {
  position: absolute;
  top: -65px;
  right: 0;
}
@media (max-width: 575px) {
  .banner__right-line1,
  .banner__right-line4,
  .banner__right-line3,
  .banner__right-line2 {
    display: none;
  }
}
@media (max-width: 575px) {
  .banner__right-line2 {
    display: none;
  }
}
@media (max-width: 575px) {
  .banner__right-line3 {
    display: none;
  }
}
@media (max-width: 575px) {
  .banner__right-line4 {
    display: none;
  }
}
.banner__content,
.banner-two__content,
.banner-three__content {
  max-width: 770px;
  padding: 218px 0;
  position: relative;
}
@media (max-width: 991px) {
  .banner__content,
  .banner-two__content,
  .banner-three__content {
    padding: 100px 0;
  }
}
.banner__content h4,
.banner-two__content h4,
.banner-three__content h4 {
  text-transform: uppercase;
  font-size: 18px;
  line-height: 32px;
  font-weight: 600;
  letter-spacing: 1px;
}
.banner__content h4 svg,
.banner-two__content h4 svg,
.banner-three__content h4 svg {
  margin-top: -4px;
}
.banner__content h1,
.banner-two__content h1,
.banner-three__content h1 {
  font-size: 80px;
  line-height: 90px;
  font-weight: 700;
}
@media (max-width: 991px) {
  .banner__content h1,
  .banner-two__content h1,
  .banner-three__content h1 {
    font-size: 60px;
    line-height: 70px;
  }
}
@media (max-width: 767px) {
  .banner__content h1,
  .banner-two__content h1,
  .banner-three__content h1 {
    font-size: 50px;
    line-height: 60px;
  }
}
@media (max-width: 575px) {
  .banner__content h1,
  .banner-two__content h1,
  .banner-three__content h1 {
    font-size: 35px;
    line-height: 50px;
  }
}
.banner__content p,
.banner-two__content p,
.banner-three__content p {
  color: var(--white);
  opacity: 90%;
}
.banner__slider .slide-bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 1;
  z-index: -2;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  transform: scale(1);
  -webkit-transition: all 9s ease-out 0s;
  -moz-transition: all 9s ease-out 0s;
  -ms-transition: all 9s ease-out 0s;
  -o-transition: all 9s ease-out 0s;
  transition: all 9s ease-out 0s;
}
.banner__slider .swiper-slide-active .slide-bg {
  -webkit-transform: scale(1.3);
  -moz-transform: scale(1.3);
  transform: scale(1.3);
}
.banner-two__shape1 {
  position: absolute;
  right: 0;
  top: 100px;
}
@media (max-width: 575px) {
  .banner-two__shape1 {
    display: none;
  }
}
.banner-two__shape2 {
  position: absolute;
  right: 0;
  top: 130px;
}
@media (max-width: 575px) {
  .banner-two__shape2 {
    display: none;
  }
}
.banner-two__line-left {
  position: absolute;
  left: 0;
  top: 100px;
}
@media (max-width: 575px) {
  .banner-two__line-left {
    display: none;
  }
}
.banner-two__circle-solid {
  position: absolute;
  bottom: -80px;
  left: -100px;
}
@media (max-width: 575px) {
  .banner-two__circle-solid {
    display: none;
  }
}
.banner-two__circle-regular {
  position: absolute;
  bottom: -80px;
  left: -100px;
}
@media (max-width: 575px) {
  .banner-two__circle-regular {
    display: none;
  }
}
.banner-two__right-shape {
  position: absolute;
  bottom: 0;
  right: 0;
}
.banner-two__line {
  position: absolute;
  left: 17%;
  top: 25%;
  z-index: 2;
}
@media (max-width: 575px) {
  .banner-two__line {
    display: none;
  }
}
.banner-two__dot-wrp {
  bottom: 120px;
}
.banner-two__content,
.banner-three__content {
  max-width: 950px;
  margin: 0 auto;
  padding: 250px 0;
}
@media (max-width: 991px) {
  .banner-two__content,
  .banner-three__content {
    padding: 150px 0;
  }
}
.banner-two__content h1,
.banner-three__content h1 {
  font-size: 90px;
  line-height: 100px;
}
@media (max-width: 991px) {
  .banner-two__content h1,
  .banner-three__content h1 {
    font-size: 60px;
    line-height: 70px;
  }
}
@media (max-width: 575px) {
  .banner-two__content h1,
  .banner-three__content h1 {
    font-size: 30px;
    line-height: 40px;
  }
}
.banner-two__content h4,
.banner-three__content h4 {
  background-color: rgba(255, 255, 255, 0.1);
  display: inline-block;
  padding: 0px 15px;
}
@media (max-width: 575px) {
  .banner-two__content h4,
  .banner-three__content h4 {
    padding: 0px 10px;
    font-size: 14px;
    margin-bottom: 5px;
  }
}
@media (max-width: 575px) {
  .banner-two__content p,
  .banner-three__content p {
    font-size: 14px;
    margin-top: 10px;
  }
}
@media (max-width: 575px) {
  .banner-two__content .btn-one,
  .banner-three__content .btn-one {
    margin-top: 30px;
  }
}
.banner-three-area {
  overflow: hidden;
  position: relative;
  z-index: 1;
  background: linear-gradient(90deg, #0f0d1d -76.72%, #3c72fc 191.51%);
  padding-top: 250px;
  padding-bottom: 200px;
}
@media (max-width: 767px) {
  .banner-three-area {
    padding: 80px 0;
  }
}
.banner-three__bg {
  position: absolute;
  left: 20px;
  bottom: 80px;
  z-index: -1;
}
.banner-three__shape-left {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: -1;
}
@media (max-width: 575px) {
  .banner-three__shape-left {
    display: none;
  }
}
.banner-three__shape-right {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: -1;
  height: 90%;
}
.banner-three__shape-right img {
  height: 100%;
}
@media (max-width: 575px) {
  .banner-three__shape-right {
    display: none;
  }
}
.banner-three__content {
  max-width: 100%;
}
@media (max-width: 991px) {
  .banner-three__content {
    margin-top: 100px;
  }
}
@media (max-width: 575px) {
  .banner-three__content {
    margin-top: 50px;
  }
}
@media (max-width: 575px) {
  .banner-three__content h4 {
    font-size: 12px;
  }
}
.banner-three__container {
  max-width: 1350px;
  padding: 30px;
  margin: 0 auto;
}
.banner-three__video-btn a {
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  background: var(--white);
  color: var(--primary-color);
  font-size: 16px;
}
.banner-three__info {
  display: flex;
  align-items: center;
  gap: 40px;
  flex-wrap: wrap;
}
.banner__inner-page {
  overflow: hidden;
  z-index: 1;
  text-transform: capitalize;
}
.banner__inner-page .shape1 {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: -1;
}
.banner__inner-page .shape2 {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: -1;
}
.banner__inner-page .shape3 {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: -1;
}
.banner__inner-page::before {
  background: linear-gradient(270.07deg, #002b98 0.07%, #00060c 99.95%);
  z-index: -1;
  opacity: 0.75;
}
@media (max-width: 767px) {
  .banner__inner-page {
    padding: 80px 0;
  }
}
.banner__inner-page h2 {
  margin-bottom: 10px;
  color: var(--white);
}
.banner__inner-page span {
  color: var(--white);
  font-weight: 500;
}
.banner__inner-page span i {
  color: var(--white);
}
.banner__inner-page a {
  color: var(--white);
  font-weight: 500;
}
.banner__inner-page a:hover {
  color: var(--primary-color);
}

.section-header h5 {
  color: var(--primary-color);
  padding-bottom: 20px;
  text-transform: uppercase;
  font-weight: 500;
}
.section-header h5 svg,
.section-header h5 img {
  margin-top: -5px;
}
@media (max-width: 767px) {
  .section-header h5 {
    padding-bottom: 5px;
  }
}
.section-header h2 {
  text-transform: capitalize;
}
.section-header p {
  margin-top: 30px;
}

.service-area {
  position: relative;
  overflow: hidden;
}
.service__shape {
  position: absolute;
  right: -100px;
  top: 80px;
  z-index: -1;
}
.service__icon {
  width: 70px;
  height: 70px;
  line-height: 70px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  background-color: var(--primary10);
  border-radius: 0px;
  transition: var(--transition);
}
.service__item {
  position: relative;
  z-index: 1;
  background-color: var(--white);
  padding: 40px 30px;
  box-shadow: var(--shadow);
}
.service__item .service-shape {
  position: absolute;
  top: 22px;
  right: 0;
  opacity: 0.3;
  transition: var(--transition);
}
.service__item h4 {
  margin-top: 20px;
  margin-bottom: 10px;
}
.service__item p {
  transition: var(--transition);
}
.service__item::after {
  position: absolute;
  content: "";
  top: 0;
  right: 0;
  width: 0;
  height: 100%;
  background: var(--gradient-bg);
  transition: var(--transition);
  z-index: -1;
}
.service__item.active .service__icon {
  background-color: var(--white);
}
.service__item.active .service-shape {
  opacity: 0.5;
}
.service__item.active h4 a {
  color: var(--white);
}
.service__item.active p {
  color: var(--white);
  opacity: 90%;
}
.service__item.active::after {
  width: 100%;
  right: unset;
  left: 0;
}
.service-two-area,
.project-three-area {
  overflow: hidden;
  position: relative;
}
.service-two__shape-left {
  position: absolute;
  left: 0;
  top: 0;
}
.service-two__shape-right {
  position: absolute;
  right: 0;
  bottom: 0;
}
.service-two__content {
  position: relative;
  padding: 30px;
  padding-top: 0;
}
.service-two__content .icon {
  width: 70px;
  height: 70px;
  line-height: 70px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  border-radius: 0;
  background: var(--gradient-bg);
  margin-bottom: 30px;
  margin-top: -35px;
  transition: 1s;
}
.service-two__content .shape {
  position: absolute;
  top: 35px;
  right: 0;
  opacity: 0.3;
  transition: var(--transition);
}
.service-two__content p {
  margin-top: 10px;
  margin-bottom: 20px;
}
.service-two__item {
  box-shadow: var(--shadow);
  background-color: var(--main-bg);
  border-top-left-radius: 50px;
  border-bottom-right-radius: 50px;
  overflow: hidden;
}
.service-two__item:hover .icon {
  transform: rotateX(-360deg);
}
.service-two__item:hover .shape {
  opacity: 0.5;
}
.service-two__item:hover .read-more-btn {
  color: var(--primary-color);
}
.service-two__item:hover .read-more-btn i {
  color: var(--primary-color);
  margin-left: 10px;
}
.service-two__slider .swiper-slide-active .icon {
  transform: rotateX(-360deg);
}
.service-two__slider .swiper-slide-active .shape {
  opacity: 0.5;
}
.service-two__slider .swiper-slide-active .read-more-btn {
  color: var(--primary-color);
}
.service-two__slider .swiper-slide-active .read-more-btn i {
  color: var(--primary-color);
  margin-left: 10px;
}
.service-three-area {
  overflow: hidden;
  position: relative;
}
.service-three__shape {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}
.service-three__image img {
  border-radius: 8px;
}
.service-three__content {
  background-color: var(--white);
  padding: 20px 30px;
  display: flex;
  align-items: center;
  width: 85%;
  border-radius: 8px;
  box-shadow: var(--shadow);
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  transition: var(--transition);
  opacity: 1;
}
@media (max-width: 1399px) {
  .service-three__content {
    width: 95%;
  }
}
.service-three__content .icon {
  padding-right: 15px;
  margin-right: 15px;
  border-right: var(--border-1px);
}
.service-three__up-content {
  padding: 40px 30px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  transition: var(--transition);
  opacity: 0;
}
.service-three__up-content .icon {
  width: 70px;
  height: 70px;
  line-height: 70px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  border-radius: 0px;
  background: linear-gradient(180deg, #3c72fc -93.57%, #00060c 228.57%);
  margin: 0 auto;
}
.service-three__item {
  position: relative;
  border-radius: 8px;
  z-index: 1;
}
.service-three__item::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 0%;
  background: linear-gradient(180deg, #3c72fc -21.56%, #00060c 252.19%);
  border-radius: 8px;
  transition: var(--transition);
}
.service-three__item:hover .service-three__content {
  opacity: 0;
  bottom: 0;
}
.service-three__item:hover .service-three__up-content {
  opacity: 1;
}
.service-three__item:hover::before {
  height: 100%;
  bottom: 0;
  top: unset;
}
@media (max-width: 1199px) {
  .service-three__item {
    margin-bottom: 40px;
  }
}
.service-single-area {
  overflow: hidden;
}
.service-single__left-item .image {
  position: relative;
}
.service-single__left-item .title {
  font-size: 30px;
}
.service-single__left-item ul li {
  font-weight: 500;
  color: var(--heading-color);
}
.service-single__left-item ul li i {
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  background: var(--gradient-bg);
  color: var(--white);
  font-size: 10px;
  margin-right: 10px;
}
.service-single__right-item .service-category li:hover {
  background-color: var(--primary-color) !important;
}
.service-single__right-item .service-category li:hover a {
  color: var(--white) !important;
}
.service-single__right-item .service-category li:hover i {
  color: var(--white) !important;
}
.service-single__right-item .service-category li.active {
  background-color: var(--primary-color) !important;
}
.service-single__right-item .service-category li.active a {
  color: var(--white) !important;
}
.service-single__right-item .service-category li.active i {
  color: var(--white) !important;
}
.about-area,
.about-two-area {
  overflow: hidden;
  position: relative;
  padding-bottom: 240px;
}
.about__shape {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 0;
}
.about__shape img {
  width: 100%;
  animation: sway_Y 5s linear infinite alternate;
}
.about__left-item,
.about-two__left-item {
  position: relative;
}
@media (max-width: 1199px) {
  .about__left-item,
  .about-two__left-item {
    max-width: 580px;
    margin: 0 auto;
  }
}
.about__left-item .big-image,
.about-two__left-item .big-image {
  max-width: 386px;
  position: relative;
  z-index: 1;
}
.about__left-item .sm-image,
.about-two__left-item .sm-image {
  max-width: 295px;
  position: absolute;
  right: 25px;
  bottom: 30px;
  z-index: 2;
}
.about__left-item .sm-image .video__btn-wrp,
.about-two__left-item .sm-image .video__btn-wrp {
  position: absolute;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.about__left-item .circle-shape,
.about-two__left-item .circle-shape {
  position: absolute;
  top: 30px;
  right: 80px;
}
.about__right-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 20px;
}
.about__right-item .icon {
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  width: 100%;
  max-width: 80px;
  border-radius: 0;
  background-color: var(--primary10);
}
.about__info {
  position: relative;
  display: flex;
  align-items: center;
  gap: 40px;
}
@media (max-width: 575px) {
  .about__info {
    gap: 5px;
  }
}
.about-two-area {
  padding-bottom: 120px;
}
@media (max-width: 767px) {
  .about-two-area {
    padding-bottom: 60px;
  }
}
.about-two__shape {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}
.about-two__left-item .circle-shape {
  right: unset;
  left: -32%;
  top: 12%;
  animation: rotate 5s linear infinite;
}
.about-two__left-item .dots {
  position: absolute;
  right: 15%;
  top: 0;
}
.about-two__left-item .shape-halper {
  position: absolute;
  top: 28%;
  right: 26%;
}
.about-two__left-item .sm-image {
  bottom: 20px;
}
.about-two__right-item {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  max-width: 500px;
}
.about-two__right-item ul li {
  color: var(--heading-color);
  font-weight: 600;
}
.about-two__right-item ul li:not(:last-child) {
  margin-bottom: 20px;
}
.about-two__right-item ul li i {
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  background: var(--gradient-bg);
  color: var(--white);
  font-size: 10px;
  margin-right: 10px;
}
.about-three-area {
  z-index: 1;
}
.about-three__box-up {
  position: absolute;
  top: 15px;
  right: 0;
  z-index: -1;
}
.about-three__box-down {
  position: absolute;
  top: 60px;
  right: 0;
  z-index: -1;
}
.about-three__info {
  max-width: 430px;
}
.about-three__info .icon {
  width: 64px;
  height: 64px;
  line-height: 64px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  border-radius: 0px;
  width: 100%;
  max-width: 64px;
}
.about-three__left-item {
  max-width: 450px;
}
.about-three__left-item .about-call-icon {
  width: 56px;
  height: 56px;
  line-height: 56px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  border: 1px solid var(--primary-color);
}
.about-three__left-item .about-call-icon span {
  width: 48px;
  height: 48px;
  line-height: 50px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  background: var(--gradient-bg);
}
.about-three__image {
  max-width: 634px;
  float: right;
  z-index: 1;
}
.about-three__image .faq__line {
  left: 20% !important;
  z-index: -1;
  top: 10px;
}
.about-three__image .about-three-dot {
  position: absolute;
  left: 8%;
  bottom: 15%;
  z-index: -1;
}
.about-three__image .about-three-count {
  box-shadow: var(--shadow);
  width: 208px;
  position: absolute;
  top: 23%;
  left: 10px;
  background-color: var(--white);
  border-radius: 8px;
}
.about-three__image .about-three-count h3 {
  font-size: 28px;
  line-height: 40px;
}
.about-three__image .about-three-count h3 span {
  color: var(--secondary-color);
}
.about-three__image .about-three-count .icon {
  width: 40px;
}

.counter-area {
  position: relative;
}
.counter__shape {
  position: absolute;
  right: 0;
  bottom: 0;
}
.counter__wrp,
.quote__wrp {
  overflow: hidden;
  padding: 80px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  position: relative;
  margin-top: -120px;
  z-index: 2;
}
@media (max-width: 767px) {
  .counter__wrp,
  .quote__wrp {
    justify-content: center;
  }
}
.counter__item {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
}
.counter__item h3 {
  font-size: 36px;
  color: var(--white);
  font-weight: 700;
  line-height: 50px;
}
.counter__item h3 span {
  color: var(--white);
}

.case-area {
  overflow: hidden;
}
.case__image {
  position: relative;
}
.case__image::before {
  background: linear-gradient(0deg, #0f0d1d 0%, rgba(15, 13, 29, 0) 125%);
}
.case__content {
  position: absolute;
  bottom: 40px;
  width: 100%;
  left: 30px;
}
.case__btn {
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  position: absolute;
  right: 40px;
  bottom: 40px;
  z-index: 2;
}
.case__btn i {
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  border-radius: 0;
  background: var(--gradient-bg);
  font-size: 20px;
  color: var(--secondary-color);
  color: var(--white);
  transition: var(--transition);
}
.case__btn::after {
  position: absolute;
  content: url(../images/shape/case-btn-shape.png);
  right: -20px;
  top: 0px;
  z-index: -1;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}
.case__btn::before {
  position: absolute;
  content: url(../images/shape/case-btn-shape.png);
  left: -20px;
  top: -28px;
  z-index: -1;
  transform: rotate(180deg);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}
.case__btn:hover::before {
  left: -10px;
  top: -18px;
  opacity: 1;
  visibility: visible;
}
.case__btn:hover::after {
  right: -10px;
  top: -10px;
  opacity: 1;
  visibility: visible;
}
.case__btn:hover i {
  color: var(--primary-color);
  background: var(--white);
}
.case__item,
.project-three__item {
  position: relative;
}
.case__item:hover .case__btn,
.project-three__item:hover .case__btn {
  opacity: 1;
  visibility: visible;
  transform: translate(0);
}
.case__slider {
  margin-left: 19%;
}
@media (max-width: 1600px) {
  .case__slider {
    margin-left: 12%;
  }
}
@media (max-width: 1399px) {
  .case__slider {
    margin-left: 10%;
  }
}
@media (max-width: 991px) {
  .case__slider {
    margin-left: 0%;
    padding: 0 12px;
  }
}
.case__slider .swiper-slide.swiper-slide-active .case__item .case__btn,
.case__slider
  .swiper-slide.swiper-slide-active
  .project-three__item
  .case__btn {
  opacity: 1;
  visibility: visible;
  transform: translate(0);
}
.case-two-area {
  overflow: hidden;
  position: relative;
  z-index: 1;
  padding-bottom: 420px;
}
.case-two__bg {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
}
.case-two__bg img {
  width: 100%;
}
.case-two__container {
  max-width: 1600px;
  padding: 0 15px;
  margin: 0 auto;
}
.case-two__content {
  position: absolute;
  bottom: 30px;
  left: 30px;
  z-index: 1;
}
.case-two__content span {
  color: var(--primary-color);
  font-weight: 500;
  transition: var(--transition);
}
.case-two__btn {
  position: absolute;
  right: 30px;
  bottom: 30px;
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  border: 1px solid var(--white);
  font-size: 20px;
  color: var(--white);
  z-index: 1;
}
.case-two__btn:hover {
  color: var(--primary-color);
  background-color: var(--white);
}
.case-two__item {
  position: relative;
  z-index: 1;
}
.case-two__item::after {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 0;
  content: "";
  background: linear-gradient(0deg, #3c72fc 0%, rgba(0, 0, 0, 0) 100%);
  transition: var(--transition);
}
.case-two__item:hover::after {
  height: 100%;
}
.case-two__item:hover .case-two__content span {
  color: var(--white);
}
.case-three-area {
  overflow: hidden;
}
.case-single-area {
  overflow: hidden;
}
.case-single__title {
  font-size: 30px;
  line-height: 32px;
}
.case-single__item .case-date {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 30px;
}
.case-single__item .case-date li {
  font-weight: 500;
  color: var(--heading-color);
}
.case-single__item .case-date li span {
  font-weight: 400;
  color: var(--primary-color);
}
.case-single__item .case-challenge-list {
  max-width: 780px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}
.case-single__item .case-challenge li {
  font-weight: 500;
  color: var(--heading-color);
}
.case-single__item .case-challenge li i {
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  background: var(--gradient-bg);
  color: var(--white);
  font-size: 10px;
  margin-right: 10px;
}

.choose-area {
  overflow: hidden;
  position: relative;
  z-index: 1;
}
.choose__shape-left {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: -1;
}
.choose__shape-right1 {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 0;
}
.choose__shape-right2 {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 0;
}
.choose__image {
  position: absolute;
  top: 0;
  right: 0;
  width: 47%;
  height: 100%;
  z-index: -1;
}
.choose__image img {
  height: 100%;
}
.choose__video-btn,
.banner-video__video-btn,
.service-single__video-btn {
  position: absolute;
  right: 20%;
  top: 45%;
  z-index: 2;
}
.choose__video-btn .video-btn a,
.banner-video__video-btn .video-btn a,
.service-single__video-btn .video-btn a {
  width: 90px;
  height: 90px;
  line-height: 90px;
  font-size: 25px;
}
@media (max-width: 991px) {
  .choose__video-btn,
  .banner-video__video-btn,
  .service-single__video-btn {
    right: 42%;
    top: 20%;
  }
}
@media (max-width: 450px) {
  .choose__video-btn,
  .banner-video__video-btn,
  .service-single__video-btn {
    right: 34%;
    top: 15%;
  }
}

.offer-area {
  position: relative;
  overflow: hidden;
  z-index: 1;
}
.offer__shadow {
  position: absolute;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
}
.offer__shape-left {
  position: absolute;
  bottom: 0;
  left: 0;
  opacity: 0.3;
}
.offer__shape-right {
  position: absolute;
  top: 0px;
  right: 0;
  opacity: 0.3;
}
.offer__icon {
  width: 70px;
  height: 70px;
  line-height: 70px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  margin: 0 auto;
  background: linear-gradient(180deg, #3c72fc -210.71%, #00060c 100%);
  margin-top: -35px;
  transition: 1s;
}
.offer__item {
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 20px;
  padding-top: 0;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  position: relative;
}
@media (max-width: 991px) {
  .offer__item {
    margin-top: 30px;
  }
}
.offer__item .shape-top {
  position: absolute;
  top: 50%;
  right: 50%;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}
.offer__item .shape-bottom {
  position: absolute;
  bottom: 50%;
  left: 50%;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}
.offer__item:hover .shape-top {
  top: -1px;
  right: -1px;
  opacity: 1;
  visibility: visible;
}
.offer__item:hover .shape-bottom {
  position: absolute;
  bottom: -1px;
  left: -1px;
  opacity: 1;
  visibility: visible;
}
.offer__item:hover .offer__icon {
  background: var(--gradient-bg);
  transform: rotateY(360deg);
}
.offer__item:hover .offer__icon svg path {
  fill: #fff;
}

.brand__wrp {
  padding: 65px;
  background: var(--gradient-bg);
  margin-top: -85px;
  position: relative;
  z-index: 1;
}
.brand__shape {
  position: absolute;
  right: 0;
  bottom: 0;
  opacity: 0.3;
}
.brand__image {
  max-width: 175px;
}
.brand-three-area {
  overflow: hidden;
  position: relative;
  z-index: 1;
}
.brand-three-area::before {
  background-color: var(--heading-color);
  opacity: 0.9;
  z-index: -1;
}
.brand-three__shape1 {
  position: absolute;
  right: 0;
  bottom: 0;
}
@media (max-width: 767px) {
  .brand-three__shape1 {
    display: none;
  }
}
.brand-three__line1 {
  position: absolute;
  right: 0;
  bottom: 0;
}
@media (max-width: 767px) {
  .brand-three__line1 {
    display: none;
  }
}
.brand-three__shape2 {
  position: absolute;
  left: 0;
  bottom: 0;
}
@media (max-width: 767px) {
  .brand-three__shape2 {
    display: none;
  }
}
.brand-three__line2 {
  position: absolute;
  left: 0;
  bottom: 0;
}
@media (max-width: 767px) {
  .brand-three__line2 {
    display: none;
  }
}

.process-area {
  overflow: hidden;
}
.process__image {
  max-width: 178px;
  padding: 10px;
  margin: 0 auto;
  position: relative;
  border-radius: 50%;
}
.process__image img {
  width: 100%;
}
.process__image::after {
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  border: 1px dashed var(--primary-color);
  content: "";
  border-radius: 50%;
  position: absolute;
  animation: rotate 20s linear infinite;
}
.process__image .process-number {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  background: var(--gradient-bg);
  color: var(--white);
  font-size: 18px;
  position: absolute;
  top: 5px;
  left: 5px;
  z-index: 1;
}
.process__item {
  text-align: center;
  padding: 0 30px;
  position: relative;
}
@media (min-width: 992px) {
  .process__item {
    margin-bottom: 0px;
  }
}
.process__item .process-arry {
  position: absolute;
  top: 18%;
  right: -14%;
}
@media (max-width: 991px) {
  .process__item .process-arry {
    top: unset;
    right: unset;
    bottom: -110px;
    left: 40%;
  }
  .process__item .process-arry img {
    transform: rotate(90deg);
  }
}

.testimonial__arry-btn {
  display: flex;
  align-items: center;
  gap: 20px;
  justify-content: center;
  position: relative;
}
.testimonial__arry-btn::after {
  position: absolute;
  content: "";
  width: 35%;
  height: 1px;
  background-color: var(--primary-color);
  right: 0;
  top: 25px;
  opacity: 0.5;
}
@media (max-width: 575px) {
  .testimonial__arry-btn::after {
    width: 30%;
  }
}
.testimonial__arry-btn::before {
  position: absolute;
  content: "";
  width: 35%;
  height: 1px;
  background-color: var(--primary-color);
  left: 0;
  top: 25px;
  opacity: 0.5;
}
@media (max-width: 575px) {
  .testimonial__arry-btn::before {
    width: 30%;
  }
}
.testimonial__item {
  position: relative;
  padding: 40px;
  background-color: var(--white);
  box-shadow: var(--shadow);
}
.testimonial__item .coma {
  position: absolute;
  top: 40px;
  right: 40px;
}
.testimonial-two-area {
  margin-top: -300px;
  position: relative;
  z-index: 1;
}
.testimonial-two__slider {
  padding: 20px 0;
}
.testimonial-two__item,
.testimonial-three__item {
  background-color: var(--white);
  box-shadow: var(--shadow);
  padding: 40px;
  position: relative;
}
.testimonial-two__item .coma,
.testimonial-three__item .coma {
  position: absolute;
  bottom: 40px;
  right: 40px;
}
.testimonial-two__item.dark-mode,
.dark-mode.testimonial-three__item {
  background-color: var(--sub-bg);
}
.testimonial-three-area {
  overflow: hidden;
}
.testimonial-three__wrp {
  margin-left: 19%;
  margin-right: -70px;
}
@media (max-width: 1399px) {
  .testimonial-three__wrp {
    margin-left: 30px;
    margin-right: 30px;
  }
}
.testimonial-three__image {
  position: relative;
}
.testimonial-three__image svg {
  position: absolute;
  left: 50%;
  bottom: -12px;
  transform: translate(-50%);
}
.testimonial-three__item {
  box-shadow: none;
}

.talk-us__item {
  background: var(--gradient-bg);
  padding: 60px;
}
.talk-us__item .section-header h5 {
  padding-bottom: 5px;
}
.talk-us__item .section-header h2 {
  font-size: 30px;
}
.talk-us__item form label {
  color: var(--white);
  margin-bottom: 10px;
  text-transform: capitalize;
}
.talk-us__item form input {
  padding: 15px 20px;
  background-color: var(--white);
  border: none;
  outline: none;
  color: var(--paragraph);
  width: 100%;
}
.talk-us__item form textarea {
  padding: 15px 20px;
  background-color: var(--white);
  border: none;
  outline: none;
  color: var(--paragraph);
  width: 100%;
  height: 110px;
  resize: none;
}
.talk-us__item form button {
  padding: 15px 20px;
  background-color: var(--secondary-color);
  color: var(--white);
  font-weight: 600;
  width: 100%;
  text-align: center;
  margin-top: 15px;
  transition: var(--transition);
}
.talk-us__item form button:hover {
  opacity: 0.9;
}

.blog__image {
  position: relative;
  overflow: hidden;
}
.blog__image img {
  transition: var(--transition);
}
.blog__image .blog-tag {
  position: absolute;
  top: 15px;
  left: 15px;
  background: var(--gradient-bg);
  padding: 10px 22px;
}
.blog__image .blog-tag h3 {
  line-height: 22px;
  font-weight: 700;
}
.blog__image .blog-tag span {
  font-size: 12px;
  line-height: 22px;
}
.blog__content {
  padding: 30px;
}
.blog__content .blog-info {
  display: flex;
  align-items: center;
  gap: 22px;
}
.blog__content .blog-info li {
  display: flex;
  align-items: center;
  gap: 8px;
}
.blog__content .blog-info li a {
  color: var(--paragraph);
  font-size: 14px;
  line-height: 22px;
}
.blog__content .blog-info li a:hover {
  color: var(--primary-color);
}
.blog__item,
.blog-two__grid-item,
.blog-two__list-item {
  box-shadow: var(--shadow);
}
.blog__item .read-more-btn,
.blog-two__grid-item .read-more-btn,
.blog-two__list-item .read-more-btn {
  color: var(--paragraph);
}
.blog__item:hover .read-more-btn,
.blog-two__grid-item:hover .read-more-btn,
.blog-two__list-item:hover .read-more-btn {
  color: var(--primary-color);
}
.blog__item:hover .read-more-btn i,
.blog-two__grid-item:hover .read-more-btn i,
.blog-two__list-item:hover .read-more-btn i {
  color: var(--primary-color);
  margin-left: 10px;
}
.blog__item:hover .blog__image img,
.blog-two__grid-item:hover .blog__image img,
.blog-two__list-item:hover .blog__image img {
  transform: scale(1.1) rotate(3deg);
  filter: brightness(70%);
}
.blog-two-area {
  overflow: hidden;
}
.blog-two__grid-item,
.blog-two__list-item {
  border: 1px solid var(--border);
  padding: 20px;
}
.blog-two__grid-item .blog__content,
.blog-two__list-item .blog__content {
  padding-left: 10px;
  padding-right: 10px;
  padding-bottom: 10px;
}
.blog-two__list-item {
  display: flex;
  align-items: center;
  gap: 20px;
}
@media (max-width: 575px) {
  .blog-two__list-item {
    flex-direction: column;
  }
}
.blog-two__list-item .blog__content {
  padding: 10px;
  flex-basis: 50%;
}
@media (max-width: 575px) {
  .blog-two__list-item .blog__content {
    flex-basis: 100%;
  }
}
.blog-single__title {
  font-size: 30px;
}
.blog-single__left-item:hover .image img {
  transform: scale(1) rotate(0deg);
}
.blog-single__left-item .fs-30 {
  font-size: 30px;
}
.blog-single__left-item .hilight-text {
  border-left: 4px solid var(--primary-color);
  padding: 40px;
}
.blog-single__left-item .hilight-text p {
  max-width: 650px;
  color: var(--heading-color);
  font-weight: 500;
  text-transform: capitalize;
  font-style: italic;
  line-height: 26px;
}
.blog-single__left-item .hilight-text svg {
  float: right;
  margin-top: -30px;
}
.blog-single__left-item .tags-share {
  padding: 20px 0;
  border-top: var(--border-1px);
  border-bottom: var(--border-1px);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}
.blog-single__left-item .tags-share .tags {
  display: flex;
  align-items: center;
  gap: 10px;
}
.blog-single__left-item .tags-share .tags strong {
  color: var(--heading-color);
}
.blog-single__left-item .tags-share .tags a {
  padding: 2px 15px;
  font-size: 14px;
  font-weight: 600;
  border: var(--border-1px);
  text-transform: capitalize;
}
.blog-single__left-item .tags-share .tags a:hover {
  background-color: var(--primary-color);
  color: var(--white);
  border: 1px solid var(--primary-color);
}
.blog-single__left-item .tags-share .share {
  display: flex;
  align-items: center;
}
.blog-single__left-item .tags-share .share strong {
  color: var(--heading-color);
}
.blog-single__left-item .tags-share .share a {
  font-size: 20px;
  color: var(--paragraph);
  margin-left: 20px;
}
.blog-single__left-item .tags-share .share a:hover {
  color: var(--primary-color);
}
.blog-single__left-item .tags-share .share a.active {
  color: var(--primary-color);
}
.blog-single__right-item .item,
.service-single__right-item .item {
  padding: 30px;
}
.blog-single__right-item .item .title,
.service-single__right-item .item .title {
  position: relative;
  margin-bottom: 35px;
  font-weight: 700;
}
.blog-single__right-item .item .title::after,
.service-single__right-item .item .title::after {
  position: absolute;
  content: "";
  left: 0;
  bottom: -10px;
  height: 2px;
  width: 40px;
  background-color: var(--primary-color);
}
.blog-single__right-item .item .serach-bar,
.service-single__right-item .item .serach-bar {
  position: relative;
}
.blog-single__right-item .item .serach-bar input,
.service-single__right-item .item .serach-bar input {
  width: 100%;
  border: var(--border-1px);
  padding: 16px;
}
.blog-single__right-item .item .serach-bar input:focus,
.service-single__right-item .item .serach-bar input:focus {
  border: 1px solid rgba(13, 110, 253, 0.5);
}
.blog-single__right-item .item .serach-bar button,
.service-single__right-item .item .serach-bar button {
  position: absolute;
  right: 15px;
  top: 18px;
  color: var(--span);
  background-color: var(--white);
}
.blog-single__right-item .item .category li,
.service-single__right-item .item .category li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background-color: var(--white);
  transition: var(--transition);
}
.blog-single__right-item .item .category li span,
.service-single__right-item .item .category li span {
  transition: var(--transition);
}
.blog-single__right-item .item .category li:hover a,
.service-single__right-item .item .category li:hover a {
  color: var(--primary-color);
}
.blog-single__right-item .item .category li:hover span,
.service-single__right-item .item .category li:hover span {
  color: var(--primary-color);
}
.blog-single__right-item .item .category li:not(:last-child),
.service-single__right-item .item .category li:not(:last-child) {
  margin-bottom: 10px;
}
.blog-single__right-item .item .single-post li,
.service-single__right-item .item .single-post li {
  display: flex;
  align-items: center;
  gap: 10px;
}
.blog-single__right-item .item .single-post li:not(:last-child),
.service-single__right-item .item .single-post li:not(:last-child) {
  margin-bottom: 15px;
}
.blog-single__right-item .item .single-post li span,
.service-single__right-item .item .single-post li span {
  font-size: 14px;
}
.blog-single__right-item .item .tags,
.service-single__right-item .item .tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}
.blog-single__right-item .item .tags a,
.service-single__right-item .item .tags a {
  padding: 5px 14px;
  font-weight: 500;
  border: var(--border-1px);
  text-transform: capitalize;
  background-color: var(--white);
  font-size: 14px;
}
.blog-single__right-item .item .tags a:hover,
.service-single__right-item .item .tags a:hover {
  background-color: var(--primary-color);
  color: var(--white);
  border: 1px solid var(--primary-color);
}
.blog-single__review .image {
  max-width: 96px;
  width: 100%;
}
.blog-single__review .reply {
  border-radius: 30px;
  padding: 0px 12px;
  font-weight: 500;
}

.team-area {
  overflow: hidden;
}
.team__share {
  position: absolute;
  bottom: 30px;
  right: 30px;
}
@media (max-width: 991px) {
  .team__share {
    right: 20px;
  }
}
.team__share button {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  background: var(--gradient-bg);
  color: var(--white);
  font-size: 18px;
}
.team__share ul {
  width: 40px;
  background: var(--gradient-bg);
  text-align: center;
  padding-top: 20px;
  padding-bottom: 12px;
  border-radius: 40px;
  margin-bottom: 15px;
  display: none;
  transition: var(--transition);
}
.team__share ul li:not(:last-child) {
  margin-bottom: 5px;
}
.team__share ul li a {
  color: var(--white);
}
.team__share:hover ul {
  display: block;
}
.team__content {
  position: absolute;
  max-width: 270px;
  padding: 20px 30px;
  background: var(--gradient-bg);
  bottom: 0;
  left: 0;
  width: 100%;
}
.team__item {
  position: relative;
}
.team__item::before {
  transition: var(--transition);
  position: absolute;
  content: "";
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0%;
  background: linear-gradient(
    359.9deg,
    #00060c 0.09%,
    rgba(22, 36, 62, 0) 99.91%
  );
}
.team__item:hover::before {
  height: 100%;
  top: 0;
}
.team-single__image {
  overflow: hidden;
  position: relative;
}
.team-single__image img {
  width: 100%;
}
.team-single__image .team-info {
  position: absolute;
  left: 50%;
  bottom: 30px;
  display: flex;
  gap: 10px;
  transform: translateX(-50%);
}
.team-single__image .team-info a {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  border-radius: 0;
  color: var(--white);
  border: 1px solid var(--white);
}
.team-single__image .team-info a:hover {
  background-color: var(--primary-color);
  border: 1px solid var(--primary-color);
}
.team-single__image .team-info a.active {
  background-color: var(--primary-color);
  border: 1px solid var(--primary-color);
}
.team-single__info .skills .experience-title {
  font-weight: 400;
}
.team-single__info .skills .progress {
  height: 8px;
}
.team-single__info .skills .progress .progress-bar {
  background-color: var(--primary-color);
}

.faq-area {
  overflow: hidden;
  position: relative;
  z-index: 1;
}
.faq__shape {
  position: absolute;
  right: 0;
  top: 0;
  z-index: -1;
}
.faq__image {
  position: relative;
}
.faq__line {
  position: absolute;
  top: 30px;
  left: 30px;
}

.quote__wrp {
  display: block;
  padding: 60px;
  z-index: 1;
  margin-top: 0;
  margin-bottom: -120px;
}
.quote__shape {
  position: absolute;
  left: 0;
  border-top: 0;
  z-index: -1;
}

.pricing-area {
  position: relative;
  z-index: 1;
}
.pricing__shape-up {
  position: absolute;
  top: -120px;
  left: 0;
  z-index: -1;
}
.pricing__shape-down {
  position: absolute;
  top: -80px;
  left: 0;
  z-index: -1;
}
.pricing__shape {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: -1;
}
.pricing__item {
  overflow: hidden;
  position: relative;
  z-index: 1;
  background: linear-gradient(180deg, #3c72fc -1.09%, #00060c 175.27%);
  padding: 50px 40px;
}
.pricing__item .item-shape {
  position: absolute;
  bottom: 40px;
  right: 0;
  z-index: -1;
}
.pricing__item ul {
  padding: 40px 0;
}
.pricing__item ul li {
  color: var(--white);
}
.pricing__item ul li:not(:last-child) {
  margin-bottom: 20px;
}
.pricing__item .pricing-head {
  padding-bottom: 30px;
  border-bottom: 1px solid rgba(227, 227, 227, 0.4);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}
.pricing__item .pricing-head h2 {
  color: var(--white);
}
.pricing__item .pricing-head h2 span {
  color: var(--white);
  font-size: 18px;
  font-weight: 400;
}
.pricing__item .pricing-icon {
  width: 94px;
  height: 90px;
  text-align: center;
  line-height: 90px;
}
.pricing__item .pricing-icon img {
  transition: 1s;
}
.pricing__item:hover .item-shape {
  animation: bobble 2s ease-in-out infinite alternate;
}
.pricing__item:hover .pricing-icon img {
  transform: rotateY(-360deg);
}

.project-three-area {
  padding-bottom: 240px;
  padding-top: 240px;
  margin-top: -120px;
}
.project-three__item .case__content {
  text-align: center;
}
.project-three__item .case__btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  bottom: unset;
  right: unset;
}

.banner-video-area {
  position: relative;
}
.banner-video__wrp {
  margin-top: -120px;
  z-index: 1;
}
.banner-video__wrp img {
  border-radius: 20px;
}
.banner-video__wrp::before {
  background-color: var(--heading-color);
  border-radius: 20px;
  opacity: 0.5;
}
.banner-video__video-btn,
.service-single__video-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  right: unset;
}

.contact__left-item {
  padding: 50px;
}
.contact__left-item ul li {
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}
.contact__left-item ul li i {
  width: 70px;
  height: 70px;
  line-height: 70px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  background-color: var(--white);
  display: block;
  position: relative;
}
.contact__left-item ul li i::after {
  position: absolute;
  content: "";
  width: 60px;
  height: 62px;
  line-height: 62px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  border: 1px dashed var(--primary-color);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.contact__left-item .social a {
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  background-color: var(--white);
  color: var(--heading-color);
  margin-right: 5px;
}
.contact__left-item .social a:hover {
  color: var(--primary-color);
}
.contact__right-item {
  padding-left: 50px;
}
@media (max-width: 991px) {
  .contact__right-item {
    padding-left: 0;
  }
}
.contact__form form .nice-select {
  width: 100%;
  font-size: 16px;
  font-weight: 400;
  border: none;
  background-color: var(--sub-bg);
  margin-bottom: 30px;
  padding: 18px 20px;
  height: 70px;
  border-radius: 5px;
}
.contact__form form .nice-select .list {
  width: 100%;
}
.contact__form form label {
  font-family: var(--quicksand);
  color: var(--heading-color);
  margin-bottom: 15px;
  font-weight: 700;
}
.contact__form form input,
.contact__form form textarea {
  width: 100%;
  padding: 18px 20px;
  background-color: var(--sub-bg);
  border-radius: 4px;
  margin-bottom: 30px;
}
.contact__form form textarea {
  height: 188px;
}
.contact__map {
  margin-bottom: -10px;
}
.contact__map iframe {
  width: 100%;
  height: 600px;
}

.error__item {
  max-width: 860px;
  margin: 0 auto;
  text-align: center;
}
.error__item h2 {
  font-size: 64px;
  line-height: 72px;
}
@media (max-width: 991px) {
  .error__item h2 {
    font-size: 40px;
    line-height: 50px;
  }
}
@media (max-width: 575px) {
  .error__item h2 {
    font-size: 30px;
    line-height: 40px;
  }
}

.footer-area,
.footer-two-area {
  overflow: hidden;
  position: relative;
}
.footer__wrp {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 30px;
}
@media (max-width: 575px) {
  .footer__wrp {
    flex-direction: column;
    justify-content: left;
    align-items: unset;
  }
}
.footer__shape-solid-left {
  position: absolute;
  top: 0;
  left: 0;
}
.footer__shape-regular-left {
  position: absolute;
  top: 30px;
  left: 0;
}
@media (max-width: 767px) {
  .footer__shape-regular-left {
    display: none;
  }
}
.footer__shape-solid-right {
  position: absolute;
  top: 0;
  right: 0;
}
@media (max-width: 767px) {
  .footer__shape-solid-right {
    display: none;
  }
}
.footer__shape-regular-right {
  position: absolute;
  top: 30px;
  right: 0;
}
@media (max-width: 767px) {
  .footer__shape-regular-right {
    display: none;
  }
}
.footer__shadow-shape {
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translate(-50%);
  animation: footer__shadow 10s linear infinite alternate;
}
.footer__item .logo {
  margin-top: -30px;
}
@media (max-width: 575px) {
  .footer__item .logo {
    margin-top: 0;
  }
}
.footer__item .footer-title {
  margin-bottom: 30px;
  color: var(--white);
}
.footer__item ul li:not(:last-child) {
  margin-bottom: 15px;
}
.footer__item ul li a {
  color: var(--white);
  opacity: 0.8;
}
.footer__item ul li a:hover {
  color: var(--primary-color);
  padding-left: 5px;
}
.footer__item .footer-contact li {
  display: flex;
  align-items: center;
  gap: 15px;
  color: var(--white);
}
.footer__item .footer-contact li i {
  font-size: 24px;
}
.footer__item .footer-contact li h5 {
  color: var(--white);
}
.footer__item p {
  color: var(--white);
  opacity: 0.8;
}
.footer__item.item-big {
  max-width: 270px;
  width: 100%;
}
.footer__item.item-sm {
  max-width: 170px;
  width: 100%;
}
.footer__item .social-icon {
  margin-top: 30px;
  display: flex;
  align-items: center;
  gap: 7px;
}
.footer__item .social-icon a {
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  border-radius: 50%;
  transition: var(--transition);
  border-radius: 0;
  border: 1px solid rgba(227, 227, 227, 0.2);
  color: var(--white);
}
.footer__item .social-icon a:hover {
  background-color: var(--primary-color);
}
.footer__item .social-icon a.active {
  background-color: var(--primary-color);
}
.footer__copyright {
  position: relative;
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 225, 0.2);
}
.footer__copyright p {
  color: var(--white);
  opacity: 0.8;
}
.footer__copyright a {
  color: var(--white);
  opacity: 0.8;
}
.footer__copyright a:hover {
  color: var(--primary-color);
}
.footer-two-area {
  padding-top: 120px;
}

@keyframes sway {
  0% {
    transform: translateX(-20px);
  }
  100% {
    transform: translateX(0px);
  }
}
.sway__animation {
  animation: sway 3s linear infinite alternate;
}

@keyframes swayX {
  0% {
    transform: translateX(20px);
  }
  100% {
    transform: translateX(0px);
  }
}
.sway__animationX {
  animation: swayX 3s linear infinite alternate;
}

@keyframes footer__shadow {
  0% {
    margin-left: -200px;
  }
  100% {
    margin-right: -200px;
  }
}
@keyframes sway_Y {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(20px);
  }
}
.sway_Y__animation {
  animation: sway_Y 3s linear infinite alternate;
}

@keyframes sway_YY {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(-20px);
  }
}
.sway_Y__animationY {
  animation: sway_YY 3s linear infinite alternate;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.animation__rotate {
  animation: rotate 40s linear infinite;
}

@keyframes rotateY {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}
.animation__rotateY {
  animation: rotateY 40s linear infinite;
}

@keyframes bobble {
  0% {
    transform: translateY(0) scale(1);
  }
  100% {
    transform: translateY(-20px) scale(1.1);
  }
}
.bobble__animation {
  animation: bobble 3s ease-in-out infinite alternate;
}

@-webkit-keyframes video-animation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.5);
  }
  100% {
    opacity: 0;
    transform: scale(2);
  }
}
@keyframes video-animation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.5);
  }
  100% {
    opacity: 0;
    transform: scale(2);
  }
}

.icon-box {
  background: var(--blue);
  padding: 20px;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

/* Active Nav Link Style */
.main-menu nav ul li a.active {
  color: var(--blue);
}

/* Style for active link in sub-menu */
.main-menu nav ul li .sub-menu li a.active {
  color: var(--blue) !important;
}

.cursor-inner.cursor-hover {
  width: 20px;
  height: 20px;
}

/* Set cursor for dropdowns */
.main-menu nav ul li > a {
  cursor: pointer;
}

/* Terms & Privacy Page Spacing */
.terms-item,
.privacy-item {
  margin-bottom: 20px;
}
