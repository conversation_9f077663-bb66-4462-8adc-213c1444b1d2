<?
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);

							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';
include_once LIB_PATH . 'mail.php';

//--- 8. local var -----
$pgError = false;

if($submitFlag == 'submitEnquiry'){

/*
	if(isset($topic)){
		switch ($topic) {
			case 'software':
			case 'hardware':
			case 'maintenance':
			case 'account':
				$sendTo = $EMAIL_SALES;
				$team = 'our sales team';
				break;
			case 'support':
			case 'other':
			default:
			   $sendTo = $EMAIL_SUPPORT; 	
			   $team = 'our customer service team';
		} //close switch
	} else {
	   $sendTo = $EMAIL_SALES;
	}
*/
	$sendTo = $EMAIL_SALES;
	$team = 'our customer service team';

	$senderName = trim($name);
	$senderCompany = trim($company);
	$senderEmail = strtolower(trim($email));
	$subject = nl2br(ucwords(stripslashes(trim($subject))));
	$message = nl2br(stripslashes(trim($message)));
	
	if($senderEmail != ''){
//echo "senderEmail: $senderEmail";
//echo "sendTo: $sendTo";
		$incidentId = date('ymd-His');
		$incidentDate = date('m/d/y H:i');
		//mail to support --------------------
		$msgBody = '<font style="font-size:12px; font-family:Verdana, Arial, Helvetica, sans-serif;">';
		$msgBody .= 'Thank you for contacting '. $CO_NAME .'<p>';
		$msgBody .= "Customer: $senderName - $incidentDate";
		if($tel1 !=""){ $msgBody .=  "<br> Daytime Phone: $tel1";}
		$msgBody .= "<p>
				Case Number: $incidentId<br>";
		$msgBody .= "Case Subject: $subject <br>";
		$msgBody .= "Case Topic: $topic <br>
				Case Description:<p>
				$message </font>";

		$m2staff = new Mail();
		$m2staff->charset = "utf-8";
		$m2staff->From($senderEmail); $m2staff->To($sendTo);
		$m2staff->Subject($subject . " - Case:$incidentId"); $m2staff->Body($msgBody);
		$m2staff->Send();

	//auto-response to customer --------------------
		$m2cst = new Mail();
		$m2cst->charset = "utf-8";
		$m2cst->From($EMAIL_SUPPORT); $m2cst->To($senderEmail);
		$replySubject = "$CO_NAME Auto-Response [Case:$incidentId]";
		$replyMsgBody = '<font style="font-size:12px; font-family:Verdana, Arial, Helvetica, sans-serif;">' . strtoupper($CO_NAME) . ' CLIENT SERVICE NOTIFICATION<P>';
		$replyMsgBody .= "Case Number: $incidentId<br>";
		$replyMsgBody .= "Case Subject: $subject<p>";
		$replyMsgBody .= $CO_NAME . ' thanks you for your email. This auto-generated email is to confirm that we have received your inquiry. PLEASE DO NOT REPLY TO THIS EMAIL.<p>We will try to respond to your email as soon as we can. We appreciate your patience as we work to bring better service to you.<p><p>
		Yours Sincerely,<p>
		Client Services, ' . $CO_NAME .'<p><p>';
		$replyMsgBody .= "CASE DESCRIPTION:<P>
						$message </font>";
		
		$m2cst->Subject($replySubject); $m2cst->Body($replyMsgBody);	//mail to customer
		$m2cst->Send();
	
		header(LANG_URL . 'coy/contact-us.php?flag=true');	
	}
	
    $bodyTxt = '<p><strong>Your message has been sent to ' . $team . '.</strong>
				<br><br>
				You will receive an acknowledgment email shortly to confirm that we have 
				received your message.<br></p>
				<p>&nbsp;</p>
				<p><a href="' . $SITE_URL . '">Click here</a> to return to the main page.<p><br> 
				<p><p>Best Regards, </p><p><br>' .
				$CO_NAME . ' Support Team<br></p>';
		
}else { 
	header(LANG_URL . 'coy/contact-us.php?flag=false');	
	$bodyTxt = '<p><strong>Page Error. Please contact ' . $CO_NAME . ' system support team for assistance.</strong><br>';
	$pgError = true;
}





//--- **below 4 lines includes both redirected msg + current page msg
if(isset($alertMsg)){$alertMsg = array($alertMsg);} else { $alertMsg = array();}
if(isset($noticeMsg)){$noticeMsg = array($noticeMsg);} else { $noticeMsg = array();}
if(isset($_SESSION['SS_alertMsg']) && $_SESSION['SS_alertMsg']!=''){$alertMsg[] = $_SESSION['SS_alertMsg']; unset($_SESSION['SS_alertMsg']); unset($SS_alertMsg);}
if(isset($_SESSION['SS_noticeMsg']) && $_SESSION['SS_noticeMsg']!=''){$noticeMsg[] = $_SESSION['SS_noticeMsg']; unset($_SESSION['SS_noticeMsg']);unset($SS_noticeMsg);}


//$sql_meta="select * from category where cat_id='1'"; $res_meta=$myDb->query($sql_meta) ;$rec_meta=mysql_fetch_array($res_meta);

//--- setup pgMsg ---------
if(!empty($alertMsg)){$alertMsg = implode('<BR>', $alertMsg); $pgMsg = $alertMsg; $pgMsgClass = "alertBox";} 
if(!empty($noticeMsg)){
	$noticeMsg = implode('<BR>', $noticeMsg); if(!empty($alertMsg)){ $pgMsg .= "<BR>";}
	$pgMsg .= $noticeMsg; if(!isset($pgMsgClass)){$pgMsgClass = "noticeBox";}
}
?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, maximum-scale=1">
<title>Email | <?php echo $CO_NAME;?></title>
<meta name="description" content="<?php echo $CO_NAME;?> offers affordable and scalable business IT solutions for Small and Medium-sized Businesses (SMBs). Whether you are setting up a new office, plan to extend your existing network, integrate business applications, or to expand your storage capacity, our  team of IT professionals can help you to achieve your goal.">
<meta name="keywords" content="system deployment, infrastructure setup, system implementation, office IT setup,system setup, business IT setup,IT implementation">
<?php
echo getPgCss();
?>
<link href="<?php echo CSS_URL; ?>mainpage.css" rel="stylesheet" type="text/css">
<link href="<?php echo CSS_URL; ?>detailpage.css" rel="stylesheet" type="text/css">
</head>
<body>

<!--Header_section-->
 	<?php include(INCLUDE_PATH . 'head.php');?>
<!--Header_section--> 

<!--slider_section-->
   <?php include(SLIDER_PATH . 'slider-mainpage.php');?>
<!--slider_section--> 



<section id="tag-stdm">
  <div class="container">
  
	<div class="row">
    <div class="col-lg-9 col-md-9 col-sm-9 col-xs-12 pull-right">
      <?
if($pgError){
	echo '<h2>Submission Error</h2><br><span style="font-size:16px;">Submission error occured. Plesae try again.</span>';
} else {
	echo '<h2>Enquiry Submitted</h2><br><span style="font-size:16px;">Thank you for contacting <?php echo $CO_NAME?>. Your ticket has been successfully submitted.
	<p>We will try to respond to your inquiry within 24 hours. Click <a href="'.LANG_URL.'">here</a> to return to our main page.</p></span>';
}

?>
      </div>
      
      
      
      
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 pull-left">
                    <div class="delay-01s animated fadeInDown wow animated">
                    <?
                    include("left-menu-email.php");
                    ?>
                    </div>
                </div>
      </div>
  </div> 
</section>


<!--Footer-->
   <?php include(INCLUDE_PATH . 'foot.php');?>

<script type="text/javascript" src="<?php echo JSLIB_URL;?>custom.js"></script>    
</body>
</html>