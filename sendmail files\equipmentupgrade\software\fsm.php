<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';

$picCnt = 0;

?>
<!DOCTYPE html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html lang="en" class="no-js"> <!--<![endif]-->
<head>
   <meta charset="utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta name="description" content="MySaas Field Service Manager helps track work orders and service cost including labor, material and service charges.">
   <meta name="keywords" content="field service management, facility management, CMMS, work order management, PM Plan, work scheduling,field service management,work order management,planning software">
   <meta name="techspace" content="techspace.co.th">

   <title>Field Service Management, Work Order Management, Preventive Maintenance Planning & Scheduling</title>

   <!-- retina Bookmark Icon -->
   <link rel="apple-touch-icon-precomposed" href="apple-icon.png" />

   <!-- CSS -->
   <link href="<?php echo CSS_URL?>foundstrap.css" rel="stylesheet" />
   <link href="<?php echo CSS_URL?>font-awesome.min.css" rel="stylesheet" />

   <!--[if (lt IE 9) & (!IEMobile)]>
      <script src="https://cdnjs.cloudflare.com/ajax/libs/respond.js/1.4.2/respond.min.js"></script>
   <![endif]-->
   
   <!-- CSS Plugin -->   
   <link href="<?php echo JSLIB_URL?>rs-plugin/css/settings.css" rel="stylesheet" media="screen">

   <!-- theme Stylesheet -->
   <link href="<?php echo CSS_URL?>style.css" rel="stylesheet">
   <link href="<?php echo CSS_URL?>theme-responsive.css" rel="stylesheet">   
   
 
   <!-- favicon -->
   <link rel="shortcut icon" href="<?php echo IMG_URL;?>favicon.ico">
   
   <!-- modernizr -->
   <script src="<?php echo JSLIB_URL?>modernizr.js"></script>
   
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-main.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
      <h2>MySaas Field Service Manager (FSM) / CMMS</h2>
            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
             MySaas Field Service Manager is a simple yet powerful application that is designed to help  improving work efficiency and customer satisfaction. It provides a 360 view of all your field service activities, enabling you to keep track of your  jobs, customers, invoices and schedules online.

            <p>With MySaas Field Service Manager, you can plan, schedule, assign and track work orders online. Additionally, the application also allows you to track job cost and labor utilization. Managers, engineers and technicians can track and update work  status through their laptops or mobile applications, providing instant  work status update to the customer and team.</p>

            <h3>MySaas FSM runs on the cloud, providing instant, real-time access to work order information anywhere, anytime. With MySaas FSM, you can:</h3>
            <ul class="me-list list-circle-check">
              <li>Create, submit, approve, track, and manage work orders in   real-time</li>
              <li>Receive work notification and alerts via E-mail or SMS.</li>
              <li>PM Planning and auto-scheduling of work order on due dates</li>
              <li>Maintain equipment data, service history and costs</li>
              <li>Configurable workflows to match your business needs.</li>
              <li>Improve  operational  efficiencies and customer satisfaction</li>
            </ul>


           <h3>Features</h3>
           <ul class="me-list list-circle-check">
              <li>Increase productivity, improve customer loyalty, and save more time  each day</li>
              <li>Gain greater control of  jobs and service personnel.</li>
              <li>Track work orders and service cost including labor, material and service charges.</li>
              <li>Create and dispatch work orders across multiple business units, region or locations.</li>
              <li>Simple, easy to use interface for technician/repair men to enter work status.</li>
              <li> Ability to configure  workflow behaviors</li>
              <li> User-definable email notification and screen alert capabilities</li>
              <li> User-definable service code with labor, material and service charges</li>
              <li>Generate billings and service reports</li>
              <li> Track job time, response time, machine downtime and other metrics </li>
              <li>Mobile work order integration</li>
            </ul>

           <p>MySaas FSM is fully integrated with the <a href="operations.htm" target="_blank">MySaas Operation Management System (OPS)</a>, providing    real-time access to customer billings and stocks information.</p>

            </div>

            <!--Header_section-->
               <?php include('menu-software.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->

   
      <section class="container container-gray container-border">
         <div class="row">
            <div class="large-12 column text-center">
               <h3 class="heading-light">Close More Deals with MySaaS CRM</h3>
               <p class="gap" data-gap-bottom="38">Everything you need is combined in one simple, integrated and effective solution.</p>

               <ul class="portfolio-container large-block-grid-4 medium-block-grid-2 small-block-grid-1 no-wrap">
                  <li class="web motion">
                     <figure class="me-image">
                        <div class="image-content">
                           <img src="<?php echo IMG_URL?>software/crm-contacts.jpg" alt="sales dashboard">
                           <div class="img-overlay"></div>  
                           
                           <a href="<?php echo IMG_URL?>software/crm-contacts.jpg" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">
                              <i class="fa fa-search"></i>
                           </a>
                        </div>

                        <figcaption class="image-caption">
                           <span>Sales Contacts</span>
                           <p>Enables businesses to manage contact information and activity for marketing and customer service.</p>
                        </figcaption>
                     </figure>
                  </li>
                  <li class="motion">
                     <figure class="me-image">
                        <div class="image-content">
                           <img src="<?php echo IMG_URL?>software/crm-leads.jpg" alt="Sales Contact">
                           <div class="img-overlay"></div>  
                           
                           <a href="<?php echo IMG_URL?>software/crm-leads.jpg" class="preview fancybox" data-fancybox-group="gallery" title="Sales Contact Screenshot">
                              <i class="fa fa-search"></i>
                           </a>
                        </div>

                        <figcaption class="image-caption">
                           <span>Sales Leads</span>
                           <p>Lead management & lead tracking software that optimize marketing campaigns from lead to close.</p>
                        </figcaption>
                     </figure>
                  </li>
                  <li class="web print">
                     <figure class="me-image">
                        <div class="image-content">
                           <img src="<?php echo IMG_URL?>software/crm-opportunities.jpg" alt="Portfolio Image">
                           <div class="img-overlay"></div>  
                           
                           <a href="<?php echo IMG_URL?>software/crm-opportunities.jpg" class="preview fancybox" data-fancybox-group="gallery" title="Sales Lead Screenshot">
                              <i class="fa fa-search"></i>
                           </a>
                        </div>

                        <figcaption class="image-caption">
                           <span>Sales Opportunities</span>
                           <p>Sales opportunity management tools will help your sales team effectively manage all of your prospects.</p>
                        </figcaption>
                     </figure>
                  </li>
                  <li class="web">
                     <figure class="me-image">
                        <div class="image-content">
                           <img src="<?php echo IMG_URL?>software/crm-opportunities.jpg" alt="Portfolio Image">
                           <div class="img-overlay"></div>  
                           
                           <a href="<?php echo IMG_URL?>software/crm-opportunities.jpg" class="preview fancybox" data-fancybox-group="gallery" title="Sales Opportunity Screenshot">
                              <i class="fa fa-search"></i>
                           </a>
                        </div>

                        <figcaption class="image-caption">
                           <span>Ticket Management</span>
                           <p>Ticket Management System that simplifies, streamlines, and automates the managing of company tickets.</p>
                        </figcaption>
                     </figure>
                  </li>
               </ul>

            </div>
         </div>
      </section>


      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->

   <!-- javascript -->
   <script src="<?php echo JSLIB_URL?>jquery.min.js"></script>
   <script src="<?php echo JSLIB_URL?>foundstrap.js"></script>
   <script src="<?php echo JSLIB_URL?>jquery.sscr.js"></script>
   <script src="<?php echo JSLIB_URL?>jquery.waypoints.min.js"></script>
   <script src="<?php echo JSLIB_URL?>owl.carousel.min.js"></script>
   <script src="<?php echo JSLIB_URL?>jquery.scrollUp.js"></script>
   <script src="<?php echo JSLIB_URL?>jquery.retina.js"></script>

   <!-- javascript plugin - popup images  -->
   <script src="<?php echo JSLIB_URL?>jquery.fancybox.js"></script>
   <script src="<?php echo JSLIB_URL?>jquery.fancybox-media.js"></script>
   <script src="<?php echo JSLIB_URL?>jquery.isotope.js"></script>


   <!-- javascript plugin -->
   <script src="<?php echo JSLIB_URL?>rs-plugin/js/jquery.themepunch.tools.min.js"></script>
   <script src="<?php echo JSLIB_URL?>rs-plugin/js/jquery.themepunch.revolution.min.js"></script>

   <!-- javascript core -->
   <script src="<?php echo JSLIB_URL?>theme-script.js"></script>
   <script src="<?php echo JSLIB_URL?>jquery.cookie.js"></script>
   <script src="<?php echo JSLIB_URL?>theme-switcher.js"></script>

   <script type="text/javascript">
      jQuery(document).ready(function($) { 
         Foundstrap.theme();

         // revolution slider configuration here
         $('.slideshow').revolution({
            delay:8000,
            startwidth:1080,
            startheight:520,
            hideThumbs:1,
            navigationType:"none",                  // bullet, thumb, none
            navigationArrows:"solo",                // nexttobullets, solo (old name verticalcentered), none
            navigationStyle:"square",               // round,square,navbar,round-old,square-old,navbar-old, or any from the list in the docu (choose between 50+ different item), custom
            navigationHAlign:"center",              // Vertical Align top,center,bottom
            navigationVAlign:"bottom",              // Horizontal Align left,center,right
            navigationHOffset:0,
            navigationVOffset:0,
            soloArrowLeftHalign:"left",
            soloArrowLeftValign:"center",
            soloArrowLeftHOffset:25,
            soloArrowLeftVOffset:0,
            soloArrowRightHalign:"right",
            soloArrowRightValign:"center",
            soloArrowRightHOffset:25,
            soloArrowRightVOffset:0,
            touchenabled:"on",                      // Enable Swipe Function : on/off
            onHoverStop:"on",                       // Stop Banner Timet at Hover on Slide on/off
            stopAtSlide:-1,                         // Stop Timer if Slide "x" has been Reached. If stopAfterLoops set to 0, then it stops already in the first Loop at slide X which defined. -1 means do not stop at any slide. stopAfterLoops has no sinn in this case.
            stopAfterLoops:-1,                      // Stop Timer if All slides has been played "x" times. IT will stop at THe slide which is defined via stopAtSlide:x, if set to -1 slide never stop automatic
            hideCaptionAtLimit:0,                   // It Defines if a caption should be shown under a Screen Resolution ( Basod on The Width of Browser)
            hideAllCaptionAtLilmit:0,               // Hide all The Captions if Width of Browser is less then this value
            hideSliderAtLimit:0,                    // Hide the whole slider, and stop also functions if Width of Browser is less than this value
            shadow:0,                               // 0 = no Shadow, 1,2,3 = 3 Different Art of Shadows  (No Shadow in Fullwidth Version !)
            fullWidth:"off",                        // Turns On or Off the Fullwidth Image Centering in FullWidth Modus
            fullScreen:"off"
         });
      });
   </script>
</body>
</html>