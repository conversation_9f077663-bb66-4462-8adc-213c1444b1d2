<?php

/******** START BOOTSTRAP *********/

//--- 1.  bootstrap

define('DS', DIRECTORY_SEPARATOR);

include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';

include_once LIB0_PATH . 'bootstrap.php';



//--- 2. start session

//startBeSession();



//--- 3. extract parsed variables

extract($_GET);	extract($_POST);



/*

//--- 4. instantiate DB

$myDb		= new sysDB;	$myDb->init();

//====>>>>> DB Connection Established after this line ====>>>>>



//--- 5. SM init

include_once LIB1_PATH . 'sm_init.php';

*/



//==== include language type

$langUrl = SF_URL;

if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}

define('LANG_URL',		$langUrl);







							

//--- 6. special library





$picCnt = 0;



?>

<!DOCTYPE html>



<html>

<head>

   <meta charset="utf-8">

   <meta http-equiv="X-UA-Compatible" content="IE=edge"> 

   <title>IT Maintenance บริการดูแลซ่อมบำรุงระบบคอมพิวเตอร์ network และserverครบวงจร</title>

   <meta name="description" content="IT Maintenance Service บริการดูแลระบบคอมพิวเตอร์ ซ่อมบำรุง และแก้ไขปัญหา NetworkและServer ด้วยทีมงานIT Support ผู้เชี่ยวชาญ พร้อมระบบ 24/7 Monitoring System ช่วยเช็ค และแก้ไขปัญหา บำรุงรักษาระบบคอมพิวเตอร์ให้ใช้งานได้อย่างดีเยี่ยม  และยืดอายุการใช้งานคอมพิวเตอร์ให้ใช้ได้ยาวนานขึ้น ">

   <meta name="keywords" content="IT Maintenance,IT Support,ดูแลคอมพิวเตอร์,บริการดูแลระบบคอมพิวเตอร์,ดูแลระบบคอมพิวเตอร์,รับดูแลคอมพิวเตอร์,รับดูแลระบบไอที,บริการดูแลระบบไอที,ซ่อมบำรุงคอมพิวเตอร์,แก้ปัญหาระบบคอมพิวเตอร์,IT Services,IT Outsourcing">

   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">







<?php echo getPgCss(); ?>  



<style>



      tr.rowTitle {

        margin: 2px;

        line-height: 12px;

      }

      tr.rowTitle td {

        background-color:  #dfdfdf;

        font-color: #000;

        font-size: 80%;

      }



      tr.row0 {

        margin: 2px;

        line-height: 12px;

      }

      tr.row0 td {

        background-color:  #fff;

        font-size: 80%;

      }



      tr.row1 {

        margin: 2px;

        line-height: 12px;

      }

      tr.row1 td {

        background-color:  #EAF4FB;

       font-size: 80%;

       }



</style>

</head>

<body>



   <!-- main-container -->

   <div id="main-container">



      <!--Header_section-->

         <?php include(INCLUDE_PATH . 'head.php');?>

      <!--Header_section-->  







      <!-- slideshow -->

         <?php include(INCLUDE_PATH . 'slider-services-it-support.php');?>

      <!-- slideshow end here -->



 

       <!-- container -->

      <section class="container">

         <div class="row">

            <h1>IT Maintenance Services</h1>

            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >

              <p>

                <?php echo $SITE_NAME;?>เป็นบริษัทIT Outsource ผู้ให้บริการดูแลระบบคอมพิวเตอร์ IT Maintenance Service บริการดูแลระบบคอมพิวเตอร์ ซ่อมบำรุง และแก้ไขปัญหา NetworkและServer ด้วยทีมงานIT Support ผู้เชี่ยวชาญ พร้อมระบบ 24/7 Monitoring System ช่วยเช็ค และแก้ไขปัญหาเป็นการบริการดูแลระบบคอมพิวเตอร์ให้ใช้งานได้อย่างเป็นปกติ เพื่อยืดอายุการใช้งานคอมพิวเตอร์  ให้ยาวนานขึ้น บริการ<u>ตรวจเช็ค</u>และ<u>ซ่อมแซม</u>ส่วนที่ชำรุดเพื่อให้การทำงานได้อย่างราบรื่น  โดยมีการคิดอัตราการบริการทั้งแบบ<u>รายเดือน</u>และ<u>รายปี</u> ตามความเหมาะสมกับการใช้งาน โดยมีทีมงานIT Support ที่มีความเชี่ยวชาญระดับมืออาชีพคอยดูแลเสมอ</p>

<img src="<?php echo IMG_URL?>services/it-support-specialist.jpg" width="700" height="160" alt="IT Maintenance" title="IT Maintenance " />              





              <p><br>

                นอกจากบริการด้าน IT Maintenance แล้ว เรายังจัดเตรียมบริการทางด้านไอทีแบบครบวงจรไม่ว่าจะเป็นจัดจำหน่ายอุปกรณ์ คอมพิวเตอร์ (Hardware) ซอฟท์แวร์ (Software) รวมถึงการออกแบบ, ติดตั้งระบบคอมพิวเตอร์และเน็ตเวิร์ค ด้วยบริการดูแลระบบคอมพิวเตอร์ 

                (IT Outsource) จากเทคสเปซ เราให้บริการครอบคลุมด้วยทีมงานที่มีความเชี่ยวชาญ อย่างมืออาชีพ 



              </p>





              <h2>รายละเอียดบริการ IT Maintenance Service</h2>

               <ul class="me-list list-circle-check">

                 <li>บริการดูแลระบบคอมพิวเตอร์  แก้ไขปัญหาอุปกรณ์ฮาร์ดแวร์ทั้งเครื่อง Server, Computer, Printer และอุปกรณ์ต่อพวงต่างๆ  เป็นต้น </li>

                 <li>บริการซ่อมแซมอุปกรณ์คอมพิวเตอร์ ( PC/Notebooks Maintenance) </li>

                 <li>ออกแบบ ปรับปรุง และวางระบบคอมพิวเตอร์เน็ตเวิร์ค  ภายในองค์กรหรือเชื่อมต่อระหว่างองค์กร </li>

                 <li>ให้คำปรึกษา แก้ปัญหาทางโทรศัพท์ </li>

                 <li>บริการแก้ปัญหาผ่านระบบ Remote Access </li>

                 <li>จัดเจ้าหน้าที่ดูแลระบบประจำพื้นที่เพื่อความสะดวกและความต่อเนื่องในการให้บริการ </li>

                 <li>บริการจัดหาอุปกรณ์คอมพิวเตอร์ ทั้งHardwareและSoftware  ที่คุณภาพในราคาถูก </li>

                 <li>ให้คำปรึกษาในการปรับปรุงระบบ Server, Network และ  Security </li>

               </ul>

              <h2>โปรแกรม IT Maintenance</h2>

<?php include('it-support-plan.php');?>

              <h2>ทำไมต้องเลือก IT Maintenance Service จาก <?php echo $SITE_NAME;?></h2>

                <li>เพราะเรามีทีมงาน IT Support & Maintenance และ Engineer ที่มีผู้เชี่ยวชาญและประสบการ์ณสูงด้านระบบคอมพิวเตอร์, Network, Server</li>

                <li>การSupport ที่รวดเร็วกว่า โดยระบบ Remote Access สามารถแก้ปัญหาให้ท่านได้ทุกเวลา</li>

                <li>ดูแลระบบอย่างมีคุณภาพ โดยทีมงานผู้เชี่ยวชาญ ในราคาที่เหมาะสม สามารถตรวจสอบได้</li>

              <p>สอบถามหรือฝากข้อมูลเพื่อรับคำแนะนำและประเมินราคา  ฟรี!!!  พร้อมรับส่วนลดพิเศษ โทร. 02 381-9075 หรือ <a href="https://www.bkk-it.com/coy/contact-us.html">คลิกที่นี่</a></p>

               <h3>ขั้นตอนการบริการดูแลระบบคอมพิวเตอร์</h3>

               

               <p> <u>1. เริ่มการบริการดูแลระบบคอมพิวเตอร์</u> โดยมีเจ้าหน้าที่ สำรวจระบบว่ามีการวางระบบคอมพิวเตอร์ Networkอย่างไร เพื่อเริ่มทำแผนการดูแลระบบคอมพิวเตอร์ต่อไป  โดยกระบวณการ การตรวจสอบมีดังนี้</p>

<ul class="me-list list-circle-check">

                 <li>สำรวจจำนวนอุปกรณ์คอมพิวเตอร์,ตำแหน่งเครื่อง,คุณสมบัติหรือเสปคเครื่อง,โปรแกรมภายใน,และข้อควรระวังต่างๆ</li>

                 <li>ตรวจสอบอุปกรณ์คอมพิวเตอร์ที่ต้องซ่อมบำรุง เช่น  อุปกรณ์ที่จำเป็นต้องเปลี่ยน,อุปกรณ์ที่ยังพอใช้ได้หรือใกล้หมดสภาพ  และจะแจ้งให้ทราบ</li>

                 <li>ตรวจสอบและบันทึกโปรแกรม รวมถึงSoftwareที่อยู่ในแต่ละเครื่อง</li>

                 <li>จัดทำทะเบียนHardwareและsoftwareทั้งหมด  เพื่อง่ายต่อการตรวจสอบและแก้ไขปัญหา</li>

                 <li>จัดทำรายการที่ต้องแก้ไขโดยเร่งด่วนและรายการที่ยังไม่เร่งด่วน  รวมถึงวิธีการแก้ไขปัญหา  ซึ่งทางพนักงานจะแจ้งต่อผู้รับผิดชอบภายในบริษัทของท่านทราบ เพื่ออนุมัติ  และดำเนินการแก้ไขปัญหาต่อไป</li>

              </ul>

<p><u>2.ขั้นตอนการปรับปรุงแก้ไข</u></p>

<p>หลังดำเนินตรวจสอบปัญหาและได้รับอนุมัติแล้ว ทางบริษัทจะส่งทีมงาน  IT Support เพื่อเข้าไปแก้ไขตามที่ได้แจ้งไว้เบื้องต้น  ซึ่งการดำเนินการปรับระบบต่างๆ ซึ่งใช้ระยะเวลาประมาณ 2 เดือน</p>

<p><u>3.การดูแลระบบคอมพิวเตอร์หลังจากการปรับปรุง</u> ภายหลังจากการปรับปรุงแก้ไขสมบูรณ์</p>

<p>- ดูแลและแก้ไขปัญหาเล็กน้อย  โดยจะมีการเฝ้าระวังกรณีที่มีความเสี่ยงที่อาจที่เกิดขึ้น</p>

<p>- คอยช่วยเหลือและแก้ไขปัญหาคอมพิวเตอร์  จากผู้ใช้งาน ตลอดเวลา </p>

  <p><u>โดยหากผู้ใช้งานมีปัญหาเร่งด่วน  สามารถให้ความช่วยเหลือโดย</u></p>

              <ul class="me-list list-circle-check">

                   <li>ทางโทรศัพท์</li>

                   <li>ทาง E-mail</li>

                   <li>Online Support (Remote Access)</li>

                   <li>Onsite Support </li>

                 </ul>

              <p>สอบถามรายละเอียดเพิ่มเติมบริการ IT Maintenanceได้ที 02 381-9075</p>

               <!--Header_section-->           </div>

            <?php include('menu-services.php');?>

            <!--Header_section--> 



         </div>   

      </section>

      <!-- container end here -->



      <?php echo getPgFootJs(); ?>



      <!--Header_section-->

         <?php include(INCLUDE_PATH . 'foot.php');?>

      <!--Header_section--> 



   </div>

   <!-- main-container end here -->



</body>

</html>