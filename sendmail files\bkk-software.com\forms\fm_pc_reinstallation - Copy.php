<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);


							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';


//--- 8. local var -----
$picCnt = 0;


$submitMsg = '';



if($submitFlag == "submitForm"){
  $strTo = "<EMAIL>";
  $strSubject = "=?UTF-8?B?".base64_encode("IT request installation for ".$_POST["com_name"]." ")."?=";
  $strHeader = "MIME-Version: 1.0' . \r\n";
  $strHeader .= "Content-type: text/html; charset=utf-8\r\n";
  $strHeader .= "From: <EMAIL>";
  $appList = "";
  for( $i = 1; $i <= 7; $i++ ) {
    $app = 'app' . $i;
    if( isset($_POST[$app]) ){
      $appList .= $_POST[$app] . " / ";
    }
  }   
  $msg = ' <html><head>
<style type="text/css">
.myTable { background-color:#FFFFFF;border-collapse:collapse; }
.myTable th { background-color:#585858;color:white; }
.myTable td, .myTable th { padding:5px;border:1px solid #000000; }
</style>
<style type="text/css">
.myStaff { background-color:#00000;border-collapse:collapse; }
.myStaff th { background-color:#000000;color:white; }
.myStaff td, .myStaff th { padding:5px;border:1px solid #000000; }
.style3 {font-size: large; font-weight: bold; }
</style>
<style type="text/css" media="screen">
body {
  font: .75em/1.5 "Lucida Grande", "Lucida Sans Unicode", helvetica, verdana, arial, sans-serif;
    margin: 0 auto;
    padding: 0 10px;
    width: 610px;
}
strong {
  background: #ffc;
}
</style></head><body>
<th><p class="style3">New PC Installation Request</p></th>
<table width="700" class="myTable">
<tr><th colspan="2" align="left"><p>Company Installation</p></th></tr>
<tr><td colspan="2">' . $_POST[com_name].  ' (Request by K. ' . $_POST[request]. ') 
<br>Contact +66 '. $_POST[contact].' &nbsp;E-mail : '. $_POST[email].' </td> </tr>
<tr><th colspan="2" align="left"><p>Installation Details </p></th></tr>
<tr><td width="150">Operation System :</td>
<td width="550"> ' . $_POST[os].  '</td></tr>
<tr>
  <td>Microsoft Office :</td>
  <td> ' . $_POST[office].  ' </td>
</tr>
<tr>
  <td> Default Language :</td>
  <td> ' . $_POST[lang]. ' </td>
</tr>
<tr>
  <td> Preferred Username :</td>
  <td> ' . $_POST[username]. ' </td>
</tr>
<tr>
  <td> Preferred password :</td>
  <td> ' . $_POST[password]. ' </td>
</tr>
<tr>
  <td>Application :</td>
  <td> ' . $appList. ' </td>
</tr>
  <tr>
    <th colspan="2" align="left"><p>Additional Note </p></th>
  </tr>
  <tr>
    <td colspan="2"> ' . nl2br($_POST[notes]).  ' </td>
  </tr></table>
<th><p class="style3">For Internal Use. Pre-installation Check</p></th>
<table width="700" class="myStaff">
  <tr><td width="150">&nbsp;Operation System :</td> <td width="550">(&nbsp; ) '. $_POST[os].' </td></tr>
  <tr><td>&nbsp;Microsoft Office :</td><td> (&nbsp; ) '. $_POST[office].' </td></tr>
  <tr><td>&nbsp;Default Language :</td><td> (&nbsp; ) '. $_POST[lang].' </td></tr>
  <tr><td valign="top">&nbsp;Application : <br> <td> (&nbsp; )  ' . $appList. ' </td></tr>
  
  <tr><td colspan="2">&nbsp;Note :<br><br><br><br><br><br><br></td></tr>
</table>
</body>
</html>
';
  
  $flgSend = @mail($strTo,$strSubject,$msg,$strHeader);
  if($flgSend){
    $submitMsg = "Your submission is completed. We will contact you once the installation is completed. Thank you.";  
  } else {
    $submitMsg = "An error has occur while submitting this form. Please contact the administrator for assistance.";     
  }

  
}

?>
<!doctype html>
<html lang="en">
  <head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8">
    <title>PC Re-installation Form</title>
    <link href="css/forms.css" rel="stylesheet" type="text/css">
    <style type="text/css" media="screen">
body {
	font: .75em/1.5 "Lucida Grande", "Lucida Sans Unicode", helvetica, verdana, arial, sans-serif;
  	margin: 0 auto;
  	padding: 0 10px;
  	width: 610px;
}
strong {
  background: #ffc;
}
	.style1 {font-size: large}
    </style>
</head>
	<body>
    <p>&nbsp;</p>
    <table width="790" border="0" cellspacing="0" cellpadding="0">
      <tr>
        <td height="66"><img src="<?php echo IMG_URL?>techspace_logo_m.gif"></td>
      </tr>
    </table>
    <h1 class="style1">PC Re-Installation Form</h1>
    <form id="newpc" name="newpc" method="post" action="fm_pc_reinstallation.php">
      <p>Complete the below form and allow up to 6 hours for our engineer to complete the re-installation upon receiving the PC/laptop.</p>
      <fieldset>
        <legend><span style="font-size: 14px; font-weight:bold">Contact Information</span></legend>
      <div>
      <table width="485" border="0">
          <tr>
            <td width="170">Company Name: </td>
            <td width="305"><input type="text" name="com_name" size="40"></td>
          </tr>
          <tr>
            <td>Requester Name:</td>
            <td><input type="text" name="request" size="40"></td>
          </tr>
          <tr>
            <td>Requester Contact:</td>
            <td><input type="text" name="contact" size="40"></td>
          </tr>
          <tr>
            <td>Requester E-mail: </td>
            <td><input type="text" name="email" size="40"></td>
          </tr>
        </table>
      </div>
      </fieldset>
      <p>&nbsp;</p>
      <fieldset>
        <legend>Machine Details </legend>
      <div class="inline">
        <table width="514" border="0">
          <tr>
            <td width="190">Machine ID : 
            <input type="text" name="machine"></td>
            <td width="190">Username
            : 
            <input type="text" name="username"></td>
            <td width="120"><p>&nbsp;</p>            </td>
          </tr>
          <tr>
            <td colspan="3" align="left" valign="top">Additional Note :            </td>
          </tr>
        </table><textarea id="textarea2" name="textarea2" maxlength="250"></textarea>
        * E.g. Operation System, Genuine, Backup, UserData, Application, Anitivirus, Mapdrive or etc. </p>
      </div>  
      </fieldset>
      <fieldset>
      <legend>Re-Installation Details</legend>
      <div class="inline">
        <table width="512" border="0">
          <tr>
            <td width="155">New Operation System</td>
            <td width="10">:</td>
            <td width="333"><select id="select" name="os">
                <option value="">Please Select...</option>
                <option value="Windows 8.1 64 bit">Windows 8.1 64 bit</option>
                <option value="Windows 8.1 Pro 64 bit">Windows 8.1 Pro 64 bit</option>
                <option value="Windows 8 64 bit">Windows 8 64 bit</option>
                <option value="Windows 8 Pro 64 bit">Windows 8 Pro 64 bit</option>
                <option value="Windows 7 Home Basic 64 Bit">Windows 7 Home Basic 64 Bit</option>
                <option value="Windows 7 Home Premium 64 Bit">Windows 7 Home Premium 64 Bit</option>
                <option value="Windows 7 Professional 64 Bit">Windows 7 Professional 64 Bit</option>
                <option value="Windows 7 Ultimate 64 Bit">Windows 7 Ultimate 64 Bit</option>
                <option value="Windows 8.1 32 bit">Windows 8.1 32 bit</option>
                <option value="Windows 8.1 Pro 32 bit">Windows 8.1 Pro 32 bit</option>
                <option value="Windows 8 32 bit">Windows 8 32 bit</option>
                <option value="Windows 8 Pro 32 bit">Windows 8 Pro 32 bit</option>
                <option value="Windows 7 Home Basic 32 Bit">Windows 7 Home Basic 32 Bit</option>
                <option value="Windows 7 Home Premium 32 Bit">Windows 7 Home Premium 32 Bit</option>
                <option value="Windows 7 Professional 32 Bit">Windows 7 Professional 32 Bit</option>
                <option value="Windows 7 Ultimate 32 Bit">Windows 7 Ultimate 32 Bit</option>
                <option value="Windows XP Professional">Windows XP Professional</option>
            </select></td>
          </tr>
          <tr>
            <td>Microsoft Office</td>
            <td>:</td>
            <td width="333"><select id="select2" name="office">
                <option value="">Please Select...</option>
                <option value="Professional 2013">Professional 2013</option>
                <option value="Home and Business 2013">Home and Business 2013</option>
                <option value="Professional Plus 2013">Professional Plus 2013</option>
                <option value="Professional 2010">Professional 2010</option>
                <option value="Home and Business 2010">Home and Business 2010</option>
                <option value="Professional 2007">Professional 2007</option>
                <option value="Small Business 2007">Small Business 2007</option>
                <option value="Professional Plus 2007">Professional Plus 2007</option>
            </select></td>
          </tr>
          <tr>
            <td height="34">Default Language</td>
            <td>:</td>
            <td width="333"><select id="select3" name="lang">
                <option value="English">English</option>
                <option value="Thai">Thai</option>
            </select></td>
          </tr>
          <tr>
            <td>Application :  </td>
            <td colspan="3"><input id="post[teamviewer]" name="app1" type="checkbox" value="Teamviewer">
			<label for="post[teamviewer]">Teamviewer</label>
			<input id="post[skype]" name="app2" type="checkbox" value="Skype">
			<label for="post[skype]">Skype</label>
			<input id="post[line]" name="app3" type="checkbox" value="Line">
			<label for="post[line]">Line</label></td>
		  </tr>
          <tr>
            <td>&nbsp;</td>
            <td colspan="3"><input id="post[firefox]" name="app4" type="checkbox" value="Firefox">
			<label for="post[firefox]">Mozilla Firefox</label>
			<input id="post[chrome]" name="app5" type="checkbox" value="Chrome">
			<label for="post[chrome]">Google Chrome</label></td>
          </tr>
          <tr>
            <td>&nbsp;</td>
            <td colspan="3"><input id="post[winrar]" name="app6" type="checkbox" value="WinRAR">
                <label for="post[winrar]">Winrar</label>
                <input id="post[adobe]" name="app7" type="checkbox" value="Adobe Reader">
                <label for="post[adobe]">Adobe Reader (PDF Viewer) </label></td>
          </tr>
        </table>
      </div>
      <div>
        <label for="post_content">Additional Note </label>
        <p>Enter additional requirement or preferences (E.g. other application or e-mail configuration)
          <textarea id="textarea" name="content" maxlength="250"></textarea>
        </p>
      </div>
      </fieldset>
      <p>Thank you for your submission. We will contact you when the installation is completed. </p>
      <p>
        <input type="hidden" name="submitFlag" value="submitForm">
        <input type="submit" value="Submit">
        or
        <a href="#">Cancel</a>
      </p>
  </form>
 </body>
</html>