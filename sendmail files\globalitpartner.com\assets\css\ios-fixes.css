/**
 * iOS-specific fixes for the website
 * Particularly focused on fixing preloader issues on iOS devices
 */

/* Target iOS devices specifically */
.ios-device #preloader,
html.ios-device #preloader,
body.ios-device #preloader {
  animation: hidePreloader 5s forwards !important; /* Shorter timeout for iOS */
}

/* Force hide preloader after animation */
.ios-device #preloader.isdone,
html.ios-device #preloader.isdone,
body.ios-device #preloader.isdone {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  z-index: -1 !important;
  pointer-events: none !important;
}

/* Force hide loading text after animation */
.ios-device .loading.isdone,
html.ios-device .loading.isdone,
body.ios-device .loading.isdone {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  z-index: -1 !important;
  pointer-events: none !important;
}

/* Ensure content is visible even if preloader fails */
.ios-device #preloader:after,
.ios-device #preloader:before {
  animation: hidePreloaderElements 5s forwards !important;
}

/* Fix for the blue Pace.js progress bar that gets stuck on iOS */
.ios-device .pace,
html.ios-device .pace,
body.ios-device .pace {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  z-index: -999 !important;
  pointer-events: none !important;
  height: 0 !important;
  width: 0 !important;
  overflow: hidden !important;
  position: absolute !important;
  top: -9999px !important;
  left: -9999px !important;
}

.ios-device .pace-progress,
html.ios-device .pace-progress,
body.ios-device .pace-progress {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  width: 0 !important;
  max-width: 0 !important;
  overflow: hidden !important;
}

/* Force hide Pace.js elements after a delay */
.ios-device .pace,
.ios-device .pace-progress {
  animation: hidePaceElements 3s forwards !important;
}

@keyframes hidePaceElements {
  0%,
  50% {
    opacity: 1;
    visibility: visible;
  }
  100% {
    opacity: 0;
    visibility: hidden;
    display: none;
    height: 0;
    width: 0;
    max-width: 0;
  }
}

@keyframes hidePreloaderElements {
  0%,
  70% {
    height: 50%;
  }
  100% {
    height: 0;
  }
}
