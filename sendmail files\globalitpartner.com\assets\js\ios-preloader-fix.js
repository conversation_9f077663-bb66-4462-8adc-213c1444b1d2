/**
 * iOS-specific preloader fix
 * This script helps ensure the preloader is properly hidden on iOS devices
 */
(function () {
  // Detect iOS devices
  function isIOS() {
    return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
  }

  // If this is an iOS device, add special handling
  if (isIOS()) {
    // Add a class to the body for iOS-specific CSS
    document.documentElement.classList.add("ios-device");

    // Function to hide all preloader elements including Pace.js
    function hideAllPreloaderElements() {
      // Hide main preloader
      var preloader = document.getElementById("preloader");
      var loading = document.querySelector(".loading");

      if (preloader) {
        preloader.classList.add("isdone");
      }

      if (loading) {
        loading.classList.add("isdone");
      }

      // Hide Pace.js elements - specifically target the blue progress bar
      var paceElements = document.querySelectorAll(".pace, .pace-progress");
      paceElements.forEach(function (element) {
        if (element) {
          // Apply multiple techniques to ensure it's hidden
          element.style.display = "none";
          element.style.visibility = "hidden";
          element.style.opacity = "0";
          element.style.height = "0";
          element.style.width = "0";
          element.style.maxWidth = "0";
          element.style.overflow = "hidden";
          element.style.position = "absolute";
          element.style.top = "-9999px";
          element.style.left = "-9999px";
          element.style.zIndex = "-999";

          // Also try to remove it from the DOM if possible
          if (element.parentNode) {
            try {
              element.parentNode.removeChild(element);
            } catch (e) {
              // Ignore errors if we can't remove it
            }
          }
        }
      });
    }

    // Force hide preloader after a short delay
    setTimeout(hideAllPreloaderElements, 3000); // 3 seconds

    // Try again after a bit longer to catch any stragglers
    setTimeout(hideAllPreloaderElements, 5000); // 5 seconds

    // Add a window load event handler as another fallback
    window.addEventListener("load", function () {
      setTimeout(hideAllPreloaderElements, 1000); // 1 second after window load
      setTimeout(hideAllPreloaderElements, 2000); // 2 seconds after window load
    });

    // Also try on DOMContentLoaded
    document.addEventListener("DOMContentLoaded", function () {
      setTimeout(hideAllPreloaderElements, 1000); // 1 second after DOM ready
    });

    // One final attempt after user interaction
    document.addEventListener(
      "touchstart",
      function () {
        setTimeout(hideAllPreloaderElements, 500); // 0.5 seconds after first touch
      },
      { once: true }
    ); // Only trigger once
  }
})();
