<!DOCTYPE html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html lang="en" class="no-js"> <!--<![endif]-->
<head>
   <meta charset="utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta name="keywords" content="corporate, minimalist, html5, responsive, simple" />
   <meta name="description" content="Enix - Minimalist Business Template">
   <meta name="indonez" content="indonez.com">

   <title>Enix - Minimalist Business Template</title>
   
   <!-- retina Bookmark Icon -->
   <link rel="apple-touch-icon-precomposed" href="apple-icon.png" />

   <!-- CSS -->
   <link href="css/foundstrap.css" rel="stylesheet" />
   <link href="css/font-awesome.min.css" rel="stylesheet" />

   <!--[if (lt IE 9) & (!IEMobile)]>
      <script src="https://cdnjs.cloudflare.com/ajax/libs/respond.js/1.4.2/respond.min.js"></script>
   <![endif]-->
   
   <!-- CSS Plugin -->   
   <link href="js/rs-plugin/css/settings.css" rel="stylesheet" media="screen">

   <!-- theme Stylesheet -->
   <link href="css/style.css" rel="stylesheet">
   <link href="css/theme-responsive.css" rel="stylesheet">   
   
   <!-- theme Option -->
   <link href="css/theme-switcher.css" rel="stylesheet" />
   <link href="css/theme/default.css" id="theme" rel="stylesheet">

   <!-- favicon -->
   <link rel="shortcut icon" href="img/favicon.ico">
   
   <!-- modernizr -->
   <script src="js/modernizr.js"></script>
   
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!-- headaer -->
      <header id="me-header" class="header-version3">
         
         <!-- header information  -->
         <div class="header-info-container">
            <div class="row">
               <div class="large-12 column">
                  <div class="header-info-left">   
                     <ul class="social-list social-bg social-header">
                        <li><a class="facebook-bgcolor" href="#"><i class="fa fa-facebook"></i></a></li>
                        <li><a class="twitter-bgcolor" href="#"><i class="fa fa-twitter"></i></a></li>
                        <li><a class="googleplus-bgcolor" href="#"><i class="fa fa-google-plus"></i></a></li>
                        <li><a class="instagram-bgcolor" href="#"><i class="fa fa-instagram"></i></a></li>
                        <li><a class="linkedin-bgcolor" href="#"><i class="fa fa-linkedin"></i></a></li>
                        <li><a class="dribbble-bgcolor" href="#"><i class="fa fa-dribbble"></i></a></li>
                     </ul>
                  </div>
                  <div class="header-info-right"> 
                     <ul class="action-header">
                        <li><i class="fa fa-phone icon-margin"></i> 1400-333-222</li>
                        <li><i class="fa fa-envelope icon-margin"></i> <EMAIL></li>
                     </ul>
                  </div>
               </div>      
            </div>
         </div>
         <!-- header infomration end here -->

         <div class="row">
            <div class="large-12 column">
               <!-- logo container -->
               <div class="logo-container">
                  <a href="index.html">
                     <img class="retina" src="img/logo.png" alt="Logo">
                  </a>
               </div> 
               <!-- logo container end here -->
               
               <!-- menu navigation -->
               <div class="navigation-container">
                  
                  <div class="form-search-trigger">
                     <i class="fa fa-search"></i>

                     <form action="#" class="form-search">
                        <div class="input-group-placeholder addon-right">
                           <input name="search" type="text" class="form-control" placeholder="Search here..">
                           <span class="input-group-icon"><i class="fa fa-search"></i></span>
                        </div>
                     </form>
                  </div>

                  <nav class="menu-container">
                     <ul id="menu" class="sm me-menu">
                        <li class="active"><a href="index.html">Home</a>
                           <ul>
                              <li><a href="index-version2.html">Home - Version 2</a></li>
                              <li class="active"><a href="index-version3.html">Home - Version 3</a></li>
                           </ul>
                        </li>
                        <li><a href="#">Pages</a>
                           <ul>
                              <li><a href="about.html">About Us</a></li>
                              <li><a href="services.html">Services</a></li>
                              <li><a href="testimonials.html">Testimonials</a></li>
                              <li><a href="pricing-plan.html">Pricing Plan</a></li>
                              <li><a href="team.html">Our Team</a></li>
                              <li><a href="single.html">Blog Post</a></li>
                              <li><a href="portfolio-single.html">Portfolio Single</a></li>
                              <li><a href="faq.html">FAQ Page</a></li>
                              <li><a href="sitemap.html">Sitemap</a></li>
                              <li><a href="404.html">404 Error</a></li>
                           </ul>
                        </li>
                        <li><a href="#">Shortcode</a>
                           <ul>
                              <li><a href="column.html">Column</a></li>
                              <li><a href="button-list.html">Button &amp; List</a></li>
                              <li><a href="icon-list.html">Icon List</a></li>
                              <li><a href="icon-use.html">Icon use</a></li>
                              <li><a href="typography.html">Typography</a></li>
                              <li><a href="table.html">Table</a></li>
                              <li><a href="panel-promo.html">Panel &amp; Promobox</a></li>
                              <li><a href="tab-accordion.html">Tab &amp; Accordion</a></li>
                              <li><a href="alert-progress.html">Alert &amp; Progress</a></li>
                           </ul>
                        </li>
                        <li><a href="#">Portfolio</a>
                           <ul>
                              <li><a href="portfolio-2column.html">Portfolio 2 Column</a></li>
                              <li><a href="portfolio-3column.html">Portfolio 3 Column</a></li>
                              <li><a href="portfolio-4column.html">Portfolio 4 Column</a></li>
                              <li><a href="#">Dropdown Level 1</a>
                                 <ul>
                                    <li><a href="#">Dropdown Level 2</a></li>
                                    <li><a href="#">Dropdown Level 2</a></li>
                                    <li><a href="#">Dropdown Level 2</a>
                                       <ul>
                                          <li><a href="#">Dropdown Level 3</a></li>
                                          <li><a href="#">Dropdown Level 3</a></li>
                                          <li><a href="#">Dropdown Level 3</a></li>
                                       </ul>
                                    </li>
                                 </ul>
                              </li>
                           </ul>
                        </li>
                        <li><a href="blog.html">Blog</a></li>
                        <li><a href="contact.html">Contact</a></li>
                     </ul>
                  </nav>
               </div>
            </div>
         </div>
      </header>
      <!-- header end here -->  
      
      <!-- slideshow -->
      <div class="container" id="slideshow-container">
         <div class="slideshow">
            <ul>
               <li data-transition="fade" data-masterspeed="1500">
                  <img src="img/slideshow/slide3-bg.png" alt="slider background">

                  <div class="tp-caption customin customout tp-resizeme slider-title slide3-title1" 
                     data-x="-15" data-hoffset="0"
                     data-y="124" data-voffset="0"
                     data-customin="x:0;y:0;z:0;rotationX:0;rotationY:0;rotationZ:0;scaleX:0;scaleY:0;skewX:0;skewY:0;opacity:0;transformPerspective:600;transformOrigin:50% 50%;"
                     data-customout="x:0;y:0;z:0;rotationX:0;rotationY:0;rotationZ:0;scaleX:0;scaleY:0;skewX:0;skewY:0;opacity:0;transformPerspective:600;transformOrigin:50% 50%;"
                     data-speed="1500"
                     data-start="1500"
                     data-easing="Power3.easeInOut"
                     data-splitin="chars"
                     data-splitout="chars"
                     data-elementdelay="0.08"
                     data-endelementdelay="0.08"
                     data-end="8500"
                     data-endspeed="500">
                     Makes You
                  </div>
                  <div class="tp-caption customin customout tp-resizeme slider-title slide3-title2" 
                     data-x="-15" data-hoffset="0"
                     data-y="180" data-voffset="0"
                     data-customin="x:0;y:0;z:0;rotationX:0;rotationY:0;rotationZ:0;scaleX:0;scaleY:0;skewX:0;skewY:0;opacity:0;transformPerspective:600;transformOrigin:50% 50%;"
                     data-customout="x:0;y:0;z:0;rotationX:0;rotationY:0;rotationZ:0;scaleX:0;scaleY:0;skewX:0;skewY:0;opacity:0;transformPerspective:600;transformOrigin:50% 50%;"
                     data-speed="1500"
                     data-start="2500"
                     data-easing="Power3.easeInOut"
                     data-splitin="chars"
                     data-splitout="chars"
                     data-elementdelay="0.08"
                     data-endelementdelay="0.08"
                     data-end="8500"
                     data-endspeed="500">
                     Confident
                  </div>
                  <div class="tp-caption lfl ltl slider-list slide3-caption1" 
                     data-x="0" 
                     data-y="260"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="4000" 
                     data-end="8000" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                     Complete HTML5 Pages for Business
                  </div>
                  <div class="tp-caption lfl ltl slider-list slide3-caption2" 
                     data-x="0" 
                     data-y="309"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="4500" 
                     data-end="8250" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                     Powered by Revolution Slider ($15 value)
                  </div>
                  <div class="tp-caption lfl ltl slider-list slide3-caption3" 
                     data-x="0" 
                     data-y="359"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="5000" 
                     data-end="8500" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                     Responsive and Retina display ready
                  </div>
               </li>

               <li data-transition="fade" data-masterspeed="1000">
                  <img src="img/slideshow/slide1-bg.png" alt="slider background">
                  
                  <!-- caption slider 1 -->
                  <div class="tp-caption lfb ltb slide1-image" 
                     data-x="540" 
                     data-y="55"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="1750" 
                     data-end="8500" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                     <img src="img/slideshow/slide1-img1.png" alt="slider image">
                  </div>
                  <div class="tp-caption customin customout tp-resizeme slider-title slide1-title" 
                     data-x="-17" data-hoffset="0"
                     data-y="218" data-voffset="0"
                     data-customin="x:0;y:0;z:0;rotationX:0;rotationY:0;rotationZ:0;scaleX:0;scaleY:0;skewX:0;skewY:0;opacity:0;transformPerspective:600;transformOrigin:50% 50%;"
                     data-customout="x:0;y:0;z:0;rotationX:0;rotationY:0;rotationZ:0;scaleX:0;scaleY:0;skewX:0;skewY:0;opacity:0;transformPerspective:600;transformOrigin:50% 50%;"
                     data-speed="1500"
                     data-start="2000"
                     data-easing="Power3.easeInOut"
                     data-splitin="chars"
                     data-splitout="chars"
                     data-elementdelay="0.08"
                     data-endelementdelay="0.08"
                     data-end="8500"
                     data-endspeed="500">
                     Get Ready For
                  </div>
                  <div class="tp-caption lfl ltb slider-caption slide1-caption" 
                     data-x="10" 
                     data-y="268"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="3500" 
                     data-end="8500" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                     Great Solution for Online Business
                  </div>
                  <div class="tp-caption lft ltt slider-separator" 
                     data-x="0" 
                     data-y="160"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="4000" 
                     data-end="8000" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                  </div>
                  <div class="tp-caption lfb ltb slider-separator" 
                     data-x="0" 
                     data-y="305"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="4000" 
                     data-end="8000" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                  </div>
               </li>
               
               <li data-transition="fade" data-masterspeed="1500">
                  <img src="img/slideshow/slide2-bg.jpg" alt="slider background">
                  
                  <!-- caption slider 2 -->
                  <div class="tp-caption customin customout tp-resizeme slider-title slider2-title" 
                     data-x="305" data-hoffset="0"
                     data-y="211" data-voffset="0"
                     data-customin="x:0;y:0;z:0;rotationX:0;rotationY:0;rotationZ:0;scaleX:0;scaleY:0;skewX:0;skewY:0;opacity:0;transformPerspective:600;transformOrigin:50% 50%;"
                     data-customout="x:0;y:0;z:0;rotationX:0;rotationY:0;rotationZ:0;scaleX:0;scaleY:0;skewX:0;skewY:0;opacity:0;transformPerspective:600;transformOrigin:50% 50%;"
                     data-speed="1500"
                     data-start="2000"
                     data-easing="Power3.easeInOut"
                     data-splitin="chars"
                     data-splitout="chars"
                     data-elementdelay="0.08"
                     data-endelementdelay="0.08"
                     data-end="8500"
                     data-endspeed="500">
                     Get Ready For
                  </div>
                  <div class="tp-caption lfb ltb slider-caption slider2-caption" 
                     data-x="333" 
                     data-y="262"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="3500" 
                     data-end="8500" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                     Great Solution for Online Business
                  </div>
                  <div class="tp-caption lfb ltb slider2-button" 
                     data-x="444" 
                     data-y="320"
                     data-speed="1500" 
                     data-endspeed="1500" 
                     data-start="4000" 
                     data-end="8250" 
                     data-easing="easeOutExpo" 
                     data-endeasing="easeInExpo">
                     <a class="slider-button" href="#">Purchase Now 
                        <i class="fa fa-angle-double-right"></i>
                     </a>
                  </div>
               </li>
            </ul>
         </div>
      </div>
      <!-- slideshow end here -->

      <!-- container -->
      <section class="container container-gray container-border no-padding">
         <div class="row">
            <div class="large-12 column">
               <div class="promo-box no-border gap" data-gap-top="28" data-gap-bottom="30">
                  <div class="promo-text">
                     <h3 class="heading-light">Develop Beautiful Masterpiece.</h3>
                     <p class="lead-half">Voluptale sequ nesciunt exercitationem ullam corporis suscipit.</p>
                  </div>
                  <div class="promo-action">
                     <a class="button half-block radius" href="#">Develop with Enix 
                        <i class="fa fa-angle-double-right"></i>
                     </a>
                  </div>
               </div>
            </div>
         </div>
      </section>
      <!-- container end here -->

      <!-- container -->
      <section class="container">
         <div class="row">
            <div class="large-3 medium-6 column">   
               <div class="me-box-icon large offset-box me-animate" data-animate="fadeIn">
                  <div class="icon-container">
                  <div class="icon-shape circle">  
                        <i class="linea-basic_lightbulb"></i>
                     </div>
                  </div>
                  <div class="text-container">
                     <h5 class="title-box text-uppercase">Collect Ideas</h5>
                     <p>Voluptate accusantium doloremque laudantiuse totam rem aperiam.</p>
                  </div>
               </div>
            </div>
            <div class="large-3 medium-6 column">   
               <div class="me-box-icon large offset-box me-animate" data-animate="fadeIn" data-animate-delay="250">
                  <div class="icon-container">
                  <div class="icon-shape circle">  
                        <i class="linea-ecommerce_graph2"></i>
                     </div>
                  </div>
                  <div class="text-container">
                     <h5 class="title-box text-uppercase">Data Analysis</h5>
                     <p>Voluptate accusantium doloremque laudantiuse totam rem aperiam.</p>
                  </div>
               </div>
            </div>
            <div class="large-3 medium-6 column">   
               <div class="me-box-icon large offset-box me-animate" data-animate="fadeIn" data-animate-delay="500">
                  <div class="icon-container">
                  <div class="icon-shape circle">  
                        <i class="linea-basic_hammer"></i>
                     </div>
                  </div>
                  <div class="text-container">
                     <h5 class="title-box text-uppercase">Project Develop</h5>
                     <p>Voluptate accusantium doloremque laudantiuse totam rem aperiam.</p>
                  </div>
               </div>
            </div>
            <div class="large-3 medium-6 column">   
               <div class="me-box-icon large offset-box me-animate" data-animate="fadeIn" data-animate-delay="750">
                  <div class="icon-container">
                  <div class="icon-shape circle">  
                        <i class="linea-basic_paperplane"></i>
                     </div>
                  </div>
                  <div class="text-container">
                     <h5 class="title-box text-uppercase">launch product</h5>
                     <p>Voluptate accusantium doloremque laudantiuse totam rem aperiam.</p>
                  </div>
               </div>
            </div>
         </div>
      </section>
      <!-- container end here -->
      
      <!-- container -->
      <section class="container container-gray container-border">
         <div class="row">
            <div class="large-6 medium-6 column me-animate" data-animate="fadeIn">
               <h3 class="heading-light">Are You Ready to Enjoy</h3>

               <p>Cras ultricies tortor sit amet tellus imperdiet ultrices. Phasellus ut porta orci, cursus vestibulum tellus. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus.</p>
               <p>Vivamus sed finibus elit. Sed vel leo fermentum, efficitur sapien et, tempor dui. Sed lacus purus, mattis quis metus sit amet, efficitur faucibus erat. Nam nec dolor vitae enim ultricies convallis ut sit.</p>
               
               <a class="button radius" href="#">Purchase Now
                  <i class="fa fa-angle-double-right"></i>
               </a>
            </div>
            <div class="large-6 medium-6 column me-animate" data-animate="fadeIn">
               <img class="image-right-margin" src="img/mockup-device.png" alt="image content">
            </div>
         </div>
      </section>
      <!-- container end here -->

      <!-- container -->
      <section class="container">
         <div class="row">
            <div class="large-12 column text-center me-animate" data-animate="fadeIn">
               <h3 class="heading-light">Choose your Monthly Plan.</h3>
               <p class="gap" data-gap-bottom="38">irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur</p>
            </div>

            <div class="large-12 column me-animate" data-animate="fadeIn">
               <ul class="large-block-grid-3 medium-block-grid-3 small-block-grid-1 no-wrap">
                  <li>
                     <div class="me-pricing">
                        <h5 class="pricing-title">Regular</h5>
                        <div class="pricing-price">
                           <span class="price">$</span>60<small>/ month</small>
                           <p>Best for starter teams</p> 
                        </div>                    
                        <ul class="pricing-content">
                           <li><strong>10GB</strong> Disk Space</li>
                           <li><strong>100GB</strong> Monthly Bandwidth</li>
                           <li><strong>20</strong> Email Accounts</li>
                           <li><strong>30</strong> subdomains</li>
                           <li><strong>SSL</strong> Encryption</li>
                           <li><strong>Payment</strong> Integration</li>
                        </ul>                    
                        <div class="pricing-button">
                           <a class="button half-block radius" href="#">Order Now
                              <i class="fa fa-angle-double-right"></i>
                           </a>
                        </div>
                     </div>
                  </li>
                  <li>
                     <div class="me-pricing featured-pricing black">
                        <h5 class="pricing-title">Plus +</h5>
                        <div class="pricing-price">
                           <span class="price">$</span>80<small>/ month</small>
                           <p>Best for starter teams</p> 
                        </div>                    
                        <ul class="pricing-content">
                           <li><strong>150GB</strong> Disk Space</li>
                           <li><strong>500GB</strong> Monthly Bandwidth</li>
                           <li><strong>100</strong> Email Accounts</li>
                           <li><strong>50</strong> subdomains</li>
                           <li><strong>SSL</strong> Encryption</li>
                           <li><strong>Payment</strong> Integration</li>
                        </ul>                     
                        <div class="pricing-button">
                           <a class="button black half-block radius" href="#">Order Now
                              <i class="fa fa-angle-double-right"></i>
                           </a>
                        </div>
                     </div>
                  </li>
                  <li>
                     <div class="me-pricing">
                        <h5 class="pricing-title">Enterprise</h5>
                        <div class="pricing-price">
                           <span class="price">$</span>100<small>/ month</small>
                           <p>Best for starter teams</p> 
                        </div>                    
                        <ul class="pricing-content">
                           <li><strong>Unlimited</strong> Disk Space</li>
                           <li><strong>Unlimited</strong> Monthly Bandwidth</li>
                           <li><strong>Unlimited</strong> Email Accounts</li>
                           <li><strong>Unlimited</strong> subdomains</li>
                           <li><strong>SSL</strong> Encryption</li>
                           <li><strong>Payment</strong> Integration</li>
                        </ul>                    
                        <div class="pricing-button">
                           <a class="button half-block radius" href="#">Order Now
                              <i class="fa fa-angle-double-right"></i>
                           </a>
                        </div>
                     </div>
                  </li>
               </ul>
            </div>

            <div class="large-12 column me-animate" data-animate="fadeIn">
               <div class="circle-separator">
                  <i class="fa fa-quote-left"></i>
               </div>
            </div>

            <div class="large-12 text-center column me-animate" data-animate="fadeIn">
               <h3>Our Happy Clients</h3>
               <ul class="client-list client-carousel large-block-grid-4 medium-block-grid-4 small-block-grid-2">
                  <li><a href="#"><img src="img/client-logo/client-logo1.png" alt="client logo 1"></a></li>
                  <li><a href="#"><img src="img/client-logo/client-logo2.png" alt="client logo 2"></a></li>
                  <li><a href="#"><img src="img/client-logo/client-logo3.png" alt="client logo 3"></a></li>
                  <li><a href="#"><img src="img/client-logo/client-logo4.png" alt="client logo 4"></a></li>
                  <li><a href="#"><img src="img/client-logo/client-logo5.png" alt="client logo 5"></a></li>
                  <li><a href="#"><img src="img/client-logo/client-logo6.png" alt="client logo 6"></a></li>
                  <li><a href="#"><img src="img/client-logo/client-logo7.png" alt="client logo 7"></a></li>
                  <li><a href="#"><img src="img/client-logo/client-logo8.png" alt="client logo 8"></a></li>
                  <li><a href="#"><img src="img/client-logo/client-logo9.png" alt="client logo 9"></a></li>
                  <li><a href="#"><img src="img/client-logo/client-logo10.png" alt="client logo 10"></a></li>
                  <li><a href="#"><img src="img/client-logo/client-logo11.png" alt="client logo 11"></a></li>
                  <li><a href="#"><img src="img/client-logo/client-logo12.png" alt="client logo 12"></a></li>
               </ul>               
            </div>
         </div>
      </section>
      <!-- container end here -->
      
      <!-- footer -->
      <footer id="me-footer">
         <div class="row">
            <div class="large-3 medium-3 column">
               <div class="logo-footer">
                  <a href="index.html">
                     <img class="retina" src="img/logo-footer.png" alt="Logo">
                  </a>
               </div>
            </div>
            <div class="large-3 medium-3 column">
               <h5 class="heading-footer">Come Visit Us</h5>
               <p>
                  Enix Headquarter <br>
                  765 Roosevelt Trail, Suite 301 <br>
                  Windham, Maine 04062
               </p>
            </div>
            <div class="large-3 medium-3 column">
               <h5 class="heading-footer">Talk With Us</h5>
               <ul>
                  <li><strong>E</strong> <a href="#"><EMAIL></a></li>
                  <li><strong>T</strong> +1 207 892 1255</li> 
                  <li><strong>M</strong> +1 207 513 2871</li>
               </ul>
            </div>
            <div class="large-3 medium-3 column">
               <h5 class="heading-footer">Get Social</h5>
               <ul>
                  <li><a href="#">Facebook</a></li>
                  <li><a href="#">Twitter</a></li>
                  <li><a href="#">Instagram</a></li>
               </ul>
            </div>
         </div>
         <div class="row footer-information">
            <div class="large-4 medium-4 column">
               <div class="copyright">
                  <span>&copy; All rights reserved 2014. <span>Enix Inc.</span></span>
               </div>
            </div>
            <div class="large-8 medium-8 column text-right">
               <ul class="inline-list footer-menu">
                  <li><a href="#">About Us</a></li>
                  <li><a href="#">Privacy</a></li>
                  <li><a href="#">Term of Use</a></li>
                  <li><a href="#">Pricing</a></li>
                  <li><a href="#">DMCA Notice</a></li>
                  <li><a class="active" href="#">Contact</a></li>
               </ul>
            </div>
         </div>
      </footer>
      <!-- footer end here -->

   </div>
   <!-- main-container end here -->

   <!-- javascript -->
   <script src="js/jquery.min.js"></script>
   <script src="js/foundstrap.js"></script>
   <script src="js/jquery.sscr.js"></script>
   <script src="js/jquery.waypoints.min.js"></script>
   <script src="js/owl.carousel.min.js"></script>
   <script src="js/jquery.scrollUp.js"></script>
   <script src="js/jquery.retina.js"></script>

   <!-- javascript plugin -->
   <script src="js/rs-plugin/js/jquery.themepunch.tools.min.js"></script>
   <script src="js/rs-plugin/js/jquery.themepunch.revolution.min.js"></script>

   <!-- javascript core -->
   <script src="js/theme-script.js"></script>
   <script src="js/jquery.cookie.js"></script>
   <script src="js/theme-switcher.js"></script>

   <script type="text/javascript">
      jQuery(document).ready(function($) { 
         Foundstrap.theme();

         // revolution slider configuration here
         $('.slideshow').revolution({
            delay:8000,
            startwidth:1080,
            startheight:520,
            hideThumbs:1,
            navigationType:"none",                  // bullet, thumb, none
            navigationArrows:"solo",                // nexttobullets, solo (old name verticalcentered), none
            navigationStyle:"square",               // round,square,navbar,round-old,square-old,navbar-old, or any from the list in the docu (choose between 50+ different item), custom
            navigationHAlign:"center",              // Vertical Align top,center,bottom
            navigationVAlign:"bottom",              // Horizontal Align left,center,right
            navigationHOffset:0,
            navigationVOffset:0,
            soloArrowLeftHalign:"left",
            soloArrowLeftValign:"center",
            soloArrowLeftHOffset:25,
            soloArrowLeftVOffset:0,
            soloArrowRightHalign:"right",
            soloArrowRightValign:"center",
            soloArrowRightHOffset:25,
            soloArrowRightVOffset:0,
            touchenabled:"on",                      // Enable Swipe Function : on/off
            onHoverStop:"on",                       // Stop Banner Timet at Hover on Slide on/off
            stopAtSlide:-1,                         // Stop Timer if Slide "x" has been Reached. If stopAfterLoops set to 0, then it stops already in the first Loop at slide X which defined. -1 means do not stop at any slide. stopAfterLoops has no sinn in this case.
            stopAfterLoops:-1,                      // Stop Timer if All slides has been played "x" times. IT will stop at THe slide which is defined via stopAtSlide:x, if set to -1 slide never stop automatic
            hideCaptionAtLimit:0,                   // It Defines if a caption should be shown under a Screen Resolution ( Basod on The Width of Browser)
            hideAllCaptionAtLilmit:0,               // Hide all The Captions if Width of Browser is less then this value
            hideSliderAtLimit:0,                    // Hide the whole slider, and stop also functions if Width of Browser is less than this value
            shadow:0,                               // 0 = no Shadow, 1,2,3 = 3 Different Art of Shadows  (No Shadow in Fullwidth Version !)
            fullWidth:"off",                        // Turns On or Off the Fullwidth Image Centering in FullWidth Modus
            fullScreen:"off"
         });
      });
   </script>
</body>
</html>