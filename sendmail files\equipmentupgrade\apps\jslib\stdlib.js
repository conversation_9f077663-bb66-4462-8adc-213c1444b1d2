function toggleDisplay(obj){
	if(document.getElementById(obj).style.display == 'none') {document.getElementById(obj).style.display = '';
	} else {document.getElementById(obj).style.display = 'none';}
}
/* ------------------*/
function validEmail(mailObj)
{
	if (mailObj.value.search("^.+@.+\\..+$") != -1)
	{	return true; }
	else {	return false; }
}
/* ------------------*/
function validUrl(url)
{
	if (url.value.search("^http://.+\\..+$") != -1)
	{	return true; }
	else {	return false; }
}
/* ------- */
function validDigit(fld,evt){
 var charCode = (evt.which) ? evt.which : event.keyCode
 if ( (charCode!=8 && charCode!=37 && charCode!=39 && charCode!=46) && (charCode<48 || charCode>57) ){ return false;}
 return true;
}
/* ------- */
function validFloat(fld,evt){
 var charCode = (evt.which) ? evt.which : event.keyCode
 //charCode 8= backspace, 37:back arrow, 39: front arrow, 46: delete, 190: decimal point
 if ( (charCode!=8 && charCode!=37 && charCode!=39 && charCode!=46 && charCode!=190) && (charCode<48 || charCode>57) ){return false;}
 return true;
}


function activateCoinSlider(){
	$(document).ready(function() {

		$('#coin-slider').coinslider({ 
			width: 950,				// width of slider panel
			height: 290,			// height of slider panel
			spw: 7,					// squares per width
			sph: 5,					// squares per height
			delay: 5000,			// delay between images in ms
			sDelay: 30,				// delay beetwen squares in ms
			opacity: 0.7,			// opacity of title and navigation
			titleSpeed: 500,		// speed of title appereance in ms
			effect: 'swirl',		// random, swirl, rain, straight
			navigation: true,		// prev next and buttons
			links : true,			// show images as links
			hoverPause: true		// pause on hover
		});
							  
	});
	
}

//----- ajax related
//below fn used by picture tab
function activateFancyBox(lineId){
	$(document).ready(function() {
		$("a#picture"+lineId).fancybox({
			'overlayShow'	: false,
			'transitionIn'	: 'elastic',
			'transitionOut'	: 'elastic'
		});
	});
}

