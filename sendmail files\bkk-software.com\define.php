<?php
//define('DS', DIRECTORY_SEPARATOR);
define('DS', '/');

## ==== 1. Directory Definition =========== ##
//---- General directories ----------------
define('DIR_LIB',			'lib'			. DS);	//Global			(encrypted)

define('DIR_APPS',			'apps'			. DS);
define('DIR_VIEWS',			'views'			. DS);
define('DIR_IMG',			'img'			. DS);

define('DIR_FILES',			'_files'		. DS);
define('DIR_FILES_DATA',	'_data'			. DS);
define('DIR_FILES_ATTACH',	'_attach'		. DS);
define('DIR_FILES_PIC',		'_pic'			. DS);

define('DIR_AVATAR',  		'avatar'		. DS);
define('DIR_FORMS',			'forms'			. DS);
define('DIR_LOGO',  		'logo'			. DS);
define('DIR_UTIL',  		'util'			. DS);


//try remove below line in future
define('DIR_MY',  			'my'		. DS);


define('SF_PATH',				substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DS);		## /home/<USER>/public_html/demo

define('APPS_PATH',				SF_PATH . DIR_APPS);
define('LIB_PATH',				SF_PATH . DIR_APPS . DIR_LIB);	//systems
define('VIEWS_PATH', 			SF_PATH . DIR_VIEWS);
define('FILES_PATH', 			SF_PATH . DIR_FILES);
define('FORMS_PATH', 			SF_PATH . DIR_FORMS);
define('INCLUDE_PATH', 			SF_PATH . DIR_VIEWS . 'include' . DS);
define('SLIDER_PATH', 			SF_PATH . DIR_VIEWS . 'slider' . DS);
define('IMG_PATH',  			SF_PATH . DIR_VIEWS . DIR_IMG);		
define('TMP_PATH',  			SF_PATH . '_tmp' . DS);		
define('SF_SESSION_PATH',		SF_PATH . '_tmp' . DS .'sf_session');						//don't need ending slash


$s = null;
define('ROOT_URL', 'http'.$s.'://'. $_SERVER['HTTP_HOST'] . DS);	
define('SF_URL', ROOT_URL );


## ==== 3. URL Definition =========== ##
//$root = ($_SERVER['HTTPS'] ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . '/';
$s = null;



#	define('VIEWS_URL', 		SF_URL . DIR_VIEWS);
		define('CSS_URL', 		SF_URL . 'css' . DS);
		define('IMG_URL', 		SF_URL . 'img' . DS);
		define('APPS_URL', 		SF_URL . DIR_APPS);
		define('JSLIB_URL', 	SF_URL  . 'js' . DS);

		define('FORMS_URL', 	SF_URL . DIR_FORMS);




?>