/* ------------------------------------------------------------------
 Foundstrap Framework
   
 file         : foundstrap.js
 Desc         : Foundstrap Javascript Framework
 Version      : 1.1 - Amethyst
 Date         : --/--/----
 Author       : <PERSON>
 Author URI   : http://imamfirmansyah.com
 URI          : http://foundstrap.com
 Email        : <EMAIL>
 
 Foundstrap Copyright 2014. All Rights Reserved.
------------------------------------------------------------------ */

/*! iCheck v1.0.2 by <PERSON><PERSON>, http://git.io/arlzeA, MIT Licensed */
(function(f){function A(a,b,d){var c=a[0],g=/er/.test(d)?_indeterminate:/bl/.test(d)?n:k,e=d==_update?{checked:c[k],disabled:c[n],indeterminate:"true"==a.attr(_indeterminate)||"false"==a.attr(_determinate)}:c[g];if(/^(ch|di|in)/.test(d)&&!e)x(a,g);else if(/^(un|en|de)/.test(d)&&e)q(a,g);else if(d==_update)for(var f in e)e[f]?x(a,f,!0):q(a,f,!0);else if(!b||"toggle"==d){if(!b)a[_callback]("ifClicked");e?c[_type]!==r&&q(a,g):x(a,g)}}function x(a,b,d){var c=a[0],g=a.parent(),e=b==k,u=b==_indeterminate,
v=b==n,s=u?_determinate:e?y:"enabled",F=l(a,s+t(c[_type])),B=l(a,b+t(c[_type]));if(!0!==c[b]){if(!d&&b==k&&c[_type]==r&&c.name){var w=a.closest("form"),p='input[name="'+c.name+'"]',p=w.length?w.find(p):f(p);p.each(function(){this!==c&&f(this).data(m)&&q(f(this),b)})}u?(c[b]=!0,c[k]&&q(a,k,"force")):(d||(c[b]=!0),e&&c[_indeterminate]&&q(a,_indeterminate,!1));D(a,e,b,d)}c[n]&&l(a,_cursor,!0)&&g.find("."+C).css(_cursor,"default");g[_add](B||l(a,b)||"");g.attr("role")&&!u&&g.attr("aria-"+(v?n:k),"true");
g[_remove](F||l(a,s)||"")}function q(a,b,d){var c=a[0],g=a.parent(),e=b==k,f=b==_indeterminate,m=b==n,s=f?_determinate:e?y:"enabled",q=l(a,s+t(c[_type])),r=l(a,b+t(c[_type]));if(!1!==c[b]){if(f||!d||"force"==d)c[b]=!1;D(a,e,s,d)}!c[n]&&l(a,_cursor,!0)&&g.find("."+C).css(_cursor,"pointer");g[_remove](r||l(a,b)||"");g.attr("role")&&!f&&g.attr("aria-"+(m?n:k),"false");g[_add](q||l(a,s)||"")}function E(a,b){if(a.data(m)){a.parent().html(a.attr("style",a.data(m).s||""));if(b)a[_callback](b);a.off(".i").unwrap();
f(_label+'[for="'+a[0].id+'"]').add(a.closest(_label)).off(".i")}}function l(a,b,f){if(a.data(m))return a.data(m).o[b+(f?"":"Class")]}function t(a){return a.charAt(0).toUpperCase()+a.slice(1)}function D(a,b,f,c){if(!c){if(b)a[_callback]("ifToggled");a[_callback]("ifChanged")[_callback]("if"+t(f))}}var m="iCheck",C=m+"-helper",r="radio",k="checked",y="un"+k,n="disabled";_determinate="determinate";_indeterminate="in"+_determinate;_update="update";_type="type";_click="click";_touch="touchbegin.i touchend.i";
_add="addClass";_remove="removeClass";_callback="trigger";_label="label";_cursor="cursor";_mobile=/ipad|iphone|ipod|android|blackberry|windows phone|opera mini|silk/i.test(navigator.userAgent);f.fn[m]=function(a,b){var d='input[type="checkbox"], input[type="'+r+'"]',c=f(),g=function(a){a.each(function(){var a=f(this);c=a.is(d)?c.add(a):c.add(a.find(d))})};if(/^(check|uncheck|toggle|indeterminate|determinate|disable|enable|update|destroy)$/i.test(a))return a=a.toLowerCase(),g(this),c.each(function(){var c=
f(this);"destroy"==a?E(c,"ifDestroyed"):A(c,!0,a);f.isFunction(b)&&b()});if("object"!=typeof a&&a)return this;var e=f.extend({checkedClass:k,disabledClass:n,indeterminateClass:_indeterminate,labelHover:!0},a),l=e.handle,v=e.hoverClass||"hover",s=e.focusClass||"focus",t=e.activeClass||"active",B=!!e.labelHover,w=e.labelHoverClass||"hover",p=(""+e.increaseArea).replace("%","")|0;if("checkbox"==l||l==r)d='input[type="'+l+'"]';-50>p&&(p=-50);g(this);return c.each(function(){var a=f(this);E(a);var c=this,
b=c.id,g=-p+"%",d=100+2*p+"%",d={position:"absolute",top:g,left:g,display:"block",width:d,height:d,margin:0,padding:0,background:"#fff",border:0,opacity:0},g=_mobile?{position:"absolute",visibility:"hidden"}:p?d:{position:"absolute",opacity:0},l="checkbox"==c[_type]?e.checkboxClass||"icheckbox":e.radioClass||"i"+r,z=f(_label+'[for="'+b+'"]').add(a.closest(_label)),u=!!e.aria,y=m+"-"+Math.random().toString(36).substr(2,6),h='<div class="'+l+'" '+(u?'role="'+c[_type]+'" ':"");u&&z.each(function(){h+=
'aria-labelledby="';this.id?h+=this.id:(this.id=y,h+=y);h+='"'});h=a.wrap(h+"/>")[_callback]("ifCreated").parent().append(e.insert);d=f('<ins class="'+C+'"/>').css(d).appendTo(h);a.data(m,{o:e,s:a.attr("style")}).css(g);e.inheritClass&&h[_add](c.className||"");e.inheritID&&b&&h.attr("id",m+"-"+b);"static"==h.css("position")&&h.css("position","relative");A(a,!0,_update);if(z.length)z.on(_click+".i mouseover.i mouseout.i "+_touch,function(b){var d=b[_type],e=f(this);if(!c[n]){if(d==_click){if(f(b.target).is("a"))return;
A(a,!1,!0)}else B&&(/ut|nd/.test(d)?(h[_remove](v),e[_remove](w)):(h[_add](v),e[_add](w)));if(_mobile)b.stopPropagation();else return!1}});a.on(_click+".i focus.i blur.i keyup.i keydown.i keypress.i",function(b){var d=b[_type];b=b.keyCode;if(d==_click)return!1;if("keydown"==d&&32==b)return c[_type]==r&&c[k]||(c[k]?q(a,k):x(a,k)),!1;if("keyup"==d&&c[_type]==r)!c[k]&&x(a,k);else if(/us|ur/.test(d))h["blur"==d?_remove:_add](s)});d.on(_click+" mousedown mouseup mouseover mouseout "+_touch,function(b){var d=
b[_type],e=/wn|up/.test(d)?t:v;if(!c[n]){if(d==_click)A(a,!1,!0);else{if(/wn|er|in/.test(d))h[_add](e);else h[_remove](e+" "+t);if(z.length&&B&&e==v)z[/ut|nd/.test(d)?_remove:_add](w)}if(_mobile)b.stopPropagation();else return!1}})})}})(window.jQuery||window.Zepto);

/* Chosen v1.3.0 | (c) 2011-2014 by Harvest | MIT License, https://github.com/harvesthq/chosen/blob/master/LICENSE.md */
!function(){var a,AbstractChosen,Chosen,SelectParser,b,c={}.hasOwnProperty,d=function(a,b){function d(){this.constructor=a}for(var e in b)c.call(b,e)&&(a[e]=b[e]);return d.prototype=b.prototype,a.prototype=new d,a.__super__=b.prototype,a};SelectParser=function(){function SelectParser(){this.options_index=0,this.parsed=[]}return SelectParser.prototype.add_node=function(a){return"OPTGROUP"===a.nodeName.toUpperCase()?this.add_group(a):this.add_option(a)},SelectParser.prototype.add_group=function(a){var b,c,d,e,f,g;for(b=this.parsed.length,this.parsed.push({array_index:b,group:!0,label:this.escapeExpression(a.label),children:0,disabled:a.disabled,classes:a.className}),f=a.childNodes,g=[],d=0,e=f.length;e>d;d++)c=f[d],g.push(this.add_option(c,b,a.disabled));return g},SelectParser.prototype.add_option=function(a,b,c){return"OPTION"===a.nodeName.toUpperCase()?(""!==a.text?(null!=b&&(this.parsed[b].children+=1),this.parsed.push({array_index:this.parsed.length,options_index:this.options_index,value:a.value,text:a.text,html:a.innerHTML,selected:a.selected,disabled:c===!0?c:a.disabled,group_array_index:b,classes:a.className,style:a.style.cssText})):this.parsed.push({array_index:this.parsed.length,options_index:this.options_index,empty:!0}),this.options_index+=1):void 0},SelectParser.prototype.escapeExpression=function(a){var b,c;return null==a||a===!1?"":/[\&\<\>\"\'\`]/.test(a)?(b={"<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},c=/&(?!\w+;)|[\<\>\"\'\`]/g,a.replace(c,function(a){return b[a]||"&amp;"})):a},SelectParser}(),SelectParser.select_to_array=function(a){var b,c,d,e,f;for(c=new SelectParser,f=a.childNodes,d=0,e=f.length;e>d;d++)b=f[d],c.add_node(b);return c.parsed},AbstractChosen=function(){function AbstractChosen(a,b){this.form_field=a,this.options=null!=b?b:{},AbstractChosen.browser_is_supported()&&(this.is_multiple=this.form_field.multiple,this.set_default_text(),this.set_default_values(),this.setup(),this.set_up_html(),this.register_observers(),this.on_ready())}return AbstractChosen.prototype.set_default_values=function(){var a=this;return this.click_test_action=function(b){return a.test_active_click(b)},this.activate_action=function(b){return a.activate_field(b)},this.active_field=!1,this.mouse_on_container=!1,this.results_showing=!1,this.result_highlighted=null,this.allow_single_deselect=null!=this.options.allow_single_deselect&&null!=this.form_field.options[0]&&""===this.form_field.options[0].text?this.options.allow_single_deselect:!1,this.disable_search_threshold=this.options.disable_search_threshold||0,this.disable_search=this.options.disable_search||!1,this.enable_split_word_search=null!=this.options.enable_split_word_search?this.options.enable_split_word_search:!0,this.group_search=null!=this.options.group_search?this.options.group_search:!0,this.search_contains=this.options.search_contains||!1,this.single_backstroke_delete=null!=this.options.single_backstroke_delete?this.options.single_backstroke_delete:!0,this.max_selected_options=this.options.max_selected_options||1/0,this.inherit_select_classes=this.options.inherit_select_classes||!1,this.display_selected_options=null!=this.options.display_selected_options?this.options.display_selected_options:!0,this.display_disabled_options=null!=this.options.display_disabled_options?this.options.display_disabled_options:!0},AbstractChosen.prototype.set_default_text=function(){return this.default_text=this.form_field.getAttribute("data-placeholder")?this.form_field.getAttribute("data-placeholder"):this.is_multiple?this.options.placeholder_text_multiple||this.options.placeholder_text||AbstractChosen.default_multiple_text:this.options.placeholder_text_single||this.options.placeholder_text||AbstractChosen.default_single_text,this.results_none_found=this.form_field.getAttribute("data-no_results_text")||this.options.no_results_text||AbstractChosen.default_no_result_text},AbstractChosen.prototype.mouse_enter=function(){return this.mouse_on_container=!0},AbstractChosen.prototype.mouse_leave=function(){return this.mouse_on_container=!1},AbstractChosen.prototype.input_focus=function(){var a=this;if(this.is_multiple){if(!this.active_field)return setTimeout(function(){return a.container_mousedown()},50)}else if(!this.active_field)return this.activate_field()},AbstractChosen.prototype.input_blur=function(){var a=this;return this.mouse_on_container?void 0:(this.active_field=!1,setTimeout(function(){return a.blur_test()},100))},AbstractChosen.prototype.results_option_build=function(a){var b,c,d,e,f;for(b="",f=this.results_data,d=0,e=f.length;e>d;d++)c=f[d],b+=c.group?this.result_add_group(c):this.result_add_option(c),(null!=a?a.first:void 0)&&(c.selected&&this.is_multiple?this.choice_build(c):c.selected&&!this.is_multiple&&this.single_set_selected_text(c.text));return b},AbstractChosen.prototype.result_add_option=function(a){var b,c;return a.search_match?this.include_option_in_results(a)?(b=[],a.disabled||a.selected&&this.is_multiple||b.push("active-result"),!a.disabled||a.selected&&this.is_multiple||b.push("disabled-result"),a.selected&&b.push("result-selected"),null!=a.group_array_index&&b.push("group-option"),""!==a.classes&&b.push(a.classes),c=document.createElement("li"),c.className=b.join(" "),c.style.cssText=a.style,c.setAttribute("data-option-array-index",a.array_index),c.innerHTML=a.search_text,this.outerHTML(c)):"":""},AbstractChosen.prototype.result_add_group=function(a){var b,c;return a.search_match||a.group_match?a.active_options>0?(b=[],b.push("group-result"),a.classes&&b.push(a.classes),c=document.createElement("li"),c.className=b.join(" "),c.innerHTML=a.search_text,this.outerHTML(c)):"":""},AbstractChosen.prototype.results_update_field=function(){return this.set_default_text(),this.is_multiple||this.results_reset_cleanup(),this.result_clear_highlight(),this.results_build(),this.results_showing?this.winnow_results():void 0},AbstractChosen.prototype.reset_single_select_options=function(){var a,b,c,d,e;for(d=this.results_data,e=[],b=0,c=d.length;c>b;b++)a=d[b],a.selected?e.push(a.selected=!1):e.push(void 0);return e},AbstractChosen.prototype.results_toggle=function(){return this.results_showing?this.results_hide():this.results_show()},AbstractChosen.prototype.results_search=function(){return this.results_showing?this.winnow_results():this.results_show()},AbstractChosen.prototype.winnow_results=function(){var a,b,c,d,e,f,g,h,i,j,k,l;for(this.no_results_clear(),d=0,f=this.get_search_text(),a=f.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"),i=new RegExp(a,"i"),c=this.get_search_regex(a),l=this.results_data,j=0,k=l.length;k>j;j++)b=l[j],b.search_match=!1,e=null,this.include_option_in_results(b)&&(b.group&&(b.group_match=!1,b.active_options=0),null!=b.group_array_index&&this.results_data[b.group_array_index]&&(e=this.results_data[b.group_array_index],0===e.active_options&&e.search_match&&(d+=1),e.active_options+=1),(!b.group||this.group_search)&&(b.search_text=b.group?b.label:b.text,b.search_match=this.search_string_match(b.search_text,c),b.search_match&&!b.group&&(d+=1),b.search_match?(f.length&&(g=b.search_text.search(i),h=b.search_text.substr(0,g+f.length)+"</em>"+b.search_text.substr(g+f.length),b.search_text=h.substr(0,g)+"<em>"+h.substr(g)),null!=e&&(e.group_match=!0)):null!=b.group_array_index&&this.results_data[b.group_array_index].search_match&&(b.search_match=!0)));return this.result_clear_highlight(),1>d&&f.length?(this.update_results_content(""),this.no_results(f)):(this.update_results_content(this.results_option_build()),this.winnow_results_set_highlight())},AbstractChosen.prototype.get_search_regex=function(a){var b;return b=this.search_contains?"":"^",new RegExp(b+a,"i")},AbstractChosen.prototype.search_string_match=function(a,b){var c,d,e,f;if(b.test(a))return!0;if(this.enable_split_word_search&&(a.indexOf(" ")>=0||0===a.indexOf("["))&&(d=a.replace(/\[|\]/g,"").split(" "),d.length))for(e=0,f=d.length;f>e;e++)if(c=d[e],b.test(c))return!0},AbstractChosen.prototype.choices_count=function(){var a,b,c,d;if(null!=this.selected_option_count)return this.selected_option_count;for(this.selected_option_count=0,d=this.form_field.options,b=0,c=d.length;c>b;b++)a=d[b],a.selected&&(this.selected_option_count+=1);return this.selected_option_count},AbstractChosen.prototype.choices_click=function(a){return a.preventDefault(),this.results_showing||this.is_disabled?void 0:this.results_show()},AbstractChosen.prototype.keyup_checker=function(a){var b,c;switch(b=null!=(c=a.which)?c:a.keyCode,this.search_field_scale(),b){case 8:if(this.is_multiple&&this.backstroke_length<1&&this.choices_count()>0)return this.keydown_backstroke();if(!this.pending_backstroke)return this.result_clear_highlight(),this.results_search();break;case 13:if(a.preventDefault(),this.results_showing)return this.result_select(a);break;case 27:return this.results_showing&&this.results_hide(),!0;case 9:case 38:case 40:case 16:case 91:case 17:break;default:return this.results_search()}},AbstractChosen.prototype.clipboard_event_checker=function(){var a=this;return setTimeout(function(){return a.results_search()},50)},AbstractChosen.prototype.container_width=function(){return null!=this.options.width?this.options.width:""+this.form_field.offsetWidth+"px"},AbstractChosen.prototype.include_option_in_results=function(a){return this.is_multiple&&!this.display_selected_options&&a.selected?!1:!this.display_disabled_options&&a.disabled?!1:a.empty?!1:!0},AbstractChosen.prototype.search_results_touchstart=function(a){return this.touch_started=!0,this.search_results_mouseover(a)},AbstractChosen.prototype.search_results_touchmove=function(a){return this.touch_started=!1,this.search_results_mouseout(a)},AbstractChosen.prototype.search_results_touchend=function(a){return this.touch_started?this.search_results_mouseup(a):void 0},AbstractChosen.prototype.outerHTML=function(a){var b;return a.outerHTML?a.outerHTML:(b=document.createElement("div"),b.appendChild(a),b.innerHTML)},AbstractChosen.browser_is_supported=function(){return"Microsoft Internet Explorer"===window.navigator.appName?document.documentMode>=8:/iP(od|hone)/i.test(window.navigator.userAgent)?!1:/Android/i.test(window.navigator.userAgent)&&/Mobile/i.test(window.navigator.userAgent)?!1:!0},AbstractChosen.default_multiple_text="Select Some Options",AbstractChosen.default_single_text="Select an Option",AbstractChosen.default_no_result_text="No results match",AbstractChosen}(),a=jQuery,a.fn.extend({chosen:function(b){return AbstractChosen.browser_is_supported()?this.each(function(){var c,d;c=a(this),d=c.data("chosen"),"destroy"===b&&d instanceof Chosen?d.destroy():d instanceof Chosen||c.data("chosen",new Chosen(this,b))}):this}}),Chosen=function(c){function Chosen(){return b=Chosen.__super__.constructor.apply(this,arguments)}return d(Chosen,c),Chosen.prototype.setup=function(){return this.form_field_jq=a(this.form_field),this.current_selectedIndex=this.form_field.selectedIndex,this.is_rtl=this.form_field_jq.hasClass("chosen-rtl")},Chosen.prototype.set_up_html=function(){var b,c;return b=["chosen-container"],b.push("chosen-container-"+(this.is_multiple?"multi":"single")),this.inherit_select_classes&&this.form_field.className&&b.push(this.form_field.className),this.is_rtl&&b.push("chosen-rtl"),c={"class":b.join(" "),style:"width: "+this.container_width()+";",title:this.form_field.title},this.form_field.id.length&&(c.id=this.form_field.id.replace(/[^\w]/g,"_")+"_chosen"),this.container=a("<div />",c),this.is_multiple?this.container.html('<ul class="chosen-choices"><li class="search-field"><input type="text" value="'+this.default_text+'" class="default" autocomplete="off" style="width:25px;" /></li></ul><div class="chosen-drop"><ul class="chosen-results"></ul></div>'):this.container.html('<a class="chosen-single chosen-default" tabindex="-1"><span>'+this.default_text+'</span><div><b></b></div></a><div class="chosen-drop"><div class="chosen-search"><input type="text" autocomplete="off" /></div><ul class="chosen-results"></ul></div>'),this.form_field_jq.hide().after(this.container),this.dropdown=this.container.find("div.chosen-drop").first(),this.search_field=this.container.find("input").first(),this.search_results=this.container.find("ul.chosen-results").first(),this.search_field_scale(),this.search_no_results=this.container.find("li.no-results").first(),this.is_multiple?(this.search_choices=this.container.find("ul.chosen-choices").first(),this.search_container=this.container.find("li.search-field").first()):(this.search_container=this.container.find("div.chosen-search").first(),this.selected_item=this.container.find(".chosen-single").first()),this.results_build(),this.set_tab_index(),this.set_label_behavior()},Chosen.prototype.on_ready=function(){return this.form_field_jq.trigger("chosen:ready",{chosen:this})},Chosen.prototype.register_observers=function(){var a=this;return this.container.bind("touchstart.chosen",function(b){a.container_mousedown(b)}),this.container.bind("touchend.chosen",function(b){a.container_mouseup(b)}),this.container.bind("mousedown.chosen",function(b){a.container_mousedown(b)}),this.container.bind("mouseup.chosen",function(b){a.container_mouseup(b)}),this.container.bind("mouseenter.chosen",function(b){a.mouse_enter(b)}),this.container.bind("mouseleave.chosen",function(b){a.mouse_leave(b)}),this.search_results.bind("mouseup.chosen",function(b){a.search_results_mouseup(b)}),this.search_results.bind("mouseover.chosen",function(b){a.search_results_mouseover(b)}),this.search_results.bind("mouseout.chosen",function(b){a.search_results_mouseout(b)}),this.search_results.bind("mousewheel.chosen DOMMouseScroll.chosen",function(b){a.search_results_mousewheel(b)}),this.search_results.bind("touchstart.chosen",function(b){a.search_results_touchstart(b)}),this.search_results.bind("touchmove.chosen",function(b){a.search_results_touchmove(b)}),this.search_results.bind("touchend.chosen",function(b){a.search_results_touchend(b)}),this.form_field_jq.bind("chosen:updated.chosen",function(b){a.results_update_field(b)}),this.form_field_jq.bind("chosen:activate.chosen",function(b){a.activate_field(b)}),this.form_field_jq.bind("chosen:open.chosen",function(b){a.container_mousedown(b)}),this.form_field_jq.bind("chosen:close.chosen",function(b){a.input_blur(b)}),this.search_field.bind("blur.chosen",function(b){a.input_blur(b)}),this.search_field.bind("keyup.chosen",function(b){a.keyup_checker(b)}),this.search_field.bind("keydown.chosen",function(b){a.keydown_checker(b)}),this.search_field.bind("focus.chosen",function(b){a.input_focus(b)}),this.search_field.bind("cut.chosen",function(b){a.clipboard_event_checker(b)}),this.search_field.bind("paste.chosen",function(b){a.clipboard_event_checker(b)}),this.is_multiple?this.search_choices.bind("click.chosen",function(b){a.choices_click(b)}):this.container.bind("click.chosen",function(a){a.preventDefault()})},Chosen.prototype.destroy=function(){return a(this.container[0].ownerDocument).unbind("click.chosen",this.click_test_action),this.search_field[0].tabIndex&&(this.form_field_jq[0].tabIndex=this.search_field[0].tabIndex),this.container.remove(),this.form_field_jq.removeData("chosen"),this.form_field_jq.show()},Chosen.prototype.search_field_disabled=function(){return this.is_disabled=this.form_field_jq[0].disabled,this.is_disabled?(this.container.addClass("chosen-disabled"),this.search_field[0].disabled=!0,this.is_multiple||this.selected_item.unbind("focus.chosen",this.activate_action),this.close_field()):(this.container.removeClass("chosen-disabled"),this.search_field[0].disabled=!1,this.is_multiple?void 0:this.selected_item.bind("focus.chosen",this.activate_action))},Chosen.prototype.container_mousedown=function(b){return this.is_disabled||(b&&"mousedown"===b.type&&!this.results_showing&&b.preventDefault(),null!=b&&a(b.target).hasClass("search-choice-close"))?void 0:(this.active_field?this.is_multiple||!b||a(b.target)[0]!==this.selected_item[0]&&!a(b.target).parents("a.chosen-single").length||(b.preventDefault(),this.results_toggle()):(this.is_multiple&&this.search_field.val(""),a(this.container[0].ownerDocument).bind("click.chosen",this.click_test_action),this.results_show()),this.activate_field())},Chosen.prototype.container_mouseup=function(a){return"ABBR"!==a.target.nodeName||this.is_disabled?void 0:this.results_reset(a)},Chosen.prototype.search_results_mousewheel=function(a){var b;return a.originalEvent&&(b=a.originalEvent.deltaY||-a.originalEvent.wheelDelta||a.originalEvent.detail),null!=b?(a.preventDefault(),"DOMMouseScroll"===a.type&&(b=40*b),this.search_results.scrollTop(b+this.search_results.scrollTop())):void 0},Chosen.prototype.blur_test=function(){return!this.active_field&&this.container.hasClass("chosen-container-active")?this.close_field():void 0},Chosen.prototype.close_field=function(){return a(this.container[0].ownerDocument).unbind("click.chosen",this.click_test_action),this.active_field=!1,this.results_hide(),this.container.removeClass("chosen-container-active"),this.clear_backstroke(),this.show_search_field_default(),this.search_field_scale()},Chosen.prototype.activate_field=function(){return this.container.addClass("chosen-container-active"),this.active_field=!0,this.search_field.val(this.search_field.val()),this.search_field.focus()},Chosen.prototype.test_active_click=function(b){var c;return c=a(b.target).closest(".chosen-container"),c.length&&this.container[0]===c[0]?this.active_field=!0:this.close_field()},Chosen.prototype.results_build=function(){return this.parsing=!0,this.selected_option_count=null,this.results_data=SelectParser.select_to_array(this.form_field),this.is_multiple?this.search_choices.find("li.search-choice").remove():this.is_multiple||(this.single_set_selected_text(),this.disable_search||this.form_field.options.length<=this.disable_search_threshold?(this.search_field[0].readOnly=!0,this.container.addClass("chosen-container-single-nosearch")):(this.search_field[0].readOnly=!1,this.container.removeClass("chosen-container-single-nosearch"))),this.update_results_content(this.results_option_build({first:!0})),this.search_field_disabled(),this.show_search_field_default(),this.search_field_scale(),this.parsing=!1},Chosen.prototype.result_do_highlight=function(a){var b,c,d,e,f;if(a.length){if(this.result_clear_highlight(),this.result_highlight=a,this.result_highlight.addClass("highlighted"),d=parseInt(this.search_results.css("maxHeight"),10),f=this.search_results.scrollTop(),e=d+f,c=this.result_highlight.position().top+this.search_results.scrollTop(),b=c+this.result_highlight.outerHeight(),b>=e)return this.search_results.scrollTop(b-d>0?b-d:0);if(f>c)return this.search_results.scrollTop(c)}},Chosen.prototype.result_clear_highlight=function(){return this.result_highlight&&this.result_highlight.removeClass("highlighted"),this.result_highlight=null},Chosen.prototype.results_show=function(){return this.is_multiple&&this.max_selected_options<=this.choices_count()?(this.form_field_jq.trigger("chosen:maxselected",{chosen:this}),!1):(this.container.addClass("chosen-with-drop"),this.results_showing=!0,this.search_field.focus(),this.search_field.val(this.search_field.val()),this.winnow_results(),this.form_field_jq.trigger("chosen:showing_dropdown",{chosen:this}))},Chosen.prototype.update_results_content=function(a){return this.search_results.html(a)},Chosen.prototype.results_hide=function(){return this.results_showing&&(this.result_clear_highlight(),this.container.removeClass("chosen-with-drop"),this.form_field_jq.trigger("chosen:hiding_dropdown",{chosen:this})),this.results_showing=!1},Chosen.prototype.set_tab_index=function(){var a;return this.form_field.tabIndex?(a=this.form_field.tabIndex,this.form_field.tabIndex=-1,this.search_field[0].tabIndex=a):void 0},Chosen.prototype.set_label_behavior=function(){var b=this;return this.form_field_label=this.form_field_jq.parents("label"),!this.form_field_label.length&&this.form_field.id.length&&(this.form_field_label=a("label[for='"+this.form_field.id+"']")),this.form_field_label.length>0?this.form_field_label.bind("click.chosen",function(a){return b.is_multiple?b.container_mousedown(a):b.activate_field()}):void 0},Chosen.prototype.show_search_field_default=function(){return this.is_multiple&&this.choices_count()<1&&!this.active_field?(this.search_field.val(this.default_text),this.search_field.addClass("default")):(this.search_field.val(""),this.search_field.removeClass("default"))},Chosen.prototype.search_results_mouseup=function(b){var c;return c=a(b.target).hasClass("active-result")?a(b.target):a(b.target).parents(".active-result").first(),c.length?(this.result_highlight=c,this.result_select(b),this.search_field.focus()):void 0},Chosen.prototype.search_results_mouseover=function(b){var c;return c=a(b.target).hasClass("active-result")?a(b.target):a(b.target).parents(".active-result").first(),c?this.result_do_highlight(c):void 0},Chosen.prototype.search_results_mouseout=function(b){return a(b.target).hasClass("active-result")?this.result_clear_highlight():void 0},Chosen.prototype.choice_build=function(b){var c,d,e=this;return c=a("<li />",{"class":"search-choice"}).html("<span>"+b.html+"</span>"),b.disabled?c.addClass("search-choice-disabled"):(d=a("<a />",{"class":"search-choice-close","data-option-array-index":b.array_index}),d.bind("click.chosen",function(a){return e.choice_destroy_link_click(a)}),c.append(d)),this.search_container.before(c)},Chosen.prototype.choice_destroy_link_click=function(b){return b.preventDefault(),b.stopPropagation(),this.is_disabled?void 0:this.choice_destroy(a(b.target))},Chosen.prototype.choice_destroy=function(a){return this.result_deselect(a[0].getAttribute("data-option-array-index"))?(this.show_search_field_default(),this.is_multiple&&this.choices_count()>0&&this.search_field.val().length<1&&this.results_hide(),a.parents("li").first().remove(),this.search_field_scale()):void 0},Chosen.prototype.results_reset=function(){return this.reset_single_select_options(),this.form_field.options[0].selected=!0,this.single_set_selected_text(),this.show_search_field_default(),this.results_reset_cleanup(),this.form_field_jq.trigger("change"),this.active_field?this.results_hide():void 0},Chosen.prototype.results_reset_cleanup=function(){return this.current_selectedIndex=this.form_field.selectedIndex,this.selected_item.find("abbr").remove()},Chosen.prototype.result_select=function(a){var b,c;return this.result_highlight?(b=this.result_highlight,this.result_clear_highlight(),this.is_multiple&&this.max_selected_options<=this.choices_count()?(this.form_field_jq.trigger("chosen:maxselected",{chosen:this}),!1):(this.is_multiple?b.removeClass("active-result"):this.reset_single_select_options(),c=this.results_data[b[0].getAttribute("data-option-array-index")],c.selected=!0,this.form_field.options[c.options_index].selected=!0,this.selected_option_count=null,this.is_multiple?this.choice_build(c):this.single_set_selected_text(c.text),(a.metaKey||a.ctrlKey)&&this.is_multiple||this.results_hide(),this.search_field.val(""),(this.is_multiple||this.form_field.selectedIndex!==this.current_selectedIndex)&&this.form_field_jq.trigger("change",{selected:this.form_field.options[c.options_index].value}),this.current_selectedIndex=this.form_field.selectedIndex,this.search_field_scale())):void 0},Chosen.prototype.single_set_selected_text=function(a){return null==a&&(a=this.default_text),a===this.default_text?this.selected_item.addClass("chosen-default"):(this.single_deselect_control_build(),this.selected_item.removeClass("chosen-default")),this.selected_item.find("span").text(a)},Chosen.prototype.result_deselect=function(a){var b;return b=this.results_data[a],this.form_field.options[b.options_index].disabled?!1:(b.selected=!1,this.form_field.options[b.options_index].selected=!1,this.selected_option_count=null,this.result_clear_highlight(),this.results_showing&&this.winnow_results(),this.form_field_jq.trigger("change",{deselected:this.form_field.options[b.options_index].value}),this.search_field_scale(),!0)},Chosen.prototype.single_deselect_control_build=function(){return this.allow_single_deselect?(this.selected_item.find("abbr").length||this.selected_item.find("span").first().after('<abbr class="search-choice-close"></abbr>'),this.selected_item.addClass("chosen-single-with-deselect")):void 0},Chosen.prototype.get_search_text=function(){return this.search_field.val()===this.default_text?"":a("<div/>").text(a.trim(this.search_field.val())).html()},Chosen.prototype.winnow_results_set_highlight=function(){var a,b;return b=this.is_multiple?[]:this.search_results.find(".result-selected.active-result"),a=b.length?b.first():this.search_results.find(".active-result").first(),null!=a?this.result_do_highlight(a):void 0},Chosen.prototype.no_results=function(b){var c;return c=a('<li class="no-results">'+this.results_none_found+' "<span></span>"</li>'),c.find("span").first().html(b),this.search_results.append(c),this.form_field_jq.trigger("chosen:no_results",{chosen:this})},Chosen.prototype.no_results_clear=function(){return this.search_results.find(".no-results").remove()},Chosen.prototype.keydown_arrow=function(){var a;return this.results_showing&&this.result_highlight?(a=this.result_highlight.nextAll("li.active-result").first())?this.result_do_highlight(a):void 0:this.results_show()},Chosen.prototype.keyup_arrow=function(){var a;return this.results_showing||this.is_multiple?this.result_highlight?(a=this.result_highlight.prevAll("li.active-result"),a.length?this.result_do_highlight(a.first()):(this.choices_count()>0&&this.results_hide(),this.result_clear_highlight())):void 0:this.results_show()},Chosen.prototype.keydown_backstroke=function(){var a;return this.pending_backstroke?(this.choice_destroy(this.pending_backstroke.find("a").first()),this.clear_backstroke()):(a=this.search_container.siblings("li.search-choice").last(),a.length&&!a.hasClass("search-choice-disabled")?(this.pending_backstroke=a,this.single_backstroke_delete?this.keydown_backstroke():this.pending_backstroke.addClass("search-choice-focus")):void 0)},Chosen.prototype.clear_backstroke=function(){return this.pending_backstroke&&this.pending_backstroke.removeClass("search-choice-focus"),this.pending_backstroke=null},Chosen.prototype.keydown_checker=function(a){var b,c;switch(b=null!=(c=a.which)?c:a.keyCode,this.search_field_scale(),8!==b&&this.pending_backstroke&&this.clear_backstroke(),b){case 8:this.backstroke_length=this.search_field.val().length;break;case 9:this.results_showing&&!this.is_multiple&&this.result_select(a),this.mouse_on_container=!1;break;case 13:this.results_showing&&a.preventDefault();break;case 32:this.disable_search&&a.preventDefault();break;case 38:a.preventDefault(),this.keyup_arrow();break;case 40:a.preventDefault(),this.keydown_arrow()}},Chosen.prototype.search_field_scale=function(){var b,c,d,e,f,g,h,i,j;if(this.is_multiple){for(d=0,h=0,f="position:absolute; left: -1000px; top: -1000px; display:none;",g=["font-size","font-style","font-weight","font-family","line-height","text-transform","letter-spacing"],i=0,j=g.length;j>i;i++)e=g[i],f+=e+":"+this.search_field.css(e)+";";return b=a("<div />",{style:f}),b.text(this.search_field.val()),a("body").append(b),h=b.width()+25,b.remove(),c=this.container.outerWidth(),h>c-10&&(h=c-10),this.search_field.css({width:h+"px"})}},Chosen}(AbstractChosen)}.call(this);

/* Easy Responsive Tabs Plugin - Author: Samson.Onna <Email : <EMAIL>> */
!function(t){t.fn.extend({easyResponsiveTabs:function(a){var e={type:"default",width:"auto",fit:!0,closed:!1,activate:function(){}},a=t.extend(e,a),s=a,i=s.type,r=s.fit,n=s.width,c="tab-top",o="tab-bottom",d="tab-left";tabsRight="tab-right",accord="accordion";var l=window.location.hash,p=!(!window.history||!history.replaceState);t(this).bind("tabactivate",function(t,e){"function"==typeof a.activate&&a.activate.call(e,t)}),this.each(function(){function e(){if(i==c&&s.addClass("resp-tabs-top"),i==o){var t=s.addClass("resp-tabs-bottom");t.find("ul.resp-tabs-list").insertAfter(t.find(".resp-tabs-container"))}i==d&&s.addClass("resp-tabs-left"),i==tabsRight&&s.addClass("resp-tabs-right"),1==r&&s.css({width:"100%",margin:"0px"}),i==accord&&(s.addClass("resp-accordion"),s.find(".resp-tabs-list").css("display","none"))}var s=t(this),b=s.find("ul.resp-tabs-list"),f=s.attr("id");s.find("ul.resp-tabs-list li").addClass("resp-tab-item"),s.css({display:"block",width:n}),s.find(".resp-tabs-container > div").addClass("resp-tab-content"),e();var v;s.find(".resp-tab-content").before("<h4 class='resp-accordion' role='tab'><span class='resp-arrow'></span></h4>");var h=0;s.find(".resp-accordion").each(function(){v=t(this);var a=s.find(".resp-tab-item:eq("+h+")"),e=s.find(".resp-accordion:eq("+h+")");e.append(a.html()),e.data(a.data()),v.attr("aria-controls","tab_item-"+h),h++});var u,C=0;s.find(".resp-tab-item").each(function(){$tabItem=t(this),$tabItem.attr("aria-controls","tab_item-"+C),$tabItem.attr("role","tab");var a=0;s.find(".resp-tab-content").each(function(){u=t(this),u.attr("aria-labelledby","tab_item-"+a),a++}),C++});var m=0;if(""!=l){var y=l.match(new RegExp(f+"([0-9]+)"));null!==y&&2===y.length&&(m=parseInt(y[1],10)-1,m>C&&(m=0))}t(s.find(".resp-tab-item")[m]).addClass("resp-tab-active"),a.closed===!0||"accordion"===a.closed&&!b.is(":visible")||"tabs"===a.closed&&b.is(":visible")?t(s.find(".resp-tab-content")[m]).addClass("resp-tab-content-active resp-accordion-closed"):(t(s.find(".resp-accordion")[m]).addClass("resp-tab-active"),t(s.find(".resp-tab-content")[m]).addClass("resp-tab-content-active").attr("style","display:block")),s.find("[role=tab]").each(function(){var a=t(this);a.click(function(){var a=t(this),e=a.attr("aria-controls");if(a.hasClass("resp-accordion")&&a.hasClass("resp-tab-active"))return s.find(".resp-tab-content-active").slideUp("",function(){t(this).addClass("resp-accordion-closed")}),a.removeClass("resp-tab-active"),!1;if(!a.hasClass("resp-tab-active")&&a.hasClass("resp-accordion")?(s.find(".resp-tab-active").removeClass("resp-tab-active"),s.find(".resp-tab-content-active").slideUp().removeClass("resp-tab-content-active resp-accordion-closed"),s.find("[aria-controls="+e+"]").addClass("resp-tab-active"),s.find(".resp-tab-content[aria-labelledby = "+e+"]").slideDown().addClass("resp-tab-content-active")):(s.find(".resp-tab-active").removeClass("resp-tab-active"),s.find(".resp-tab-content-active").removeAttr("style").removeClass("resp-tab-content-active").removeClass("resp-accordion-closed"),s.find("[aria-controls="+e+"]").addClass("resp-tab-active"),s.find(".resp-tab-content[aria-labelledby = "+e+"]").addClass("resp-tab-content-active").attr("style","display:block")),a.trigger("tabactivate",a),p){var i=window.location.hash,r=f+(parseInt(e.substring(9),10)+1).toString();if(""!=i){var n=new RegExp(f+"[0-9]+");r=null!=i.match(n)?i.replace(n,r):i+"|"+r}else r="#"+r;history.replaceState(null,null,r)}})}),t(window).resize(function(){s.find(".resp-accordion-closed").removeAttr("style")})})}})}(jQuery);

/*! SmartMenus jQuery Plugin - v0.9.6 - March 27, 2014
 * http://www.smartmenus.org/
 * Copyright 2014 Vasil Dinkov, Vadikom Web Ltd. http://vadikom.com; Licensed MIT */
(function(t){function s(s){if(h||s)h&&s&&(t(document).unbind(".smartmenus_mouse"),h=!1);else{var e=!0,o=null;t(document).bind({"mousemove.smartmenus_mouse":function(s){var a={x:s.pageX,y:s.pageY,timeStamp:(new Date).getTime()};if(o){var r=Math.abs(o.x-a.x),h=Math.abs(o.y-a.y);if((r>0||h>0)&&2>=r&&2>=h&&300>=a.timeStamp-o.timeStamp&&(n=!0,e)){var u=t(s.target).closest("a");u.is("a")&&t.each(i,function(){return t.contains(this.$root[0],u[0])?(this.itemEnter({currentTarget:u[0]}),!1):void 0}),e=!1}}o=a},"touchstart.smartmenus_mouse pointerover.smartmenus_mouse MSPointerOver.smartmenus_mouse":function(t){/^(4|mouse)$/.test(t.originalEvent.pointerType)||(n=!1)}}),h=!0}}var i=[],e=!!window.createPopup,o=e&&!document.defaultView,a=e&&!document.querySelector,r=e&&document.documentElement.currentStyle.minWidth===void 0,n=!1,h=!1;t.SmartMenus=function(s,i){this.$root=t(s),this.opts=i,this.rootId="",this.$subArrow=null,this.subMenus=[],this.activatedItems=[],this.visibleSubMenus=[],this.showTimeout=0,this.hideTimeout=0,this.scrollTimeout=0,this.clickActivated=!1,this.zIndexInc=0,this.$firstLink=null,this.$firstSub=null,this.disabled=!1,this.$disableOverlay=null,this.init()},t.extend(t.SmartMenus,{hideAll:function(){t.each(i,function(){this.menuHideAll()})},destroy:function(){for(;i.length;)i[0].destroy();s(!0)},prototype:{init:function(e){var o=this;if(!e){i.push(this),this.rootId=((new Date).getTime()+Math.random()+"").replace(/\D/g,""),this.$root.hasClass("sm-rtl")&&(this.opts.rightToLeftSubMenus=!0),this.$root.data("smartmenus",this).attr("data-smartmenus-id",this.rootId).dataSM("level",1).bind({"mouseover.smartmenus focusin.smartmenus":t.proxy(this.rootOver,this),"mouseout.smartmenus focusout.smartmenus":t.proxy(this.rootOut,this)}).delegate("a",{"mouseenter.smartmenus":t.proxy(this.itemEnter,this),"mouseleave.smartmenus":t.proxy(this.itemLeave,this),"mousedown.smartmenus":t.proxy(this.itemDown,this),"focus.smartmenus":t.proxy(this.itemFocus,this),"blur.smartmenus":t.proxy(this.itemBlur,this),"click.smartmenus":t.proxy(this.itemClick,this),"touchend.smartmenus":t.proxy(this.itemTouchEnd,this)});var a=".smartmenus"+this.rootId;this.opts.hideOnClick&&t(document).bind("touchstart"+a,t.proxy(this.docTouchStart,this)).bind("touchmove"+a,t.proxy(this.docTouchMove,this)).bind("touchend"+a,t.proxy(this.docTouchEnd,this)).bind("click"+a,t.proxy(this.docClick,this)),t(window).bind("resize"+a+" orientationchange"+a,t.proxy(this.winResize,this)),this.opts.subIndicators&&(this.$subArrow=t("<span/>").addClass("sub-arrow"),this.opts.subIndicatorsText&&this.$subArrow.html(this.opts.subIndicatorsText)),s()}if(this.$firstSub=this.$root.find("ul").each(function(){o.menuInit(t(this))}).eq(0),this.$firstLink=this.$root.find("a").eq(0),this.opts.markCurrentItem){var r=/(index|default)\.[^#\?\/]*/i,n=/#.*/,h=window.location.href.replace(r,""),u=h.replace(n,"");this.$root.find("a").each(function(){var s=this.href.replace(r,""),i=t(this);(s==h||s==u)&&(i.addClass("current"),o.opts.markCurrentTree&&i.parents("li").each(function(){var s=t(this);s.dataSM("sub")&&s.children("a").addClass("current")}))})}},destroy:function(){this.menuHideAll(),this.$root.removeData("smartmenus").removeAttr("data-smartmenus-id").removeDataSM("level").unbind(".smartmenus").undelegate(".smartmenus");var s=".smartmenus"+this.rootId;t(document).unbind(s),t(window).unbind(s),this.opts.subIndicators&&(this.$subArrow=null);var e=this;t.each(this.subMenus,function(){this.hasClass("mega-menu")&&this.find("ul").removeDataSM("in-mega"),this.dataSM("shown-before")&&(a&&this.children().css({styleFloat:"",width:""}),(e.opts.subMenusMinWidth||e.opts.subMenusMaxWidth)&&(r?this.css({width:"",overflowX:"",overflowY:""}).children().children("a").css("white-space",""):this.css({width:"",minWidth:"",maxWidth:""}).removeClass("sm-nowrap")),this.dataSM("scroll-arrows")&&this.dataSM("scroll-arrows").remove(),this.css({zIndex:"",top:"",left:"",marginLeft:"",marginTop:"",display:""})),e.opts.subIndicators&&this.dataSM("parent-a").removeClass("has-submenu").children("span.sub-arrow").remove(),this.removeDataSM("shown-before").removeDataSM("ie-shim").removeDataSM("scroll-arrows").removeDataSM("parent-a").removeDataSM("level").removeDataSM("beforefirstshowfired").parent().removeDataSM("sub")}),this.opts.markCurrentItem&&this.$root.find("a.current").removeClass("current"),this.$root=null,this.$firstLink=null,this.$firstSub=null,this.$disableOverlay&&(this.$disableOverlay.remove(),this.$disableOverlay=null),i.splice(t.inArray(this,i),1)},disable:function(s){if(!this.disabled){if(this.menuHideAll(),!s&&!this.opts.isPopup&&this.$root.is(":visible")){var i=this.$root.offset();this.$disableOverlay=t('<div class="sm-jquery-disable-overlay"/>').css({position:"absolute",top:i.top,left:i.left,width:this.$root.outerWidth(),height:this.$root.outerHeight(),zIndex:this.getStartZIndex()+1,opacity:0}).appendTo(document.body)}this.disabled=!0}},docClick:function(s){(this.visibleSubMenus.length&&!t.contains(this.$root[0],s.target)||t(s.target).is("a"))&&this.menuHideAll()},docTouchEnd:function(){if(this.lastTouch){if(!(!this.visibleSubMenus.length||void 0!==this.lastTouch.x2&&this.lastTouch.x1!=this.lastTouch.x2||void 0!==this.lastTouch.y2&&this.lastTouch.y1!=this.lastTouch.y2||this.lastTouch.target&&t.contains(this.$root[0],this.lastTouch.target))){this.hideTimeout&&(clearTimeout(this.hideTimeout),this.hideTimeout=0);var s=this;this.hideTimeout=setTimeout(function(){s.menuHideAll()},350)}this.lastTouch=null}},docTouchMove:function(t){if(this.lastTouch){var s=t.originalEvent.touches[0];this.lastTouch.x2=s.pageX,this.lastTouch.y2=s.pageY}},docTouchStart:function(t){var s=t.originalEvent.touches[0];this.lastTouch={x1:s.pageX,y1:s.pageY,target:s.target}},enable:function(){this.disabled&&(this.$disableOverlay&&(this.$disableOverlay.remove(),this.$disableOverlay=null),this.disabled=!1)},getHeight:function(t){return this.getOffset(t,!0)},getOffset:function(t,s){var i;"none"==t.css("display")&&(i={position:t[0].style.position,visibility:t[0].style.visibility},t.css({position:"absolute",visibility:"hidden"}).show());var e=t[0].ownerDocument.defaultView,o=e&&e.getComputedStyle&&e.getComputedStyle(t[0],null),a=o&&parseFloat(o[s?"height":"width"]);return a?a+=parseFloat(o[s?"paddingTop":"paddingLeft"])+parseFloat(o[s?"paddingBottom":"paddingRight"])+parseInt(o[s?"borderTopWidth":"borderLeftWidth"])+parseInt(o[s?"borderBottomWidth":"borderRightWidth"]):a=s?t[0].offsetHeight:t[0].offsetWidth,i&&t.hide().css(i),a},getWidth:function(t){return this.getOffset(t)},getStartZIndex:function(){var t=parseInt(this.$root.css("z-index"));return isNaN(t)?1:t},handleEvents:function(){return!this.disabled&&this.isCSSOn()},handleItemEvents:function(t){return this.handleEvents()&&!this.isLinkInMegaMenu(t)},isCollapsible:function(){return"static"==this.$firstSub.css("position")},isCSSOn:function(){return"block"==this.$firstLink.css("display")},isFixed:function(){return"fixed"==this.$root.css("position")},isLinkInMegaMenu:function(t){return!t.parent().parent().dataSM("level")},isTouchMode:function(){return!n||this.isCollapsible()},itemActivate:function(s){var i=s.parent(),e=i.parent(),o=e.dataSM("level");if(o>1&&(!this.activatedItems[o-2]||this.activatedItems[o-2][0]!=e.dataSM("parent-a")[0])){var a=this;t(e.parentsUntil("[data-smartmenus-id]","ul").get().reverse()).add(e).each(function(){a.itemActivate(t(this).dataSM("parent-a"))})}if(this.visibleSubMenus.length>o)for(var r=this.visibleSubMenus.length-1,n=this.activatedItems[o-1]&&this.activatedItems[o-1][0]==s[0]?o:o-1;r>n;r--)this.menuHide(this.visibleSubMenus[r]);if(this.activatedItems[o-1]=s,this.visibleSubMenus[o-1]=e,this.$root.triggerHandler("activate.smapi",s[0])!==!1){var h=i.dataSM("sub");h&&(this.isTouchMode()||!this.opts.showOnClick||this.clickActivated)&&this.menuShow(h)}},itemBlur:function(s){var i=t(s.currentTarget);this.handleItemEvents(i)&&this.$root.triggerHandler("blur.smapi",i[0])},itemClick:function(s){var i=t(s.currentTarget);if(this.handleItemEvents(i)){if(i.removeDataSM("mousedown"),this.$root.triggerHandler("click.smapi",i[0])===!1)return!1;var e=i.parent().dataSM("sub");if(this.isTouchMode()){if(i.dataSM("href")&&i.attr("href",i.dataSM("href")).removeDataSM("href"),e&&(!e.dataSM("shown-before")||!e.is(":visible"))&&(this.itemActivate(i),e.is(":visible")))return!1}else if(this.opts.showOnClick&&1==i.parent().parent().dataSM("level")&&e)return this.clickActivated=!0,this.menuShow(e),!1;return i.hasClass("disabled")?!1:this.$root.triggerHandler("select.smapi",i[0])===!1?!1:void 0}},itemDown:function(s){var i=t(s.currentTarget);this.handleItemEvents(i)&&i.dataSM("mousedown",!0)},itemEnter:function(s){var i=t(s.currentTarget);if(this.handleItemEvents(i)){if(!this.isTouchMode()){this.showTimeout&&(clearTimeout(this.showTimeout),this.showTimeout=0);var e=this;this.showTimeout=setTimeout(function(){e.itemActivate(i)},this.opts.showOnClick&&1==i.parent().parent().dataSM("level")?1:this.opts.showTimeout)}this.$root.triggerHandler("mouseenter.smapi",i[0])}},itemFocus:function(s){var i=t(s.currentTarget);this.handleItemEvents(i)&&(this.isTouchMode()&&i.dataSM("mousedown")||this.activatedItems.length&&this.activatedItems[this.activatedItems.length-1][0]==i[0]||this.itemActivate(i),this.$root.triggerHandler("focus.smapi",i[0]))},itemLeave:function(s){var i=t(s.currentTarget);this.handleItemEvents(i)&&(this.isTouchMode()||(i[0].blur&&i[0].blur(),this.showTimeout&&(clearTimeout(this.showTimeout),this.showTimeout=0)),i.removeDataSM("mousedown"),this.$root.triggerHandler("mouseleave.smapi",i[0]))},itemTouchEnd:function(s){var i=t(s.currentTarget);if(this.handleItemEvents(i)){var e=i.parent().dataSM("sub");"#"===i.attr("href").charAt(0)||!e||e.dataSM("shown-before")&&e.is(":visible")||(i.dataSM("href",i.attr("href")),i.attr("href","#"))}},menuFixLayout:function(t){t.dataSM("shown-before")||(t.hide().dataSM("shown-before",!0),a&&t.children().css({styleFloat:"left",width:"100%"}))},menuHide:function(t){if(this.$root.triggerHandler("beforehide.smapi",t[0])!==!1&&(t.stop(!0,!0),t.is(":visible"))){var s=function(){o?t.parent().css("z-index",""):t.css("z-index","")};this.isCollapsible()?this.opts.collapsibleHideFunction?this.opts.collapsibleHideFunction.call(this,t,s):t.hide(this.opts.collapsibleHideDuration,s):this.opts.hideFunction?this.opts.hideFunction.call(this,t,s):t.hide(this.opts.hideDuration,s),t.dataSM("ie-shim")&&t.dataSM("ie-shim").remove(),t.dataSM("scroll")&&t.unbind(".smartmenus_scroll").removeDataSM("scroll").dataSM("scroll-arrows").hide(),t.dataSM("parent-a").removeClass("highlighted");var i=t.dataSM("level");this.activatedItems.splice(i-1,1),this.visibleSubMenus.splice(i-1,1),this.$root.triggerHandler("hide.smapi",t[0])}},menuHideAll:function(){this.showTimeout&&(clearTimeout(this.showTimeout),this.showTimeout=0);for(var t=this.visibleSubMenus.length-1;t>0;t--)this.menuHide(this.visibleSubMenus[t]);this.opts.isPopup&&(this.$root.stop(!0,!0),this.$root.is(":visible")&&(this.opts.hideFunction?this.opts.hideFunction.call(this,this.$root):this.$root.hide(this.opts.hideDuration),this.$root.dataSM("ie-shim")&&this.$root.dataSM("ie-shim").remove())),this.activatedItems=[],this.visibleSubMenus=[],this.clickActivated=!1,this.zIndexInc=0},menuIframeShim:function(s){e&&this.opts.overlapControlsInIE&&!s.dataSM("ie-shim")&&s.dataSM("ie-shim",t("<iframe/>").attr({src:"javascript:0",tabindex:-9}).css({position:"absolute",top:"auto",left:"0",opacity:0,border:"0"}))},menuInit:function(t){if(!t.dataSM("in-mega")){this.subMenus.push(t),t.hasClass("mega-menu")&&t.find("ul").dataSM("in-mega",!0);for(var s=2,i=t[0];(i=i.parentNode.parentNode)!=this.$root[0];)s++;t.dataSM("parent-a",t.prevAll("a").eq(-1)).dataSM("level",s).parent().dataSM("sub",t),this.opts.subIndicators&&t.dataSM("parent-a").addClass("has-submenu")[this.opts.subIndicatorsPos](this.$subArrow.clone())}},menuPosition:function(s){var i,e,o=s.dataSM("parent-a"),a=s.parent().parent(),r=s.dataSM("level"),h=this.getWidth(s),u=this.getHeight(s),l=o.offset(),d=l.left,c=l.top,m=this.getWidth(o),p=this.getHeight(o),f=t(window),v=f.scrollLeft(),b=f.scrollTop(),M=f.width(),S=f.height(),w=a.hasClass("sm")&&!a.hasClass("sm-vertical"),g=2==r?this.opts.mainMenuSubOffsetX:this.opts.subMenusSubOffsetX,T=2==r?this.opts.mainMenuSubOffsetY:this.opts.subMenusSubOffsetY;if(w?(i=this.opts.rightToLeftSubMenus?m-h-g:g,e=this.opts.bottomToTopSubMenus?-u-T:p+T):(i=this.opts.rightToLeftSubMenus?g-h:m-g,e=this.opts.bottomToTopSubMenus?p-T-u:T),this.opts.keepInViewport&&!this.isCollapsible()){this.isFixed()&&(d-=v,c-=b,v=b=0);var $=d+i,I=c+e;if(this.opts.rightToLeftSubMenus&&v>$?i=w?v-$+i:m-g:!this.opts.rightToLeftSubMenus&&$+h>v+M&&(i=w?v+M-h-$+i:g-h),w||(S>u&&I+u>b+S?e+=b+S-u-I:(u>=S||b>I)&&(e+=b-I)),n&&(w&&(I+u>b+S+.49||b>I)||!w&&u>S+.49)){var y=this;s.dataSM("scroll-arrows")||s.dataSM("scroll-arrows",t([t('<span class="scroll-up"><span class="scroll-up-arrow"></span></span>')[0],t('<span class="scroll-down"><span class="scroll-down-arrow"></span></span>')[0]]).bind({mouseenter:function(){y.menuScroll(s,t(this).hasClass("scroll-up"))},mouseleave:function(t){y.menuScrollStop(s),y.menuScrollOut(s,t)},"mousewheel DOMMouseScroll":function(t){t.preventDefault()}}).insertAfter(s));var x=b-(c+p);s.dataSM("scroll",{vportY:x,subH:u,winH:S,step:1}).bind({"mouseover.smartmenus_scroll":function(t){y.menuScrollOver(s,t)},"mouseout.smartmenus_scroll":function(t){y.menuScrollOut(s,t)},"mousewheel.smartmenus_scroll DOMMouseScroll.smartmenus_scroll":function(t){y.menuScrollMousewheel(s,t)}}).dataSM("scroll-arrows").css({top:"auto",left:"0",marginLeft:i+(parseInt(s.css("border-left-width"))||0),width:this.getWidth(s)-(parseInt(s.css("border-left-width"))||0)-(parseInt(s.css("border-right-width"))||0),zIndex:this.getStartZIndex()+this.zIndexInc}).eq(0).css("margin-top",x).end().eq(1).css("margin-top",x+S-this.getHeight(s.dataSM("scroll-arrows").eq(1))).end().eq(w&&this.opts.bottomToTopSubMenus?0:1).show()}}s.css({top:"auto",left:"0",marginLeft:i,marginTop:e-p}),this.menuIframeShim(s),s.dataSM("ie-shim")&&s.dataSM("ie-shim").css({zIndex:s.css("z-index"),width:h,height:u,marginLeft:i,marginTop:e-p})},menuScroll:function(t,s,i){var e=parseFloat(t.css("margin-top")),o=t.dataSM("scroll"),a=o.vportY+(s?0:o.winH-o.subH),r=i||!this.opts.scrollAccelerate?this.opts.scrollStep:Math.floor(t.dataSM("scroll").step);if(t.add(t.dataSM("ie-shim")).css("margin-top",Math.abs(a-e)>r?e+(s?r:-r):a),e=parseFloat(t.css("margin-top")),(s&&e+o.subH>o.vportY+o.winH||!s&&o.vportY>e)&&t.dataSM("scroll-arrows").eq(s?1:0).show(),!i&&this.opts.scrollAccelerate&&t.dataSM("scroll").step<this.opts.scrollStep&&(t.dataSM("scroll").step+=.5),1>Math.abs(e-a))t.dataSM("scroll-arrows").eq(s?0:1).hide(),t.dataSM("scroll").step=1;else if(!i){var n=this;this.scrollTimeout=setTimeout(function(){n.menuScroll(t,s)},this.opts.scrollInterval)}},menuScrollMousewheel:function(s,i){for(var e=t(i.target).closest("ul");e.dataSM("in-mega");)e=e.parent().closest("ul");if(e[0]==s[0]){var o=(i.originalEvent.wheelDelta||-i.originalEvent.detail)>0;s.dataSM("scroll-arrows").eq(o?0:1).is(":visible")&&this.menuScroll(s,o,!0)}i.preventDefault()},menuScrollOut:function(s,i){for(var e=/^scroll-(up|down)/,o=t(i.relatedTarget).closest("ul");o.dataSM("in-mega");)o=o.parent().closest("ul");e.test((i.relatedTarget||"").className)||(s[0]==i.relatedTarget||t.contains(s[0],i.relatedTarget))&&o[0]==s[0]||s.dataSM("scroll-arrows").css("visibility","hidden")},menuScrollOver:function(s,i){for(var e=/^scroll-(up|down)/,o=t(i.target).closest("ul");o.dataSM("in-mega");)o=o.parent().closest("ul");e.test(i.target.className)||o[0]!=s[0]||s.dataSM("scroll-arrows").css("visibility","visible")},menuScrollStop:function(t){this.scrollTimeout&&(clearTimeout(this.scrollTimeout),this.scrollTimeout=0,t.dataSM("scroll").step=1)},menuShow:function(t){if((t.dataSM("beforefirstshowfired")||(t.dataSM("beforefirstshowfired",!0),this.$root.triggerHandler("beforefirstshow.smapi",t[0])!==!1))&&this.$root.triggerHandler("beforeshow.smapi",t[0])!==!1&&(this.menuFixLayout(t),t.stop(!0,!0),!t.is(":visible"))){var s=this.getStartZIndex()+ ++this.zIndexInc;if(o?t.parent().css("z-index",s):t.css("z-index",s),(this.opts.keepHighlighted||this.isCollapsible())&&t.dataSM("parent-a").addClass("highlighted"),this.opts.subMenusMinWidth||this.opts.subMenusMaxWidth)if(a){if(t.children().css("styleFloat","none"),r?t.width(this.opts.subMenusMinWidth?this.opts.subMenusMinWidth:1).children().children("a").css("white-space","nowrap"):(t.css({width:"auto",minWidth:"",maxWidth:""}).addClass("sm-nowrap"),this.opts.subMenusMinWidth&&t.css("min-width",this.opts.subMenusMinWidth)),this.opts.subMenusMaxWidth){var i=t.width();if(r){var e=t.css({width:this.opts.subMenusMaxWidth,overflowX:"hidden",overflowY:"hidden"}).width();i>e?t.css({width:e,overflowX:"visible",overflowY:"visible"}).children().children("a").css("white-space",""):t.css({width:i,overflowX:"visible",overflowY:"visible"})}else t.css("max-width",this.opts.subMenusMaxWidth),i>t.width()?t.removeClass("sm-nowrap").css("width",this.opts.subMenusMaxWidth):t.width(i)}else t.width(t.width());t.children().css("styleFloat","left")}else if(t.css({width:"auto",minWidth:"",maxWidth:""}).addClass("sm-nowrap"),this.opts.subMenusMinWidth&&t.css("min-width",this.opts.subMenusMinWidth),this.opts.subMenusMaxWidth){var i=this.getWidth(t);t.css("max-width",this.opts.subMenusMaxWidth),i>this.getWidth(t)&&t.removeClass("sm-nowrap").css("width",this.opts.subMenusMaxWidth)}this.menuPosition(t),t.dataSM("ie-shim")&&t.dataSM("ie-shim").insertBefore(t);var n=function(){t.css("overflow","")};this.isCollapsible()?this.opts.collapsibleShowFunction?this.opts.collapsibleShowFunction.call(this,t,n):t.show(this.opts.collapsibleShowDuration,n):this.opts.showFunction?this.opts.showFunction.call(this,t,n):t.show(this.opts.showDuration,n),this.visibleSubMenus[t.dataSM("level")-1]=t,this.$root.triggerHandler("show.smapi",t[0])}},popupHide:function(t){this.hideTimeout&&(clearTimeout(this.hideTimeout),this.hideTimeout=0);var s=this;this.hideTimeout=setTimeout(function(){s.menuHideAll()},t?1:this.opts.hideTimeout)},popupShow:function(t,s){return this.opts.isPopup?(this.hideTimeout&&(clearTimeout(this.hideTimeout),this.hideTimeout=0),this.menuFixLayout(this.$root),this.$root.stop(!0,!0),this.$root.is(":visible")||(this.$root.css({left:t,top:s}),this.menuIframeShim(this.$root),this.$root.dataSM("ie-shim")&&this.$root.dataSM("ie-shim").css({zIndex:this.$root.css("z-index"),width:this.getWidth(this.$root),height:this.getHeight(this.$root),left:t,top:s}).insertBefore(this.$root),this.opts.showFunction?this.opts.showFunction.call(this,this.$root):this.$root.show(this.opts.showDuration),this.visibleSubMenus[0]=this.$root),void 0):(alert('SmartMenus jQuery Error:\n\nIf you want to show this menu via the "popupShow" method, set the isPopup:true option.'),void 0)},refresh:function(){this.menuHideAll(),this.$root.find("ul").each(function(){var s=t(this);s.dataSM("scroll-arrows")&&s.dataSM("scroll-arrows").remove()}).removeDataSM("in-mega").removeDataSM("shown-before").removeDataSM("ie-shim").removeDataSM("scroll-arrows").removeDataSM("parent-a").removeDataSM("level").removeDataSM("beforefirstshowfired"),this.$root.find("a.has-submenu").removeClass("has-submenu").parent().removeDataSM("sub"),this.opts.subIndicators&&this.$root.find("span.sub-arrow").remove(),this.opts.markCurrentItem&&this.$root.find("a.current").removeClass("current"),this.subMenus=[],this.init(!0)},rootOut:function(t){if(this.handleEvents()&&!this.isTouchMode()&&t.target!=this.$root[0]&&(this.hideTimeout&&(clearTimeout(this.hideTimeout),this.hideTimeout=0),!this.opts.showOnClick||!this.opts.hideOnClick)){var s=this;this.hideTimeout=setTimeout(function(){s.menuHideAll()},this.opts.hideTimeout)}},rootOver:function(t){this.handleEvents()&&!this.isTouchMode()&&t.target!=this.$root[0]&&this.hideTimeout&&(clearTimeout(this.hideTimeout),this.hideTimeout=0)},winResize:function(t){if(this.handleEvents())this.isCollapsible()||"onorientationchange"in window&&"orientationchange"!=t.type||(this.activatedItems.length&&this.activatedItems[this.activatedItems.length-1][0].blur(),this.menuHideAll());else if(this.$disableOverlay){var s=this.$root.offset();this.$disableOverlay.css({top:s.top,left:s.left,width:this.$root.outerWidth(),height:this.$root.outerHeight()})}}}}),t.fn.dataSM=function(t,s){return s?this.data(t+"_smartmenus",s):this.data(t+"_smartmenus")},t.fn.removeDataSM=function(t){return this.removeData(t+"_smartmenus")},t.fn.smartmenus=function(s){if("string"==typeof s){var i=arguments,e=s;return Array.prototype.shift.call(i),this.each(function(){var s=t(this).data("smartmenus");s&&s[e]&&s[e].apply(s,i)})}var o=t.extend({},t.fn.smartmenus.defaults,s);return this.each(function(){new t.SmartMenus(this,o)})},t.fn.smartmenus.defaults={isPopup:!1,mainMenuSubOffsetX:0,mainMenuSubOffsetY:0,subMenusSubOffsetX:0,subMenusSubOffsetY:0,subMenusMinWidth:"10em",subMenusMaxWidth:"20em",subIndicators:!0,subIndicatorsPos:"prepend",subIndicatorsText:"+",scrollStep:30,scrollInterval:30,scrollAccelerate:!0,showTimeout:250,hideTimeout:500,showDuration:0,showFunction:null,hideDuration:0,hideFunction:function(t,s){t.fadeOut(200,s)},collapsibleShowDuration:0,collapsibleShowFunction:function(t,s){t.slideDown(200,s)},collapsibleHideDuration:0,collapsibleHideFunction:function(t,s){t.slideUp(200,s)},showOnClick:!1,hideOnClick:!0,keepInViewport:!0,keepHighlighted:!0,markCurrentItem:!1,markCurrentTree:!0,rightToLeftSubMenus:!1,bottomToTopSubMenus:!1,overlapControlsInIE:!0}})(jQuery);

/*
Plugin: jQuery Parallax - Author: Ian Lunn
Twitter: @IanLunn
Author URL: http://www.ianlunn.co.uk/
Plugin URL: http://www.ianlunn.co.uk/plugins/jquery-parallax/
*/
!function(n){var t=n(window),e=t.height();t.resize(function(){e=t.height()}),n.fn.parallax=function(o,i,r){function u(){var r=t.scrollTop();a.each(function(){var t=n(this),u=t.offset().top,c=h(t);r>u+c||u>r+e||a.css("backgroundPosition",o+" "+Math.round((l-r)*i)+"px")})}var h,l,a=n(this);a.each(function(){l=a.offset().top}),h=r?function(n){return n.outerHeight(!0)}:function(n){return n.height()},(arguments.length<1||null===o)&&(o="50%"),(arguments.length<2||null===i)&&(i=.1),(arguments.length<3||null===r)&&(r=!0),t.bind("scroll",u).resize(u),u()}}(jQuery);

(function($){
/* --------------------------------------------------------------------------
 * Menu Configuration
 * -------------------------------------------------------------------------- */
   "use strict";
   
   if ($.fn.smartmenus) {
         $.fn.foundstrapmenu = function (options) {
            return this.each(function() {
               var self = $(this);

               self.smartmenus({
                  mainMenuSubOffsetX: 0,
                  mainMenuSubOffsetY: 0,    
                  subMenusSubOffsetX: -1,
                  subMenusSubOffsetY: -44,
                  subIndicatorsText : ''
               }).find('li.active').children('a').addClass('active');
            });
         };
   } else {
      console.log("menu requires jQuery smartmenus plugin");
   }

})(jQuery);

jQuery(document).ready(function($) {
	/* --------------------------------------------------------------------------
	 * jQuery menu
	 * -------------------------------------------------------------------------- */
	$("#menu").foundstrapmenu();

	/* --------------------------------------------------------------------------
	 * jQuery check box and radio button
	 * -------------------------------------------------------------------------- */
	jQuery('input').iCheck({
		checkboxClass: 'me-checkbox',
		radioClass: 'me-radio'
	});

	jQuery('.me-radio .radio-disable, .me-checkbox .checkbox-disable').iCheck('disable');

	var config = {
      '.chosen-select'           : {disable_search_threshold: 10, width:"100%"},
      '.chosen-select-deselect'  : {allow_single_deselect:true, width:"100%"},
      '.chosen-select-no-single' : {disable_search_threshold:10, width:"100%"},
      '.chosen-select-no-results': {no_results_text:'Oops, nothing found!'},
      '.chosen-select-width'     : {width:"95%"}
    };
    
    for (var selector in config) {
      $(selector).chosen(config[selector]);
    } 

    /* --------------------------------------------------------------------------
	 * jQuery Responsive Tabs
	 * -------------------------------------------------------------------------- */
    $('.top-tab').easyResponsiveTabs({ type : 'tab-top' });
    $('.bottom-tab').easyResponsiveTabs({ type : 'tab-bottom' });
    $('.left-tab').easyResponsiveTabs({ type : 'tab-left' });
    $('.right-tab').easyResponsiveTabs({ type : 'tab-right' });
    $('.accordion').easyResponsiveTabs({ type : 'accordion' });

    /* --------------------------------------------------------------------------
	 * jQuery Gap
	 * -------------------------------------------------------------------------- */
    $('.gap').each(function () {
    	var self = $(this),
    	dataGap = self.data();

    	$.each(dataGap, function(direction, val) {
    		if (direction == "gapTop")    { self.css("margin-top", val+'px'); }
    		if (direction == "gapBottom") { self.css("margin-bottom", val+'px'); }
    		if (direction == "gapLeft")   { self.css("margin-left", val+'px'); }
    		if (direction == "gapRight")  { self.css("margin-right", val+'px'); }
    	});
    });	

    /* --------------------------------------------------------------------------
     * jQuery alert
     * -------------------------------------------------------------------------- */
    $('a[data-component="alert"]').each(function() {
    	$(this).click(function(e){
    		e.preventDefault();
    		$(this).parent().fadeOut("slow");
       		// if you want to delet tag use remove()
    	});   
    });
});