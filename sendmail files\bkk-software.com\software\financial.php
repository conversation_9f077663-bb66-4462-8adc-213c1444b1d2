<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';

$picCnt = 0;

?>
<!DOCTYPE html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html lang="en" class="no-js"> <!--<![endif]-->
<head>
   <meta charset="utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta name="description" content="MySaaS Financial Software is a robust, efficient finance &amp; accounting software consisting of several sub-modules for managing general ledgers.">
   <meta name="keywords" content="Financial software , Financial management systems, accounting software, cloud financial software, accounting software online, Financial module">
   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">

   <title>Financial & Accounting Software</title>


<?php echo getPgCss(); ?>
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-main.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
            <h2>Financial Management</h2>
            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
               <p>MySaaS Financial Systems is a robust and efficient finance &amp; accounting software. It consists   of several sub-modules for managing general ledgers, accounts   payable/receivable, fixed assets, bank accounts and other resources. MySaaS integrates your sales and   service functions for streamlined, end-to-end financial management.   It is a powerful, flexible tools for improve financial reporting, compliance and   provide better business intelligence for a greater ROI.</p>
          
               <p><strong>The web based systems enable you to:</strong>

               <ul class="me-list list-circle-check">

                 <li>Manage all aspects of your general ledger including analysis.</li>
                 <li>Set up an unlimited number of asset and depreciation books.</li>
                 <li>Manage your customer accounts with complete flexibility on currency, taxation, and payment terms.</li>
                 <li>Manage your vendor accounts with complete flexibility on currency, taxation, and payment terms.</li>
                 <li><span id="result_box" lang="en">Define a year and divide it up into periods</span></li>
                 <li>Keep bank and accounts accurate with easy bank reconciliation</li>
                 <li>Create budgets for a day, week, month, quarter, year or a selected accounting period</li>
                 <li>Develop budgets based on department, project or business</li>
               </ul>
               MySaaS Financial Module integrates with MySaaS Operations Module to reduce costs and boost productivity.</p>



            </div>

            <!--Header_section-->
               <?php include('menu-software.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->


<?php echo getPgFootJs(); ?>
</body>
</html>