<?php
	$strTo = "<EMAIL>";
	$strSubject = "=?UTF-8?B?".base64_encode("IT Request Installation for ".$_POST["company"]."")."?=";
	$strHeader = "MIME-Version: 1.0' . \r\n";
	$strHeader .= "Content-type: text/html; charset=utf-8\r\n";
	$strHeader .= "From: <EMAIL>";
	$appList = "";
	for( $i = 1; $i <= 15; $i++ ) {
		$app = 'app' . $i;
		if( isset($_POST[$app]) ){
			$appList .= $_POST[$app] . " / ";
		}
	} 	
	$msg = ' <html><head>
<style type="text/css">
.myTable { background-color:#FFFFFF;border-collapse:collapse; }
.myTable th { background-color:#585858;color:white; }
.myTable td, .myTable th { padding:5px;border:1px solid #000000; }
</style>
<style type="text/css">
.myStaff { background-color:#00000;border-collapse:collapse; }
.myStaff th { background-color:#000000;color:white; }
.myStaff td, .myStaff th { padding:5px;border:1px solid #000000; }
.style3 {font-size: large; font-weight: bold; }
</style>
<style type="text/css" media="screen">
body {
	font: .75em/1.5 "Lucida Grande", "Lucida Sans Unicode", helvetica, verdana, arial, sans-serif;
  	margin: 0 auto;
  	padding: 0 10px;
  	width: 610px;
}
strong {
  background: #ffc;
}
</style></head><body>
<th><p class="style3">New Installation for '.$_POST["company"].'</p></th>
<table width="700" class="myTable">

<tr><th colspan="2" align="left"><p>Customer Information</p></th></tr>
<tr><td colspan="2">Company Name: ' . $_POST[company].  ' (Request by Khun ' . $_POST[requester]. ') 
<br>Contact +66 '. $_POST[contact].' &nbsp;E-mail : '. $_POST[email].' </td> </tr>
<tr><th colspan="2" align="left"><p>Installation Details </p></th></tr>
<tr><td width="160">Operation System </td>
<td width="550">(&nbsp; ) '. $_POST[os].' </td></tr> 
<tr>
  <td>Microsoft Office</td>
  <td>(&nbsp; ) ' . $_POST[office].  ' </td>
</tr>
<tr>
	<td> Default Language</td>
	<td>(&nbsp; ) ' . $_POST[lang]. ' </td>
</tr>
<tr>
	<td> Harddisk Partition</td>
	<td>(&nbsp; ) ' . $_POST[part]. ' </td>
</tr>
<tr>
	<td> Username</td>
	<td>(&nbsp; ) ' . $_POST[username]. ' </td>
</tr>
<tr>
	<td> Password</td>
	<td>(&nbsp; ) ' . $_POST[password]. ' </td>
</tr>
<tr>
  <td valign="top">Application</td>
  <td>(&nbsp; ) ' . $appList. ' </td>
</tr>
  <tr>
    <th colspan="2" align="left"><p>Additional Note</p></th>
  </tr>
  <tr>
    <td colspan="2"> ' . $_POST[notes].  ' </td>
  </tr></table>
  
<th><p class="style3">New Installation Check for Internal</p></th>
<table width="700" class="myStaff">

 <tr><td width="150">&nbsp;Operation System</td> <td width="550">(&nbsp; ) '. $_POST[os].' </td></tr>
  <tr><td>&nbsp;Microsoft Office</td><td> (&nbsp; ) '. $_POST[office].' </td></tr>
  <tr><td>&nbsp;Default Language</td><td> (&nbsp; ) '. $_POST[lang].' </td></tr>
  <tr><td valign="top">&nbsp;Application<br> <td> (&nbsp; )  ' . $appList. ' </td></tr>

<tr><td>&nbsp;Remarked</td><td>&nbsp;&nbsp;&nbsp;</td></tr>
  
</table>
</body>
</html>
';
	
	$flgSend = @mail($strTo,$strSubject,$msg,$strHeader);
	if($flgSend)
	{
		echo "<h1>Email Sending.<br> We will contact you when the installation is completed.</h1>";
		
	}
	else
	{
		echo "<h1>Email Can Not Send. Please try again later.</h1>";
	}
?>
<Meta http-equiv="refresh"content="2;URL=http://www.techspace.co.th/">