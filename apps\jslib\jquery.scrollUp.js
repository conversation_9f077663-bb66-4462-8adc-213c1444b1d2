(function($,window,document){$.fn.scrollUp=function(options){if(!$.data(document.body,'scrollUp')){$.data(document.body,'scrollUp',true);$.fn.scrollUp.init(options);}};$.fn.scrollUp.init=function(options){var o=$.fn.scrollUp.settings=$.extend({},$.fn.scrollUp.defaults,options),scrollTitle=(o.scrollTitle)?o.scrollTitle:o.scrollText,$self;if(o.scrollTrigger){$self=$(o.scrollTrigger);}else{$self=$('<a/>',{id:o.scrollName,href:'#top',title:scrollTitle});}$self.appendTo('body');if(!(o.scrollImg||o.scrollTrigger)){$self.html(o.scrollText);}$self.css({display:'none',position:'fixed',zIndex:o.zIndex});if(o.activeOverlay){$('<div/>',{id:o.scrollName+'-active'}).css({position:'absolute','top':o.scrollDistance+'px',width:'100%',borderTop:'1px dotted'+o.activeOverlay,zIndex:o.zIndex}).appendTo('body');}scrollEvent=$(window).scroll(function(){if(o.scrollFrom==='top'){scrollDis=o.scrollDistance;}else{scrollDis=$(document).height()-$(window).height()-o.scrollDistance;}switch(o.animation){case'fade':$(($(window).scrollTop()>scrollDis)?$self.fadeIn(o.animationInSpeed):$self.fadeOut(o.animationOutSpeed));break;case'slide':$(($(window).scrollTop()>scrollDis)?$self.slideDown(o.animationInSpeed):$self.slideUp(o.animationOutSpeed));break;default:$(($(window).scrollTop()>scrollDis)?$self.show(0):$self.hide(0));}});$self.click(function(e){e.preventDefault();$('html, body').animate({scrollTop:0},o.scrollSpeed,o.easingType);});};$.fn.scrollUp.defaults={scrollName:'scrollUp',scrollDistance:300,scrollFrom:'top',scrollSpeed:300,easingType:'linear',animation:'fade',animationInSpeed:200,animationOutSpeed:200,scrollTrigger:false,scrollText:'Scroll to top',scrollTitle:false,scrollImg:false,activeOverlay:false,zIndex:2147483647};$.fn.scrollUp.destroy=function(scrollEvent){$.removeData(document.body,'scrollUp');$('#'+$.fn.scrollUp.settings.scrollName).remove();$('#'+$.fn.scrollUp.settings.scrollName+'-active').remove();if($.fn.jquery.split('.')[1]>=7){$(window).off('scroll',scrollEvent);}else{$(window).unbind('scroll',scrollEvent);}};$.scrollUp=$.fn.scrollUp;})(jQuery,window,document);