<!DOCTYPE html>
<html lang="en">

<?php
$title = 'Contact Us - Global IT Partner';
$description = 'Get in touch with Global IT Partner for enterprise IT solutions and managed services. Contact our offices in Singapore, Thailand, and USA for professional IT consulting and support.';
$keywords = 'contact global it partner, IT support contact, enterprise IT consultation, Singapore IT services contact, Thailand IT support, USA technology solutions contact';
?>
<?php include './partials/head.php' ?>

<body>

    <!-- Preloader area start -->
    <?php // include './partials/preloader.php' ?>
    <!-- Preloader area end -->

    <!-- Mouse cursor area start here -->
    <?php // include './partials/cursor.php' ?>
    <!-- Mouse cursor area end here -->

    <!-- Top header area start here -->
    <?php include './partials/header-top.php' ?>
    <!-- Top header area end here -->

    <?php
    // Set header parameters
    $headerClass = "header-area";
    $logoImage = "assets/images/logo/globalitpartner-white-logo.png";
    $includeMegaMenu = true;

    // Include header
    include "./partials/header.php";
    ?>

    <!-- Sidebar area start here -->
    <?php include './partials/sidebar.php' ?>
    <!-- Sidebar area end here -->

    <!-- Fullscreen search area start here -->
    <?php include './partials/search.php' ?>
    <!-- Fullscreen search area end here -->

    <main>
        <!-- Page banner area start here -->
        <?php
        $img = 'assets/images/banner/contact-banner.jpg';
        $Title = 'Home';
        $Title2 = 'Contact Us';
        ?>
        <?php include './partials/page-header.php' ?>
        <!-- Page banner area end here -->

        <!-- Contact area start here -->
        <section class="contact-area pt-120 pb-120">
            <div class="container">
                <div class="row g-4">
                    <div class="col-lg-5">
                        <div class="contact__left-item primary-bg">
                            <h3 class="text-white mb-30">Contact Information</h3>
                            <!-- <p class="text-white">Global IT Partner provides enterprise-grade managed IT services, cloud solutions, network infrastructure, and cybersecurity. Our certified experts deliver 24/7 support, proactive monitoring, and strategic IT consulting.</p> -->
                            <ul class="mt-40 mb-40">
                                <li class="mb-4">
                                    <i>
                                        <svg width="30" height="30" viewBox="0 0 30 30" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M15 2.5C10.1625 2.5 6.25 6.4125 6.25 11.25C6.25 17.8125 15 27.5 15 27.5C15 27.5 23.75 17.8125 23.75 11.25C23.75 6.4125 19.8375 2.5 15 2.5ZM15 14.375C13.275 14.375 11.875 12.975 11.875 11.25C11.875 9.525 13.275 8.125 15 8.125C16.725 8.125 18.125 9.525 18.125 11.25C18.125 12.975 16.725 14.375 15 14.375Z"
                                                fill="#3C72FC" />
                                        </svg>
                                    </i>
                                    <div>
                                        <span class="text-white">Singapore Office</span>
                                        <h3 class="mt-1"><a class="text-white" href="#0">170 Upper Bukit Timah Rd,
                                                #02-10, Singapore 588179</a>
                                        </h3>
                                        <span style="color: white;">Phone: +(65) 8224 2660</span>
                                    </div>
                                </li>
                                <li>
                                    <i>
                                        <svg width="30" height="30" viewBox="0 0 30 30" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M15 2.5C10.1625 2.5 6.25 6.4125 6.25 11.25C6.25 17.8125 15 27.5 15 27.5C15 27.5 23.75 17.8125 23.75 11.25C23.75 6.4125 19.8375 2.5 15 2.5ZM15 14.375C13.275 14.375 11.875 12.975 11.875 11.25C11.875 9.525 13.275 8.125 15 8.125C16.725 8.125 18.125 9.525 18.125 11.25C18.125 12.975 16.725 14.375 15 14.375Z"
                                                fill="#3C72FC" />
                                        </svg>
                                    </i>
                                    <div>
                                        <span class="text-white">Thailand Office</span>
                                        <h3 class="mt-1"><a class="text-white" href="#0">1112/111 Sukhumvit Road,
                                                Phra Khanong, Khlong Toei, Bangkok 10110</a>
                                        </h3>
                                        <span style="color: white;">Phone: +(66) 2 381 9075</span>
                                    </div>
                                </li>
                                <li class="mb-4">
                                    <i>
                                        <svg width="30" height="30" viewBox="0 0 30 30" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M15 2.5C10.1625 2.5 6.25 6.4125 6.25 11.25C6.25 17.8125 15 27.5 15 27.5C15 27.5 23.75 17.8125 23.75 11.25C23.75 6.4125 19.8375 2.5 15 2.5ZM15 14.375C13.275 14.375 11.875 12.975 11.875 11.25C11.875 9.525 13.275 8.125 15 8.125C16.725 8.125 18.125 9.525 18.125 11.25C18.125 12.975 16.725 14.375 15 14.375Z"
                                                fill="#3C72FC" />
                                        </svg>
                                    </i>
                                    <div>
                                        <span class="text-white">USA Office</span>
                                        <h3 class="mt-1"><a class="text-white" href="#0">1875 Mission ST STE 103 #371
                                                San Francisco, CA 94103</a>
                                        </h3>
                                        <span style="color: white;">Phone: +****************</span>
                                    </div>
                                </li>
                            </ul>

                        </div>
                    </div>
                    <div class="col-lg-7">
                        <div class="contact__right-item">
                            <div class="section-header mb-20">
                                <h5 class="wow fadeInUp pb-2" data-wow-delay="00ms" data-wow-duration="1500ms">
                                    <img class="me-1" src="assets/images/icon/section-title.png" alt="icon">
                                    GET IN TOUCH
                                </h5>
                                <h2 class="wow fadeInUp" data-wow-delay="200ms" data-wow-duration="1500ms">Ready to Get
                                    Started?</h2>
                                <p class="wow fadeInUp mt-3 mb-4" data-wow-delay="400ms" data-wow-duration="1500ms">
                                    Contact us today to learn how Global IT Partner can help your business with
                                    enterprise-grade managed IT services, cloud solutions, network infrastructure, and
                                    cybersecurity.
                                </p>
                            </div>
                            <div class="contact__form">
                                <form id="contact-form" action="mailer.php" method="post">
                                    <div class="row">
                                        <div class="col-6">
                                            <input name="name" class="bg-transparent bor" type="text"
                                                placeholder="Your Name*" required>
                                        </div>
                                        <div class="col-6">
                                            <input class="bg-transparent bor" name="email" type="email"
                                                placeholder="Your Email*" required>
                                        </div>
                                        <div class="col-12">
                                            <input class="bg-transparent bor" name="subject" type="text"
                                                placeholder="Your Subject*" required>
                                        </div>
                                        <!-- Honeypot field - should be hidden with CSS -->
                                        <div class="col-12" style="display:none;">
                                            <input class="bg-transparent bor" name="website" type="text"
                                                placeholder="Leave this field empty" autocomplete="off">
                                        </div>
                                    </div>
                                    <div class="text-area">
                                        <textarea class="bg-transparent bor" name="message" placeholder="Write Message"
                                            required></textarea>
                                    </div>
                                    <div class="btn-two">
                                        <span class="btn-circle">
                                        </span>
                                        <button class="btn-one" type="submit">Send Message <i
                                                class="fa-regular fa-arrow-right-long"></i></button>
                                    </div>
                                </form>
                                <div id="form-messages"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- Contact area end here -->

        <!-- Contact map area start here -->
        <div class="contact__map">
            <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3988.7258244847187!2d103.7726945!3d1.3378099!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31da1a602ff17c15%3A0x9c34b1753a2a3a85!2s170%20Upper%20Bukit%20Timah%20Rd%2C%20Singapore%20588179!5e0!3m2!1sen!2ssg!4v1718451234567!5m2!1sen!2ssg"
                width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy"
                referrerpolicy="no-referrer-when-downgrade"></iframe>
        </div>
        <!-- Contact map area end here -->
    </main>

    <!-- Footer area start here -->
    <?php include './partials/footer.php' ?>
    <!-- Footer area end here -->

    <!-- Back to top area start here -->
    <?php include './partials/scroll-up.php' ?>
    <!-- Back to top area end here -->

    <!-- all js files -->
    <?php include './partials/script.php' ?>

    <!-- Contact Form AJAX Script -->
    <script>
    // Wait for jQuery to be available
    function initContactForm() {
        if (typeof jQuery === 'undefined') {
            setTimeout(initContactForm, 100);
            return;
        }

        jQuery(document).ready(function($) {
            console.log('Contact form script loaded');

            var form = $('#contact-form');
            var formMessages = $('#form-messages');

            console.log('Form found:', form.length > 0);
            console.log('Messages div found:', formMessages.length > 0);

            // Prevent any other form submission handlers
            form.off('submit');

            form.on('submit', function(e) {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();

                console.log('Form submitted via AJAX');

                // Validate form before submission
                var name = $('input[name="name"]').val().trim();
                var email = $('input[name="email"]').val().trim();
                var subject = $('input[name="subject"]').val().trim();
                var message = $('textarea[name="message"]').val().trim();

                if (!name || !email || !subject || !message) {
                    formMessages.html(
                        '<div class="alert alert-danger" style="background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 20px;"><i class="fa fa-exclamation-triangle"></i> Please fill in all required fields.</div>'
                    );
                    return false;
                }

                var formData = $(this).serialize();
                console.log('Form data:', formData);

                $.ajax({
                    type: 'POST',
                    url: 'mailer.php',
                    data: formData,
                    dataType: 'json',
                    beforeSend: function() {
                        // Show loading state
                        formMessages.html(
                            '<div class="alert alert-info" style="background-color: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin-top: 20px;"><i class="fa fa-spinner fa-spin"></i> Sending your message...</div>'
                        );
                    },
                    success: function(response) {
                        console.log('Success response:', response);
                        if (response.status === 'success') {
                            formMessages.removeClass('error').addClass('success')
                                .html(
                                    '<div class="alert alert-success" style="background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-top: 20px;"><i class="fa fa-check-circle"></i> ' +
                                    response.message + '</div>');
                            $('#contact-form input, #contact-form textarea').val('');
                        } else {
                            formMessages.removeClass('success').addClass('error')
                                .html(
                                    '<div class="alert alert-danger" style="background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 20px;"><i class="fa fa-exclamation-triangle"></i> ' +
                                    response.message + '</div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('Error response:', xhr.responseText);
                        let errorMessage =
                            'Oops! An error occurred and your message could not be sent.';

                        try {
                            let response = JSON.parse(xhr.responseText);
                            if (response.message) {
                                errorMessage = response.message;
                            }
                        } catch (e) {
                            // If not JSON, use the raw response or default message
                            if (xhr.responseText) {
                                errorMessage = xhr.responseText;
                            }
                        }

                        formMessages.removeClass('success').addClass('error')
                            .html(
                                '<div class="alert alert-danger" style="background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 20px;"><i class="fa fa-exclamation-triangle"></i> ' +
                                errorMessage + '</div>');
                    }
                });

                return false;
            });
        });
    }

    // Initialize the contact form
    initContactForm();
    </script>
</body>

</html>