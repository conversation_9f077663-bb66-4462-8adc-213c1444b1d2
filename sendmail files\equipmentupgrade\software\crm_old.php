<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);


							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';

$picCnt = 0;

?>
<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, maximum-scale=1">
<title>CRM Software, Online Customer Relationship Management</title>
<meta name="description" content="MySaas CRM is an easy-to-use, fast-to-deploy CRM solution. MySaas CRM Solutions deliver the robust functionality you require to meet.">
<meta name="keywords" content="crm software,web based crm, hosted crm,customer relationship management, SaaS CRM, sales force automation, sales lead tracking">
<?php
echo getPgCss();
?>
<link href="<?php echo CSS_URL; ?>detailpage.css" rel="stylesheet" type="text/css">
<link href="<?php echo CSS_URL; ?>mainpage.css" rel="stylesheet" type="text/css">
<link href="<?php echo APPS_URL?>fancybox/jquery.fancybox.css?v=2.1.5" type="text/css" rel="stylesheet">
<script language="JavaScript" type="text/javascript" src="<?php echo APPS_URL?>fancybox/jquery.fancybox.pack.js?v=2.1.5"></script>
</head>
<body>

<!--Header_section-->
 	<?php include(INCLUDE_PATH . 'head.php');?>
<!--Header_section--> 

<!--slider_section-->
   <?php include(SLIDER_PATH . 'slider-software.php');?>
<!--slider_section--> 



<section id="tag-crm">
  <div class="container">
    
	<div class="row">
      <div class="col-lg-9 col-md-9 col-sm-9 col-xs-12 pull-right">
      <h1>MySaas CRM (Customer Relationship Management)</h1>
      <p>MySaas Operation Management System is a web-based application that allow businesses to manage customers, sales, inventories and purchases online. It integrates business functions and data into a centralized system and it facilitates the flow of information across departments, organizational units and geographical locations.</p>
      
      <br />
      <p>MySaas OPS provides business owner and the management team with accurate, reliable and consistent view of information, thereby enabling business owner to make better decision. It is a starting point for reliable, secure and scalable enterprise solutions. With MySaas OPS, you can get started right away without the huge deployment and maintenance costs of traditional enterprise automation systems. </p>
      <br />
    <div id = "test">
      <h3>Features</h3><br/>
      <ul>
       <li>Automate the sales process from lead to sales close</li>
       <li>Manage quotes, contacts, leads, opportunities and sales accounts anywhere, anytime</li>
       <li>Keep sales, marketing and support teams on the same page</li>
       <li>Improve customer experience and customer support</li>
  </ul>
  </div>
   <br/>
  <h3>Screenshots &nbsp;&nbsp; <span style="font-size:11px; color:#666; font-style:italic"> (click on the image for enlarge view)</span></h3> <br/>
<table width="100%">
  <tr valign="top">
  <td valign="top"><b>Sales Contacts</b><br />
  <a id="picture<?php echo $picCnt?>" href="<?php echo IMG_URL?>software/crm-contacts.jpg"><img src="<?php echo IMG_URL?>software/crm-contacts-s.jpg" onclick="activateFancyBox(<?php echo $picCnt++?>)"></a></td>
  <td><b>Sales Leads</b><br />
  <a id="picture<?php echo $picCnt?>" href="<?php echo IMG_URL?>software/crm-leads.jpg"><img src="<?php echo IMG_URL?>software/crm-leads-s.jpg" onclick="activateFancyBox(<?php echo $picCnt++?>)"></a></td>
  <td><b>Sales Opportunities</b><br />
  <a id="picture<?php echo $picCnt?>" href="<?php echo IMG_URL?>software/crm-opportunities.jpg"><img src="<?php echo IMG_URL?>software/crm-opportunities-s.jpg" onclick="activateFancyBox(<?php echo $picCnt++?>)"></a></td></tr></table>
  
</div>
      
      
      
      
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 pull-left">
                    <div class="delay-01s animated fadeInDown wow animated">
                    <?
                    include("left-menu-software.php");
                    ?>
                    </div>
                </div>
      </div>
  </div> 
</section>


<!--Footer-->
   <?php include(INCLUDE_PATH . 'foot.php');?>

<script type="text/javascript" src="<?php echo JSLIB_URL;?>custom.js"></script>    
</body>
</html>