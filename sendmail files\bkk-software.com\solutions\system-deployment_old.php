<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);


							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';



?>
<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, maximum-scale=1">
<title>System Deployment Services</title>
<meta name="description" content="<?php echo $SITE_NAME;?> helps manage your IT functions including infrastructure setup, IT procurement and IT maintenance services ">
<meta name="keywords" content="system deployment, infrastructure setup, system implementation, office IT setup,system setup, business IT setup,IT implementation">
<?php
echo getPgCss();
?>
<link href="<?php echo CSS_URL; ?>mainpage.css" rel="stylesheet" type="text/css">
<link href="<?php echo CSS_URL; ?>detailpage.css" rel="stylesheet" type="text/css">
</head>
<body>

<!--Header_section-->
 	<?php include(INCLUDE_PATH . 'head.php');?>
<!--Header_section--> 

<!--slider_section-->
   <?php include(SLIDER_PATH . 'slider-solutions.php');?>
<!--slider_section--> 



<section id="tag-stdm">
  <div class="container">
  
	<div class="row">
    <div class="col-lg-9 col-md-9 col-sm-9 col-xs-12 pull-right">
      <h1>System Deployment</h1>
      <p>
 <?php echo $SITE_NAME;?>
  can help you manage all your IT needs including the design, setup and implementation of your company's IT infrastructure. From requirements analysis, architecture design, setup, integrations to training and delivery, <?php echo $SITE_NAME?> provides the services and expertise you can trust and rely on.</p>
      
      <br/>
      <br/>
      
<p>Our services include:</p><br/>
<div id="test">
<ul>
  <li>IT infrastructure design and setup</li>
  <li>Hardware and Software procurement</li>
  <li>Wiring, network design and configuration</li>
  <li>Server, desktop installation and upgrades</li>
  <li>Firewall, Router, switches, AP and other wireless deployments</li>
  <li>PC, Printers, Fax setup</li>
  <li>VLAN implementation, Virtual Private Network (VPN)</li>
  <li>IP Phone, Voice over IP (VoIP) deployment</li>
  <li>Email migration and setup</li>
  </ul><br/>
  </div>
<p>
  Additionally, <?php echo $SITE_NAME;?>
  provides post-implementation <a href="../services/it-support.htm"> maintenance &amp; support</a> to ensure your IT infrastructure runs smoothly and problem fee. For  information about our products, please contact our technical sales representative at <strong>02 381-9075</strong>.</p>
      </div>
      
      
      
      
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 pull-left">
                    <div class="delay-01s animated fadeInDown wow animated">
                    <?
                    include("left-menu-solution.php");
                    ?>
                    </div>
                </div>
      </div>
  </div> 
</section>


<!--Footer-->
   <?php include(INCLUDE_PATH . 'foot.php');?>

<script type="text/javascript" src="<?php echo JSLIB_URL;?>custom.js"></script>    
</body>
</html>