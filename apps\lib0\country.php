<?php
//straight through processing
$STP_COUNTRY = array(
	'AU',
	'CA',
	'US'
);


//Country list that uses state abbrevation (cstate Code) when displaying the address
$COUNTRY_USE_CSTATES_CODE = array(
	'US'
);


$COUNTRY_NO_CITY = array(	//no show city when formatAddress6() is called
	'SG'
);


$COUNTRY_NO_CSTATES = array(	//disabled the cState field
	'SG'
);

//allow ship to country
$ALLOWED_COUNTRY = array(
	"AU" => "Australia",
	"AT" => "Austria",
	"BE" => "Belgium",
	"BT" => "Bhutan",
	"BN" => "Brunei Darussalam",
	"KH" => "Cambodia",
	"CA" => "Canada",
	"CL" => "Chile",
	"CN" => "China",
	"CZ" => "Czech Republic",
	"DK" => "Denmark",
	"FI" => "Finland",
	"FR" => "France",
	"DE" => "Germany",
	"GR" => "Greece",
	"GL" => "Greenland",
	"HK" => "Hong Kong",
	"IS" => "Iceland",
	"IN" => "India",
	"ID" => "Indonesia",
	"IE" => "Ireland",
	"IT" => "Italy",
	"JP" => "Japan",
	"KR" => "Korea, South",
	"LA" => "Lao People's Dem. Republic",
	"MO" => "Macao",
	"MY" => "Malaysia",
	"MV" => "Maldives",
	"MA" => "Morocco",
	"MM" => "Myanmar",
	"NP" => "Nepal",
	"NL" => "Netherlands",
	"NZ" => "New Zealand",
	"NO" => "Norway",
	"PE" => "Peru",
	"PH" => "Philippines",
	"SG" => "Singapore",
	"ES" => "Spain",
	"LK" => "Sri Lanka",
	"SE" => "Sweden",
	"CH" => "Switzerland",
	"TW" => "Taiwan",
	"TH" => "Thailand",
	"TR" => "Turkey",
	"AE" => "United Arab Emirates",
	"GB" => "United Kingdom",
	"US" => "United States",
	"VE" => "Venezuela",
	"VN" => "Vietnam",
	"US" => "United States"
);




$BLOCK_COUNTRY = array(
	"AF" => "Afghanistan",
	"AL" => "Albania",
	"DZ" => "Algeria",
	"AS" => "American Samoa",
	"AD" => "Andorra",
	"AO" => "Angola",
	"AI" => "Anguilla",
	"AQ" => "Antarctica",
	"AG" => "Antigua and Barbuda",
	"AR" => "Argentina",
	"AM" => "Armenia",
	"AW" => "Aruba",
	"AZ" => "Azerbaijan",
	"BS" => "Bahamas",
	"BH" => "Bahrain",
	"BD" => "Bangladesh",
	"BB" => "Barbados",
	"BY" => "Belarus",
	"BZ" => "Belize",
	"BJ" => "Benin",
	"BM" => "Bermuda",
	"BO" => "Bolivia",
	"BA" => "Bosnia and Herzegovina",
	"BW" => "Botswana",
	"BV" => "Bouvet Island",
	"BR" => "Brazil",
	"IO" => "British Indian Ocean Territory",
	"BG" => "Bulgaria",
	"BF" => "Burkina Faso",
	"BI" => "Burundi",
	"CM" => "Cameroon",
	"CV" => "Cape Verde",
	"KY" => "Cayman Islands",
	"CF" => "Central African Republic",
	"TD" => "Chad",
	"CX" => "Christmas Island",
	"CC" => "Cocos (Keeling) Islands",
	"CO" => "Colombia",
	"KM" => "Comoros",
	"CG" => "Congo",
	"CK" => "Cook Islands",
	"CR" => "Costa Rica",
	"CI" => "Cote D'Ivoire",
	"HR" => "Croatia",
	"CU" => "Cuba",
	"CY" => "Cyprus",
	"DJ" => "Djibouti",
	"DM" => "Dominica",
	"DO" => "Dominican Republic",
	"TL" => "East Timor",
	"EC" => "Ecuador",
	"EG" => "Egypt",
	"SV" => "El Salvador",
	"GQ" => "Equatorial Guinea",
	"ER" => "Eritrea",
	"EE" => "Estonia",
	"ET" => "Ethiopia",
	"FK" => "Falkland Islands (Malvinas)",
	"FO" => "Faroe Islands",
	"FJ" => "Fiji",
	"FX" => "France, Metropolitan",
	"GF" => "French Guiana",
	"PF" => "French Polynesia",
	"TF" => "French Southern Territories",
	"GA" => "Gabon",
	"GM" => "Gambia",
	"GE" => "Georgia",
	"GH" => "Ghana",
	"GI" => "Gibraltar",
	"GD" => "Grenada",
	"GP" => "Guadeloupe",
	"GU" => "Guam",
	"GT" => "Guatemala",
	"GN" => "Guinea",
	"GW" => "Guinea-Bissau",
	"GY" => "Guyana",
	"HT" => "Haiti",
	"HM" => "Heard & McDonald Islands",
	"VA" => "Holy See (Vatican City State)",
	"HN" => "Honduras",
	"IR" => "Iran, Islamic Republic of",
	"IQ" => "Iraq",
	"HU" => "Hungary",
	"IL" => "Israel",
	"JM" => "Jamaica",
	"JO" => "Jordan",
	"KZ" => "Kazakhstan",
	"KE" => "Kenya",
	"KI" => "Kiribati",
	"KP" => "Korea, North",
	"KW" => "Kuwait",
	"KG" => "Kyrgyzstan",
	"LV" => "Latvia",
	"LB" => "Lebanon",
	"LS" => "Lesotho",
	"LR" => "Liberia",
	"LY" => "Libyan Arab Jamahiriya",
	"LI" => "Liechtenstein",
	"LT" => "Lithuania",
	"LU" => "Luxembourg",
	"MK" => "Macedonia",
	"MG" => "Madagascar",
	"MW" => "Malawi",
	"ML" => "Mali",
	"MT" => "Malta",
	"MH" => "Marshall Islands",
	"MQ" => "Martinique",
	"MR" => "Mauritania",
	"MU" => "Mauritius",
	"YT" => "Mayotte",
	"MX" => "Mexico",
	"FM" => "Micronesia, Fed. States of",
	"MD" => "Moldova, Republic of",
	"MC" => "Monaco",
	"MN" => "Mongolia",
	"MS" => "Montserrat",
	"MZ" => "Mozambique",
	"NA" => "Namibia",
	"NR" => "Nauru",
	"AN" => "Netherlands Antilles",
	"NC" => "New Caledonia",
	"NI" => "Nicaragua",
	"NE" => "Niger",
	"NG" => "Nigeria",
	"NU" => "Niue",
	"NF" => "Norfolk Island",
	"MP" => "Northern Mariana Islands",
	"OM" => "Oman",
	"PK" => "Pakistan",
	"PW" => "Palau",
	"PS" => "Palestinian Territory, Occ.",
	"PA" => "Panama",
	"PG" => "Papua New Guinea",
	"PY" => "Paraguay",
	"PN" => "Pitcairn",
	"PL" => "Poland",
	"PT" => "Portugal",
	"PR" => "Puerto Rico",
	"QA" => "Qatar",
	"RE" => "Reunion",
	"RO" => "Romania",
	"RU" => "Russian Federation",
	"RW" => "Rwanda",
	"GS" => "S. Georgia/S. Sandwich Islands",
	"SH" => "Saint Helena",
	"KN" => "Saint Kitts and Nevis",
	"LC" => "Saint Lucia",
	"PM" => "Saint Pierre and Miquelon",
	"VC" => "Saint Vincent/the Grenadines",
	"WS" => "Samoa",
	"SM" => "San Marino",
	"ST" => "Sao Tome and Principe",
	"SA" => "Saudi Arabia",
	"SN" => "Senegal",
	"SC" => "Seychelles",
	"SL" => "Sierra Leone",
	"SK" => "Slovakia",
	"SI" => "Slovenia",
	"SB" => "Solomon Islands",
	"SO" => "Somalia",
	"ZA" => "South Africa",
	"SD" => "Sudan",
	"SR" => "Suriname",
	"SJ" => "Svalbard and Jan Mayen",
	"SZ" => "Swaziland",
	"SY" => "Syrian Arab Republic",
	"TJ" => "Tajikistan",
	"TZ" => "Tanzania, United Republic of",
	"TG" => "Togo",
	"TK" => "Tokelau",
	"TO" => "Tonga",
	"TT" => "Trinidad and Tobago",
	"TN" => "Tunisia",
	"TM" => "Turkmenistan",
	"TC" => "Turks and Caicos Islands",
	"TV" => "Tuvalu",
	"UM" => "US Minor Outlying Islands",
	"UG" => "Uganda",
	"UA" => "Ukraine",
	"UY" => "Uruguay",
	"UZ" => "Uzbekistan",
	"VU" => "Vanuatu",
	"VG" => "Virgin Islands, British",
	"VI" => "Virgin Islands, U.S.",
	"WF" => "Wallis and Futuna",
	"EH" => "Western Sahara",
	"YE" => "Yemen",
	"YU" => "Yugoslavia",
	"ZR" => "Zaire",
	"ZM" => "Zambia",
	"ZW" => "Zimbabwe"
);

//-----------------
$FULL_COUNTRY = array(
	"AF" => "Afghanistan",
	"AL" => "Albania",
	"DZ" => "Algeria",
	"AS" => "American Samoa",
	"AD" => "Andorra",
	"AO" => "Angola",
	"AI" => "Anguilla",
	"AQ" => "Antarctica",
	"AG" => "Antigua and Barbuda",
	"AR" => "Argentina",
	"AM" => "Armenia",
	"AW" => "Aruba",
	"AU" => "Australia",
	"AT" => "Austria",
	"AZ" => "Azerbaijan",
	"BS" => "Bahamas",
	"BH" => "Bahrain",
	"BD" => "Bangladesh",
	"BB" => "Barbados",
	"BY" => "Belarus",
	"BE" => "Belgium",
	"BZ" => "Belize",
	"BJ" => "Benin",
	"BM" => "Bermuda",
	"BT" => "Bhutan",
	"BO" => "Bolivia",
	"BA" => "Bosnia and Herzegovina",
	"BW" => "Botswana",
	"BV" => "Bouvet Island",
	"BR" => "Brazil",
	"IO" => "British Indian Ocean Territory",
	"BN" => "Brunei Darussalam",
	"BG" => "Bulgaria",
	"BF" => "Burkina Faso",
	"BI" => "Burundi",
	"KH" => "Cambodia",
	"CM" => "Cameroon",
	"CA" => "Canada",
	"CV" => "Cape Verde",
	"KY" => "Cayman Islands",
	"CF" => "Central African Republic",
	"TD" => "Chad",
	"CL" => "Chile",
	"CN" => "China",
	"CX" => "Christmas Island",
	"CC" => "Cocos (Keeling) Islands",
	"CO" => "Colombia",
	"KM" => "Comoros",
	"CG" => "Congo",
	"CK" => "Cook Islands",
	"CR" => "Costa Rica",
	"CI" => "Cote D'Ivoire",
	"HR" => "Croatia",
	"CU" => "Cuba",
	"CY" => "Cyprus",
	"CZ" => "Czech Republic",
	"DK" => "Denmark",
	"DJ" => "Djibouti",
	"DM" => "Dominica",
	"DO" => "Dominican Republic",
	"TL" => "East Timor",
	"EC" => "Ecuador",
	"EG" => "Egypt",
	"SV" => "El Salvador",
	"GQ" => "Equatorial Guinea",
	"ER" => "Eritrea",
	"EE" => "Estonia",
	"ET" => "Ethiopia",
	"FK" => "Falkland Islands (Malvinas)",
	"FO" => "Faroe Islands",
	"FJ" => "Fiji",
	"FI" => "Finland",
	"FR" => "France",
	"FX" => "France, Metropolitan",
	"GF" => "French Guiana",
	"PF" => "French Polynesia",
	"TF" => "French Southern Territories",
	"GA" => "Gabon",
	"GM" => "Gambia",
	"GE" => "Georgia",
	"DE" => "Germany",
	"GH" => "Ghana",
	"GI" => "Gibraltar",
	"GR" => "Greece",
	"GL" => "Greenland",
	"GD" => "Grenada",
	"GP" => "Guadeloupe",
	"GU" => "Guam",
	"GT" => "Guatemala",
	"GN" => "Guinea",
	"GW" => "Guinea-Bissau",
	"GY" => "Guyana",
	"HT" => "Haiti",
	"HM" => "Heard & McDonald Islands",
	"VA" => "Holy See (Vatican City State)",
	"HN" => "Honduras",
	"HK" => "Hong Kong",
	"HU" => "Hungary",
	"IS" => "Iceland",
	"IN" => "India",
	"ID" => "Indonesia",
	"IR" => "Iran, Islamic Republic of",
	"IQ" => "Iraq",
	"IE" => "Ireland",
	"IL" => "Israel",
	"IT" => "Italy",
	"JM" => "Jamaica",
	"JP" => "Japan",
	"JO" => "Jordan",
	"KZ" => "Kazakhstan",
	"KE" => "Kenya",
	"KI" => "Kiribati",
	"KP" => "Korea, North",
	"KR" => "Korea, South",
	"KW" => "Kuwait",
	"KG" => "Kyrgyzstan",
	"LA" => "Lao People's Dem. Republic",
	"LV" => "Latvia",
	"LB" => "Lebanon",
	"LS" => "Lesotho",
	"LR" => "Liberia",
	"LY" => "Libyan Arab Jamahiriya",
	"LI" => "Liechtenstein",
	"LT" => "Lithuania",
	"LU" => "Luxembourg",
	"MO" => "Macao",
	"MK" => "Macedonia",
	"MG" => "Madagascar",
	"MW" => "Malawi",
	"MY" => "Malaysia",
	"MV" => "Maldives",
	"ML" => "Mali",
	"MT" => "Malta",
	"MH" => "Marshall Islands",
	"MQ" => "Martinique",
	"MR" => "Mauritania",
	"MU" => "Mauritius",
	"YT" => "Mayotte",
	"MX" => "Mexico",
	"FM" => "Micronesia, Fed. States of",
	"MD" => "Moldova, Republic of",
	"MC" => "Monaco",
	"MN" => "Mongolia",
	"MS" => "Montserrat",
	"MA" => "Morocco",
	"MZ" => "Mozambique",
	"MM" => "Myanmar",
	"NA" => "Namibia",
	"NR" => "Nauru",
	"NP" => "Nepal",
	"NL" => "Netherlands",
	"AN" => "Netherlands Antilles",
	"NC" => "New Caledonia",
	"NZ" => "New Zealand",
	"NI" => "Nicaragua",
	"NE" => "Niger",
	"NG" => "Nigeria",
	"NU" => "Niue",
	"NF" => "Norfolk Island",
	"MP" => "Northern Mariana Islands",
	"NO" => "Norway",
	"OM" => "Oman",
	"PK" => "Pakistan",
	"PW" => "Palau",
	"PS" => "Palestinian Territory, Occ.",
	"PA" => "Panama",
	"PG" => "Papua New Guinea",
	"PY" => "Paraguay",
	"PE" => "Peru",
	"PH" => "Philippines",
	"PN" => "Pitcairn",
	"PL" => "Poland",
	"PT" => "Portugal",
	"PR" => "Puerto Rico",
	"QA" => "Qatar",
	"RE" => "Reunion",
	"RO" => "Romania",
	"RU" => "Russian Federation",
	"RW" => "Rwanda",
	"GS" => "S. Georgia/S. Sandwich Islands",
	"SH" => "Saint Helena",
	"KN" => "Saint Kitts and Nevis",
	"LC" => "Saint Lucia",
	"PM" => "Saint Pierre and Miquelon",
	"VC" => "Saint Vincent/the Grenadines",
	"WS" => "Samoa",
	"SM" => "San Marino",
	"ST" => "Sao Tome and Principe",
	"SA" => "Saudi Arabia",
	"SN" => "Senegal",
	"SC" => "Seychelles",
	"SL" => "Sierra Leone",
	"SG" => "Singapore",
	"SK" => "Slovakia",
	"SI" => "Slovenia",
	"SB" => "Solomon Islands",
	"SO" => "Somalia",
	"ZA" => "South Africa",
	"ES" => "Spain",
	"LK" => "Sri Lanka",
	"SD" => "Sudan",
	"SR" => "Suriname",
	"SJ" => "Svalbard and Jan Mayen",
	"SZ" => "Swaziland",
	"SE" => "Sweden",
	"CH" => "Switzerland",
	"SY" => "Syrian Arab Republic",
	"TW" => "Taiwan",
	"TJ" => "Tajikistan",
	"TZ" => "Tanzania, United Republic of",
	"TH" => "Thailand",
	"TG" => "Togo",
	"TK" => "Tokelau",
	"TO" => "Tonga",
	"TT" => "Trinidad and Tobago",
	"TN" => "Tunisia",
	"TR" => "Turkey",
	"TM" => "Turkmenistan",
	"TC" => "Turks and Caicos Islands",
	"TV" => "Tuvalu",
	"UM" => "US Minor Outlying Islands",
	"UG" => "Uganda",
	"UA" => "Ukraine",
	"AE" => "United Arab Emirates",
	"GB" => "United Kingdom",
	"US" => "United States",
	"UY" => "Uruguay",
	"UZ" => "Uzbekistan",
	"VU" => "Vanuatu",
	"VE" => "Venezuela",
	"VN" => "Vietnam",
	"VG" => "Virgin Islands, British",
	"VI" => "Virgin Islands, U.S.",
	"WF" => "Wallis and Futuna",
	"EH" => "Western Sahara",
	"YE" => "Yemen",
	"YU" => "Yugoslavia",
	"ZR" => "Zaire",
	"ZM" => "Zambia",
	"ZW" => "Zimbabwe"
);


//-----------------
function getCountry($remoteAddr=''){
global $myDb;

if($remoteAddr==''){$remoteAddr = $_SERVER['REMOTE_ADDR'];}
$sql = "SELECT c.country FROM ip2nationCountries c, ip2nation i 
		WHERE i.ip < INET_ATON('$remoteAddr') AND c.code = i.country ORDER BY i.ip DESC LIMIT 0,1";
list($countryName) = $myDb->fetchArray($myDb->query($sql));	
return $countryName;
}
//-----------------
function getCcode(){
//include_once LIB0_PATH . 'country.php';
global $FULL_COUNTRY;
	$ccode = -1;
	$country =  getCountry();
	foreach($FULL_COUNTRY as $key => $value){
		if(eregi($country, $value)){ $ccode = $key;}
	}
return $ccode;
}
//--------------------------------------
function validCcode($ccode){
//requires EXACT match to the key in the $COUNTRY array (don't perform strtoupper here)
//global $FULL_COUNTRY;

$langList = array('EN');	//english

	if(in_array($ccode, $langList)) {return true;} else {return false;}
	//if(array_key_exists($ccode, $FULL_COUNTRY)) {return true;} else {return false;}
}


//-----------------
function formatAddress6($addrArr, $noShowArr, $sep){
global $FULL_COUNTRY, $COUNTRY_NO_CITY, $COUNTRY_USE_CSTATES_CODE, $COUNTRY_NO_CSTATES;
$addr = array();
$xtra = array();

	$addr[] = $addrArr['addr1'];
	$addr[] = $addrArr['addr2'];
	$ccode	= $addrArr['ccode'];
	
	//city
	if(!in_array($ccode, $COUNTRY_NO_CITY) && !in_array('city', $noShowArr)){$xtra[] = $addrArr['city'];}
	
	//cstate	
	if(in_array('ccode', $noShowArr)){$ccodeStr = '';} else {$ccodeStr = $FULL_COUNTRY[$ccode];}
	if(in_array($ccode, $COUNTRY_NO_CSTATES)){
		//country without state info
		if(in_array('zip', $noShowArr)){
			$xtra[] = $ccodeStr;							
		} else {
			$xtra[] = $ccodeStr . ' ' . $addrArr['zip'];		//Singapore 588177
		}
	} else {
		$tmp = '';
		if(!in_array('cstate', $noShowArr)){	//show cstate
			/*
			$cstate = $addrArr['cstate'];
			if(in_array($ccode, $COUNTRY_USE_CSTATES_CODE)){
				$stateName = 'STATES_' . $ccode;
				global $$stateName;
				$tmpStates = & $$stateName;
				$xtra2 = $tmpStates[$cstate];
			} else {
				$xtra2 = $addrArr['cstate'];
			}
		*/
			$tmp = $addrArr['cstate'];
		}
		if(!in_array('zip',		$noShowArr)){$tmp  .= ' '	. $addrArr['zip'];}
		$xtra[] = $tmp;
		if(!in_array('ccode',	$noShowArr)){$xtra[] = $FULL_COUNTRY[$ccode];}
	}
	$excludes = array('', ' ');
	$xtra = array_diff($xtra, $excludes); //remove empty string ''
	$addr[] = implode(', ', $xtra);

	//tel1, tel2, fax
	$xtra3 = array();
	if($addrArr['tel']!='' && !in_array('tel',	$noShowArr)){$xtra3[] = 'Tel: '.$addrArr['tel'];}
	if($addrArr['alt']!='' && !in_array('alt',	$noShowArr)){$xtra3[] = 'Alt: '.$addrArr['alt'];}
	if($addrArr['fax']!='' && !in_array('fax',	$noShowArr)){$xtra3[] = 'Fax: '.$addrArr['fax'];}
	$addr[] = implode(', ', $xtra3);

	//email
	if($addrArr['email']!='' && !in_array('email', $noShowArr)){$addr[] = $addrArr['email'];}

	//web
	if($addrArr['web']!='' && !in_array('web', $noShowArr)){$addr[] = $addrArr['web'];}

	$addr = array_diff($addr, $excludes); //remove empty string ''

$addrStr = implode("$sep", $addr);
return $addrStr;
}


?>