/**
 * Special fix for Pace.js blue progress bar on iOS
 * This script runs before Pace.js loads to prevent the blue line from appearing
 */
(function() {
    // Detect iOS devices
    if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) {
        // Create a style element to immediately hide the pace progress bar
        var style = document.createElement('style');
        style.type = 'text/css';
        style.innerHTML = `
            .pace, .pace-progress {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                width: 0 !important;
                max-width: 0 !important;
                position: absolute !important;
                top: -9999px !important;
                left: -9999px !important;
                z-index: -999 !important;
                pointer-events: none !important;
            }
        `;
        
        // Add the style to the head immediately
        document.head.appendChild(style);
        
        // Create a dummy Pace object to prevent errors
        window.Pace = {
            options: {
                ajax: false,
                document: false,
                eventLag: false,
                elements: false,
                startOnPageLoad: false,
                restartOnPushState: false,
                restartOnRequestAfter: false
            },
            restart: function(){},
            start: function(){},
            stop: function(){},
            track: function(){},
            ignore: function(){},
            on: function(){},
            off: function(){},
            destroy: function(){}
        };
        
        // Add a cleanup function to run after page load
        window.addEventListener('load', function() {
            // Remove any pace elements that might have been created
            var paceElements = document.querySelectorAll('.pace, .pace-progress');
            paceElements.forEach(function(el) {
                if (el && el.parentNode) {
                    el.parentNode.removeChild(el);
                }
            });
        });
    }
})();
