<?php
  function sendForm() {
  
    $strTo = "<EMAIL>";
    $strSubject = "=?UTF-8?B?".base64_encode("IT Request Installation for ".$_POST["company"]."")."?=";
    $strHeader = "MIME-Version: 1.0' . \r\n";
    $strHeader .= "Content-type: text/html; charset=utf-8\r\n";
    $strHeader .= "From: <EMAIL>";
    $appList = "";
    for( $i = 1; $i <= 15; $i++ ) {
      $app = 'app' . $i;
      if( isset($_POST[$app]) ){
        $appList .= $_POST[$app] . " / ";
      }
    }   
    $msg = ' <html><head>
      <style type="text/css">
      .myTable { background-color:#FFFFFF;border-collapse:collapse; }
      .myTable th { background-color:#585858;color:white; }
      .myTable td, .myTable th { padding:5px;border:1px solid #000000; }
      </style>
      <style type="text/css">
      .myStaff { background-color:#00000;border-collapse:collapse; }
      .myStaff th { background-color:#000000;color:white; }
      .myStaff td, .myStaff th { padding:5px;border:1px solid #000000; }
      .style3 {font-size: large; font-weight: bold; }
      </style>
      <style type="text/css" media="screen">
      body {
        font: .75em/1.5 "Lucida Grande", "Lucida Sans Unicode", helvetica, verdana, arial, sans-serif;
          margin: 0 auto;
          padding: 0 10px;
          width: 610px;
      }
      strong {
        background: #ffc;
      }
      </style></head><body>
      <th><p class="style3">New Installation for '.$_POST["company"].'</p></th>
      <table width="700" class="myTable">

      <tr><th colspan="2" align="left"><p>Customer Information</p></th></tr>
      <tr><td colspan="2">Company Name: ' . $_POST[company].  ' (Request by Khun ' . $_POST[requester]. ') 
      <br>Contact +66 '. $_POST[contact].' &nbsp;E-mail : '. $_POST[email].' </td> </tr>
      <tr><th colspan="2" align="left"><p>Installation Details </p></th></tr>
      <tr><td width="150">Operation System </td>
      <td width="550">(&nbsp; ) '. $_POST[os].' </td></tr> 
      <tr>
        <td>Microsoft Office</td>
        <td>(&nbsp; ) ' . $_POST[office].  ' </td>
      </tr>
      <tr>
        <td> Default Language</td>
        <td>(&nbsp; ) ' . $_POST[lang]. ' </td>
      </tr>
      <tr>
        <td> Harddisk Partition</td>
        <td>(&nbsp; ) ' . $_POST[part]. ' </td>
      </tr>
      <tr>
        <td> Username</td>
        <td>(&nbsp; ) ' . $_POST[username]. ' </td>
      </tr>
      <tr>
        <td> Password</td>
        <td>(&nbsp; ) ' . $_POST[password]. ' </td>
      </tr>
      <tr>
        <td valign="top">Application</td>
        <td>(&nbsp; ) ' . $appList. ' </td>
      </tr>
        <tr>
          <th colspan="2" align="left"><p>Additional Note</p></th>
        </tr>
        <tr>
          <td colspan="2"> ' . $_POST[notes].  ' </td>
        </tr></table>
        
      <th><p class="style3">New Installation Check for Internal</p></th>
      <table width="700" class="myStaff">

       <tr><td width="150">&nbsp;Operation System</td> <td width="550">(&nbsp; ) '. $_POST[os].' </td></tr>
        <tr><td>&nbsp;Microsoft Office</td><td> (&nbsp; ) '. $_POST[office].' </td></tr>
        <tr><td>&nbsp;Default Language</td><td> (&nbsp; ) '. $_POST[lang].' </td></tr>
        <tr><td valign="top">&nbsp;Application<br> <td> (&nbsp; )  ' . $appList. ' </td></tr>

      <tr><td>&nbsp;Remarked</td><td>&nbsp;&nbsp;&nbsp;</td></tr>
        
      </table>
      </body>
      </html>
  ';
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<title>TechSpace Co., Ltd.</title>

<style type="text/css">
<!--
body {
	background-image: url(wallpaper.jpg);
}
.style2 {
	font-family: Calibri, "Calibri Light";
	color: #666666;
}
.style9 {
	font-size: 24px;
	color: #666666;
}
.style11 {font-family: Calibri, "Calibri Light"; color: #999999; font-size: 16px; }
.style19 {font-size: 16px; color: #666666; }
.style22 {font-family: Calibri, "Calibri Light"}
.style23 {color: #666666}
.style24 {
	font-size: 18px;
	color: #666666;
}
.style25 {font-size: 18px}
.style30 {font-size: 14px}
.style31 {font-size: 14px; color: #666666; }
-->
</style>
</head>

<body>

<?php
  if(isset($_POST['submit'])) {
  sendForm();
?>
<p class="style2 style9">Already submitted! We will contact you when the installation is completed.</p>
<?php
  }
?>

<h1 align="left" class="style2 style9">TechSpace : New PC Installation Online Form</h1>
<p class="style22 style23">Please complete the below form. Once the form is submitted, it will take up to 6 hours to complete the installation.</p>

<form action="TechSpace_Newinstallation.php" method="post" name="newpc" class="style23" id="newpc">
  <fieldset class="style22">
  <legend class="style24"><span style="font-weight:bold">Contact Information</span></legend>
  <div>
    <table width="995" border="0">
      <tr>
        <td width="170">Company Name </td>
        <td width="248"><input name="company" type="text" id="company" size="40" /></td>
        <td width="20">&nbsp;</td>
        <td width="170">Requester Name</td>
        <td width="365"><input name="requester" type="text" id="requester" size="40" /></td>
      </tr>
      <tr>
        <td>Requester Contact </td>
        <td><input name="contact" type="text" id="contact" size="40" /></td>
        <td>&nbsp;</td>
        <td>Requester E-mail</td>
        <td><input name="email" type="text" id="email" size="40" /></td>
      </tr>
    </table>
  </div>
  </fieldset>
  <fieldset class="style22">
  <legend class="style25"><span style="font-weight:bold">Installation Details</span></legend>
  <table width="995" border="0">
    <tr>
      <td width="170">Preferred Username</td>
      <td width="248"><input name="username" type="text" id="username" size="40" /></td>
      <td width="20">&nbsp;</td>
      <td width="170">Peferred Password</td>
      <td width="365"><input name="password" type="text" id="password" size="40" /></td>
    </tr>
    <tr>
      <td>Operation Systems</td>
      <td><select name="os" size="1" class="style22" id="os">
        <option value="" selected="selected">Please Select...</option>
        <option value="Windows 10 Home x64">Windows 10 Home 64 bit</option>
		<option value="Windows 10 Pro x64">Windows 10 Professional 64 bit</option>
        <option value="Windows 8.1 x64">Windows 8.1 64 bit</option>
        <option value="Windows 8.1 Pro x64">Windows 8.1 Professional 64 bit</option>
        <option value="Windows 7 Pro x64">Windows 7 Professional 64 Bit</option>
		<option value="Windows 10 Home x32">Windows 10 Home 32 bit</option>
		<option value="Windows 10 Pro x32">Windows 10 Professional 32 bit</option>
        <option value="Windows 8.1 x32">Windows 8.1 32 bit</option>
        <option value="Windows 8.1 Pro x32">Windows 8.1 Professional 32 bit</option>
        <option value="Windows 7 Pro x32">Windows 7 Professional 32 Bit</option>
        <option value="Windows XP Pro">Windows XP Professional</option></select></td>
      <td>&nbsp;</td>
      <td>Microsofte Office</td>
      <td><select name="office" size="1" class="style22" id="office">
        <option value="" selected="selected">Please Select...</option>
        <option value="Office 365">Office 365</option>
        <option value="Professional Plus 2013 x64">Professional Plus 2013 64 bit</option>
        <option value="Professional 2013 x64">Professional 2013 64 bit</option>
        <option value="Home and Business 2013 x64">Home and Business 2013 64 bit</option>
		<option value="Professional Plus 2010 x64">Professional Plus 2010 64 bit</option>
        <option value="Professional 2010 x64">Professional 2010 64 bit</option>
        <option value="Home and Business 2010 x64">Home and Business 2010 64 bit</option>
		<option value="Professional Plus 2013 x32">Professional Plus 2013 32 bit</option>
        <option value="Professional 2013 x32">Professional 2013 32 bit</option>
        <option value="Home and Business 2013 x32">Home and Business 2013 32 bit</option>
		<option value="Professional Plus 2010 x32">Professional Plus 2010 32 bit</option>
        <option value="Professional 2010 x32">Professional 2010 32 bit</option>
        <option value="Home and Business 2010 x32">Home and Business 2010 32 bit</option>
        <option value="Professional 2007">Professional 2007</option>
		</select></td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
  </table>
  <table width="995" border="0">
    <tr>
      <td width="162">Default Language </td>
      <td width="197"><label>
        <input name="lang" type="radio" value="English (US)" />
      </label>
        English (US)
        <label></label></td>
      <td width="197"><label>
        <input name="lang" type="radio" value="English (UK)" />
      </label>
        English (UK)
        <label></label></td>
      <td width="423"><label>
        <input name="lang" type="radio" value="Thai" />
      </label>
Thai</td>
    </tr>
    <tr>
      <td>Harddisk Partition </td>
      <td><label></label><label>
        <input name="part" type="radio" value="Drive C and D (50/50)" />
</label>
Drive C: and D: (50/50)</td>
      <td><label></label>
      <label>
      <input name="part" type="radio" value="Drive C and D (30/70)" />
</label>
Drive C: and D: (30/70)</td>
      <td><label></label>
        <label>
<input name="part" type="radio" value="Drive C: Only" />
</label>
Only Drive C:
<label></label></td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
  </table>
  <div class="inline">
  <table width="985" border="0">
        <tr>
          <td width="162">Application Software </td>
          <td width="201"><input id="app1" name="app1" type="checkbox" value="Firefox" />             
          Mozilla Firefox </td>
          <td width="190"><input id="app2" name="app2" type="checkbox" value="Chrome" />            
          Google Chrome </td>
          <td width="219"><input id="app3" name="app3" type="checkbox" value="TeamViwer" />            
          TeamViwer</td>
          <td width="191"><input id="app4" name="app4" type="checkbox" value="Avast" />
          Avast Free Anitivirus </td>
        </tr>
        <tr>
          <td>&nbsp;</td>
          <td><input id="app5" name="app5" type="checkbox" value="AdobeReader" />
          Adobe Reader  </td>
          <td><input id="app6" name="app6" type="checkbox" value="Skype" />
            Skype</td>
          <td><input id="app7" name="app7" type="checkbox" value="Line" />
          Line</td>
          <td><input id="app8" name="app8" type="checkbox" value="7zip" />
 7-Zip</td>
        </tr>
        <tr>
          <td>&nbsp;</td>
          <td><input id="app9" name="app9" type="checkbox" value="CutePDF" /> 
            CutePDF</td>
          <td><input id="app10" name="app10" type="checkbox" value="AlwaySync" /> 
          Alway Sync</td>
          <td><input id="app11" name="app11" type="checkbox" value="FreeFileSync" /> 
            FreeFileSync</td>
          <td><input id="app12" name="app12" type="checkbox" value="FoxitReader" />
Foxit Reader </td>
        </tr>
        <tr>
          <td>&nbsp;</td>
          <td><input id="app13" name="app13" type="checkbox" value="GoogleDrive" /> 
            GoogleDrive
</td>
          <td><input id="app14" name="app14" type="checkbox" value="DropBox" /> 
            DropBox
</td>
          <td><input id="app15" name="app15" type="checkbox" value="VLC" /> 
            VLC Media Player
</td>
          <td>&nbsp;</td>
        </tr>
    </table>
  </div>
    <label for="post_content"></label>
    <label for="post_content"><span style="font-size: 14px; font-weight:bold"><br />
  </span></label>
    <fieldset class="style22">
    <legend class="style24"><span style="font-weight:bold">Additional Information </span></legend>
    <legend class="style19">Enter additional requirement or preferences:</legend>
    <ul class="style11"><li class="style31">Addtional software applications to install.</li>
      <li class="style31">E-mail account(s), and email configuration details from web host.</li>
      <li class="style30"><span class="style31">Printer information:
        
        
        </span>
        <ul>
          <li class="style31">For standalone printer, please specify the printer's brand and model.</li>
          <li class="style31">For network printer, we will remote access to setup the printer once we deliver the PC.</li>
        </ul>
      </li>
    </ul>
    <div>
      <table width="1202" border="0">
        <tr>
          <td width="22">&nbsp;</td>
          <td width="1170"><textarea id="notes" name="notes" style="width: 850px; height:60px"></textarea></td>
        </tr>
      </table>
    </div>
    </fieldset>
  </fieldset>
    <p class="style22">Thank you for your submission. We will contact you when the installation is completed. </p>
    <input id="submit" name="submit" type="submit" value="Submit" />
    or <a href="#">Cancel</a> </p>
</form>
</body>
</html>
