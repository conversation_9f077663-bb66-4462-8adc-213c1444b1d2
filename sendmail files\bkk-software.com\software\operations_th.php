<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';

$picCnt = 0;

?>
<!DOCTYPE html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html lang="en" class="no-js"> <!--<![endif]-->
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="โปรแกรมบริหารงานธุรกิจออนไลน์ เป็นโปรแกรมในรูปแบบของ Web-based application ช่วยจัดการในส่วนของงานขาย, สต็อก และงานจัดซื้อ ในรูปแบบการทำงานแบบออนไลน์">
    <meta name="keywords" content="business operations software, operation management systems, operation management software,โปรแกรมบริหารงานธุรกิจออนไลน์ , โปรแกรมจัดการสต๊อก , โปรแกรมบริหารงานจัดซื้อ">
    <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">

    <title>โปรแกรมบริหารงานธุรกิจออนไลน์</title>


<?php echo getPgCss(); ?>
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-main.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
      <h2>โปรแกรมบริหารงานธุรกิจออนไลน์</h2>
         <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
            <p>
                โปรแกรมบริหารงานธุรกิจออนไลน์ เป็นโปรแกรมในรูปแบบของ Web-based application ช่วยจัดการในส่วนของงานขาย, สต็อก และงานจัดซื้อ ในรูปแบบการทำงานแบบออนไลน์  โปรแกรมทำงานแบบศูนย์รวมอย่างเป็นระบบ   ข้อมูลการทำงานของทุกส่วนจะถูกเชื่อมโยงกันเป็นระบบเดียว  ลดขั้นตอนการทำงานที่ซ้ำซ้อนและเสียเวลา 
            </p>
            <p>
               MySaaS OPS ทำให้ผู้บริหารมองเห็นภาพรวมการทำงานและความเป็นไปของธุรกิจ  ซึ่งเป็นข้อมูลที่ช่วยในการตัดสินใจดำเนินกิจกรรมต่างๆ ทางธุรกิจต่อไป  โปรแกรมมีระบบความปลอดภัยของข้อมูล อีกทั้งสามารถขยายการใช้งานโปรแกรมได้ตามขนาดขององค์กรที่เติบโตขึ้น โดยที่ไม่จำเป้นต้องซื้อโปรแกรมใหม่ 
            </p>
            <p>
              MySaaS OPS ทำงานบนระบบ Cloud  คุณสามารถเข้าใช้งานโปรแกรมได้ทุกที ทุกเวลา แบบ real-time โดยคุณสามารถ
            </p>

            <ul class="me-list list-circle-check">
              <li>ตรวจสอบข้อมูลลูกค้า, เบอร์ติดต่อ หรือประวัติการขายสินค้า ผ่านระบบออนไลน์</li>
              <li>สามารถออกใบเสนอราคา, ส่งของหรือวางบิล ได้ทุกที ทุกเวลา</li>
              <li>ระบบบริหารจัดการสต้อกสินค้า, การโอนย้ายหรือการปรับปรุงจำนวนสินค้า</li>
              <li>ระบบแจ้งเตือนผ่านอีเมล์โดยตรงถึงผู้ที่เกี่ยวข้องเมื่อมีสินค้าต่ำกว่าหรือสูงกว่าสต็อก</li>
              <li>ระบบรับประกันสินค้า, และจัดเก็บสินค้าแบบ Lot หรือ Serial </li>
              <li>เก็บประวัติผู้ขาย, รายการสั่งซื้อและต้นทุนสินค้า</li>
              <li>มี Workflow ที่ช่วยออกแบบขั้นตอนการทำงานของโปรแกรมให้ตรงตามธุรกิจของลูกค้าได้</li>
            </ul>
            <h3>Features</h3>
            <ul class="me-list list-circle-check">
                <li>Single data source, providing accurate and consistent information across  organization.</li>
                <li>Effectively control and communicate business activities.</li>
                <li>Synchronize business processes (sales, manufacturing, finance, logistics etc.) in an collaborative environment.</li>
                <li>Improve efficiency, customer satisfaction and profitability.</li>
                <li>Provides real-time, enterprise-wide view of the business, enabling business owner to make better decision.</li>
              </ul>

  <p>
    MySaaS OPS สามารถทำงานเชื่อมกับโมดูลอื่นทั้ง <a href="crm.htm">Customer Relationship Management (CRM)</a> และ  ระบบบริหารงานซ่อมบำรุงออนไลน์  <a href="service-manager.htm">Field Service Manager (FSM)</a> ได้แบบ real-time 
  
  </p>


            </div>

            <!--Header_section-->
               <?php include('menu-software.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->

   
      <section class="container container-gray container-border">
         <div class="row">
            <div class="large-12 column text-center">
               <h3 class="heading-light">Operating Software for Business</h3>
               <p class="gap" data-gap-bottom="38">Everything you need to boost sales, step up productivity and manage all day-to-day activities</p>

               <ul class="portfolio-container large-block-grid-4 medium-block-grid-2 small-block-grid-1 no-wrap">
                  <li class="web motion">
                     <figure class="me-image">
                        <div class="image-content">
                           <img src="<?php echo IMG_URL?>software/sales-dashboard.png" alt="sales dashboard">
                           <div class="img-overlay"></div>  
                           
                           <a href="<?php echo IMG_URL?>software/sales-dashboard.png" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">
                              <i class="fa fa-search"></i>
                           </a>
                        </div>

                        <figcaption class="image-caption">
                           <span>Sales Dashboard</span>
                           <p>Provides real-time sales overview of your business</p>
                        </figcaption>
                     </figure>
                  </li>
                  <li class="motion">
                     <figure class="me-image">
                        <div class="image-content">
                           <img src="<?php echo IMG_URL?>software/sales-order.png" alt="Sales Order">
                           <div class="img-overlay"></div>  
                           
                           <a href="<?php echo IMG_URL?>software/sales-order.png" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">
                              <i class="fa fa-search"></i>
                           </a>
                        </div>

                        <figcaption class="image-caption">
                           <span>Sales Listings</span>
                           <p>Instant access to recent sales transactions and histories</p>
                        </figcaption>
                     </figure>
                  </li>
                  <li class="web print">
                     <figure class="me-image">
                        <div class="image-content">
                           <img src="<?php echo IMG_URL?>software/sales-order.png" alt="Sales Form">
                           <div class="img-overlay"></div>  
                           
                           <a href="<?php echo IMG_URL?>software/sales-order.png" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">
                              <i class="fa fa-search"></i>
                           </a>
                        </div>

                        <figcaption class="image-caption">
                           <span>Cusomizable Sales Form</span>
                           <p>Customize and print sales forms anywhere, anytime</p>
                        </figcaption>
                     </figure>
                  </li>
                  <li class="web">
                     <figure class="me-image">
                        <div class="image-content">
                           <img src="<?php echo IMG_URL?>software/sales-order.png" alt="Accounting Integration">
                           <div class="img-overlay"></div>  
                           
                           <a href="<?php echo IMG_URL?>software/sales-order.png" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">
                              <i class="fa fa-search"></i>
                           </a>
                        </div>

                        <figcaption class="image-caption">
                           <span>Accounting Integration</span>
                           <p>Seemless integration with Financial Module</p>
                        </figcaption>
                     </figure>
                  </li>
               </ul>

            </div>
         </div>
      </section>


      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->


<?php echo getPgFootJs(); ?>
</body>
</html>