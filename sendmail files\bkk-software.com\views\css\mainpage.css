
/* tag-slider
---------------------------------*/
#tag-slider{
  background:#EFEFEF;
    padding: 50px 0;
}

.slider_section {
  




}

/* Top_content
---------------------------------*/

.white_pad {
    position: relative;
    right: 0px;
    bottom: 0px;
}

.white_pad img {
    position: absolute;
    right: 0px;
    bottom: 0px;
}

.top_left_cont {
    padding: 25px 0;
}

.top_left_cont h3 {
    color: #333;
    margin: 0 0 20px 0;
    text-align: left;
}

.top_left_cont h2 {

    font-size: 34px;
    color: #333;
    line-height: 42px;
    margin: 0 0 26px 0;
    font-family: sans-serif;
    text-align: left;
}

.top_left_cont h2 strong {
    font-weight: 700;
}

.top_left_cont p {
    font-size: 14px;
    color: #666;
    margin: 0 0 30px 0;
    line-height: 26px;
}

a.read_more2 {
    font-family: 'Raleway', sans-serif;
    display: block;
    font-size: 16px;
    width: 178px;
    height: 46px;
    line-height: 46px;
    border-radius: 3px;
    text-align: center;
    text-transform: uppercase;
    font-weight: 600;
    transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -webkit-transition: all 0.2s ease-in-out;
    background: transparent;
    color: #fff;
    border: 1px solid #fff;
}

a.read_more2:hover {
    background: #fff;
    color: #df0031;
    border: 1px solid #fff;
}

	



/* tag-software
---------------------------------*/


#tag-software {
    padding: 60px 0px;
}
#tag-software h1 {
text-align: center;
}




/* IT Solutions
---------------------------------*/

#tag-itsolutions {
    padding: 60px 0px;
    background: #EFEFEF;
}
#tag-itsolutions h1 {
text-align: center;
}
.tag-itsolutions_wrapper {
    padding: 40px 0 40px;
}

.tag-itsolutions_section {
}

.tag-itsolutions_block {
    text-align: center;
    padding-left: 15px;
    padding-right: 15px;
}

.tag-itsolutions_icon {
    border: 2px solid #df0031;
    border-radius: 50%;
    width: 90px;
    height: 90px;
    margin: 0px auto 25px;
}

.tag-itsolutions_icon.icon2 {
    border: 2px solid #df0031;
}

.tag-itsolutions_icon.icon3 {
    border: 2px solid #df0031;
}

.tag-itsolutions_icon span {
    background: transparent;
    border-radius: 50%;
    display: block;
    height: 100%;
    width: 100%;
}

.tag-itsolutions_icon i {
    color: #df0031;
    font-size: 40px;
    margin-top: 0;
    position: relative;
    top: 20px;
    z-index: 20;

}

.tag-itsolutions_block h3 {
 
    font-size: 1.6em;
    color: #111111;
    margin: 10px 0 10px;
}

.tag-itsolutions_block p {
    font-size: 13px;
line-height: 18px;
    color: #666;
margin: 0 0 30px 0;
}
	

/* Services
---------------------------------*/

#tag-itservices {
    padding: 60px 0px;
text-align: center;
    
}

#tag-itservices h1 {
text-align: center;
}


#tag-itservices h3 {
font-size: 1.2em;    
}

.itservices-box p {
	margin-bottom: 20px;
line-height:18px;
font-size: 12px;
color: #666;

}
.itservices-grid-left span{
	margin-top:30px;
	width:105px;
	height:100px;	
}


.icon-itconsult span {
	display:inline-block;
	background: url(/views/img/menubar/it-consulting.gif) no-repeat;
}
.icon-maintenance span {
	display:inline-block;
	background: url(/views/img/menubar/it-support.gif) no-repeat;
}
.icon-helpdesk span {
	display:inline-block;
	background: url(/views/img/menubar/helpdesk.gif) no-repeat;
}
.icon-troubleshooting span {
	display:inline-block;
	background: url(/views/img/menubar/computer-troubleshoot.gif) no-repeat;
}



/* About Us
---------------------------------*/
 
#tag-aboutus {
    padding: 60px 0;
background:#EFEFEF;
}
#tag-aboutus h1 {
text-align: center;
}

/* Contact
---------------------------------*/
.page_section.contact {
    padding: 90px 0 100px;

}

.contact_section {
    margin: 0 0 60px 0;
}

.contact_section h2 {
    font-size: 40px;
    color: #ffffff;
    margin: 0 0 50px 0;
    text-transform: uppercase;
}

.contact_block {
    text-align: center;
}

.contact_block_icon {
    border-radius: 50%;
    height: 85px;
    width: 85px;
    margin: 0px auto 20px;
}

.contact_block_icon.icon2 {
    border-color: #df0031;
}

.contact_block_icon.icon3 {
    border-color: #49b5e7;
}

.contact_block_icon span {
    background: none repeat scroll 0 0 #f56eab;
    border: 5px solid #fff;
    border-radius: 50%;
    display: block;
    height: 100%;
    width: 100%;
}

.contact_block_icon.icon2 {
    border-color: #df0031;
}

.contact_block_icon.icon2 span {
    background: #df0031;
}

.contact_block_icon.icon3 {
    border-color: #49b5e7;
}

.contact_block_icon.icon3 span {
    background: #49b5e7;
}

.contact_block_icon i {
    color: #fff;
    font-size: 36px;
    margin-top: 0;
    position: relative;
    top: 26px;
    z-index: 20;
}

.contact_block span {
    display: block;
    font-size: 16px;
    color: #ffffff;
    line-height: 20px;
}

.contact_block span a {
    display: block;
    font-size: 16px;
    color: #ffffff;
    line-height: 20px;
}

.contact_info {
    font-size: 15px;
    margin: 0 0 0 20px;
    padding-left: 0;
}

.contact_info  h3 {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 30px;
    color: #fff;
}

.contact_info p {
    line-height: 28px;
    display: block;
    font-size: 16px;
  
    margin: 0 0 0px;

}
/*--map-Part-Starts-Here --*/
.map iframe{
	width:100%;
	height:350px;
	border:none;
}
/*--map-Part-Ends-Here --*/

.social_links {
    padding:0;
    margin: 0;
    display: block;
    overflow: hidden;
    list-style: none;
}

.social_links li {
    float: left;
    margin-right: 4px;
}

.social_links li a {
    display: block;
    width: 50px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 25px;
    color: #222222;
}

.social_links li a:hover, .social_links li a:focus {
    text-decoration: none;
    border-radius: 80px;
}

.twitter a:hover {
    color: #222222;
}

.facebook a:hover {
    color: #222222;
}

.pinterest a:hover {
    color: #222222;
}

.gplus a:hover {
    color: #222222;
}
 

.form {
    margin: 0 66px 0 30px;
}

.select {
    padding: 15px 16px;
    border: 1px solid #ccc;
    width: 100%;
    height: 55px;
    display: block;
    border-radius: 4px;
    font-size: 15px;
    color: #aaa;
    margin: 0 0 20px 0;
    transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -webkit-transition: all 0.3s ease-in-out;
}

.input-text {
    padding: 15px 16px;
    border: 1px solid #ccc;
    width: 100%;
    height: 55px;
    display: block;
    border-radius: 4px;
    font-size: 15px;
    color: #aaa;
    margin: 0 0 20px 0;
    transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -webkit-transition: all 0.3s ease-in-out;
}

.input-text:focus {
    border: 1px solid #fff;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(124, 197, 118, 0.3);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(124, 197, 118, 0.3);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(124, 197, 118, 0.3);
}

.input-text.text-area {
    height: 230px;
   
    resize: none;
    overflow: auto;
    
}

.input-btn {    
    width: 175px;
    height: 50px;
    letter-spacing: 0px;
    background: #df0031;
    border-radius: 3px;
    color: #ffffff;
    font-size: 16px;
    text-transform: uppercase;
    font-weight: 600;
    border: 0px;
    transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -webkit-transition: all 0.3s ease-in-out;
}

.input-btn:hover {
    background: #fff;
    color: #222;
}


/* Latest Work
---------------------------------*/

.inner_section {
    padding: 60px 0 20px;
}



.work_bottom {
    padding: 20px 0 0 0px;
}

.work_bottom span {
    font-size: 18px;
    color: #333333;
    display: block;
    margin: 0 0 20px 0;
}

a.contact_btn {
    background: #fff;
    text-transform: uppercase;
    display: block;
    width: 176px;
    height: 49px;
    text-align: center;
    line-height: 49px;
    font-size: 16px;
    color: #df0031;
    border-radius: 3px;
    font-family: sans-serif;
    font-weight: 600;
    border: 2px solid #df0031;
    transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -webkit-transition: all 0.2s ease-in-out;
}

a.contact_btn:hover {
    background: #df0031;
    color: #fff;
}

.input-text {
    background: transparent;
}






img {
    max-width: 100%;
}

/* no transition on .isotope container */

.isotope .isotope-item {
  /* change duration value to whatever you like */
    -webkit-transition-duration: 0.6s;
    -moz-transition-duration: 0.6s;
    transition-duration: 0.6s;
}

.isotope .isotope-item {
    -webkit-transition-property: -webkit-transform, opacity;
    -moz-transition-property: -moz-transform, opacity;
    transition-property: transform, opacity;
}





/*
   Media Queries
--------------------------- */
@media (min-width: 768px) {


}
@media (max-width: 768px) {
p{
font-size:14px;
}
.top_left_cont {
padding: 38px 0;
}
.top_left_cont h2 {
font-size: 25px;
color: #222222;
line-height: 39px;
margin: 0 0 9px 0;
}

.inner_section {
padding: 20px 0 20px;
}
#tag-aboutus img{
margin-bottom:20px;
}
#tag-aboutus h3{
font-size:16px;
line-height:22px;
}
.tag-itsolutions_block h3 { 
font-size: 16px;
}
tag-itsolutions_block p {
font-size: 14px; 
margin: 0px;
margin-bottom: 20px;
}
#filters ul{
padding-left:0px;
}
.form {
margin: 0 18px;
margin-right : 50px;
}
.page_section.team{
padding: 40px 0;
}
, #clients {
padding: 40px 0;
}
.client_logos ul li {
display: inline;
margin: 0 25px;
padding: 11px 0;
display: inline-block;
}
.social_links li {
float: left; 
}
.contact_section{
margin:0;
}
h2, .contact_section h2 {
font-size: 30px;
}
.page_section.contact {
padding: 60px 0 50px;
} 
.navbar-inverse .navbar-toggle .icon-bar{
background:#df0031;
}
.navbar-inverse .navbar-toggle {
border-color: transparent;
}
.navbar-inverse .navbar-toggle:hover, .navbar-inverse .navbar-toggle:focus{
background:none;
outline: none;
}
.navStyle ul li {
display: block;
}
.navStyle {
float: right;
width: 100%;
text-align:center;
}
.navStyle ul li:last-child {
margin: 0px;
}
.navbar-inverse .navbar-nav > li > a {
color: #222;
background: #F9F9F9;
}
} 
@media (max-width: 480px){ 
.team_area {
width: 99%;
}
}

table.tblStyle3 { color:#000; border-right: 1px solid #ccc;}

table.tblStyle3 tr td {
	font-size: 12px;
	text-align: center;
    margin: 0px;
    padding: 2px;
    padding-left: 5px;
    border-bottom: 1px solid #ccc;
    border-left: 1px solid #ccc;
}
table.tblStyle3 tr.section {
	font-size: 11px;
    background-color: #999;
    color: #fff;
	font-weight: bold;
}
table.tblStyle3 thead th {
    padding: 7px;
font-size:12px;
	font-weight: bold;
    border-left: 0px solid #fff;
    border-bottom: 0px solid #fff;
    border-right: 0px solid #fff;
}


