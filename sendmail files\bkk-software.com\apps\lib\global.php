<?php
ini_set("session.auto_start","1");

$VERBOSE_SYS	= false;
$VERBOSE_MYPHP	= false;
$VERBOSE_MYJS	= false;


if($VERBOSE_SYS){
	//error_reporting(E_ALL);			//all errors
	error_reporting(E_ERROR | E_WARNING | E_PARSE);
	ini_set('display_errors', TRUE);		
} else {
	error_reporting(0); 				//turn off
}


## name
$SITE_NICKNAME		= 'SWT';
$SITE_NAME			= 'Software Thailand';
$SITE_NAME_FULL		= 'Software Thailand';
//$SITE_NAME_FULL	= 'TechSpace Co., Ltd.';
//$SITE_DOMAIN		= 'www.software-thailand.net';
$SITE_DOMAIN_SHORT	= 'software-thailand.net';
//$SITE_URL			= 'http://www.software-thailand.net';

//--- User Login
$MIN_PSWD_LENGTH	= 8;		//password min length
$MAX_PSWD_LENGTH	= 30;
$MAX_IP_BLOCK_TIME	= 24;		//block access from a particular IP for 24 hours if usr verification during signed in fails

$EMAIL_SALES		= '<EMAIL>';
$EMAIL_SUPPORT		= '<EMAIL>';
$EMAIL_JOBS			= '<EMAIL>';



//--- field validation

$ALLOW_DOC_CHAR = '#\*\[\]-\/\\\.';

$URL_SPECIAL_CHAR = array(
	'/\//', '/\^/', '/\./', '/\$/', '/\|/',
	'/\(/', '/\)/', '/\[/', '/\]/', '/\*/',
	'/\+/', '/\?/', '/\{/', '/\}/', '/\,/',
	'/\&/', '/\'/', '/:/', '/</', '/>/',
	'/#/', '/\%/', '/\@/', '/;/', '/=/',
	'/\^/', '/\|/', '/~/', '/\s+/'
);

$URL_SPECIAL_CHAR_REPLACE = array(
	'-', '-', '-', '-', '-',
	'-', '-', '-', '-', '-',
	'-', '-', '-', '-', '-',
	'-', '-', '-', '-', '-',
	'-', '-', '-', '-', '-',
	'-', '-', '-', '-' 
);


$excludes = array("a", "i",
	"an", "as", "at", "be", "by", "if", "in", "is", "it", "of", "on", "or", "to", "up",
	"and", "are", "but", "for", "nor", "out", "the",
	"else", "from", "that", "then", "this", "over", "when", "with");
$excludesCapFirst = array("A", "I",
	"An", "As", "At", "Be", "By", "If", "In", "Is", "It", "Of", "On", "Or", "To", "Up",
	"And", "Are", "But", "For", "Nor", "Out", "The",
	"Else", "From", "That", "Then", "This", "Over", "When", "With");


?>