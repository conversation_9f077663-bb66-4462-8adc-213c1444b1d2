<?php
//--- password encoding -----------------------------
//--- Calculate HMAC according to RFC2104, http://www.ietf.org/rfc/rfc2104.txt
function hmac($key, $data, $hash = 'md5', $blocksize = 64) {
  if(strlen($key)>$blocksize){$key = pack('H*', $hash($key));}
  $key  = str_pad($key, $blocksize, chr(0));
  $ipad = str_repeat(chr(0x36), $blocksize);
  $opad = str_repeat(chr(0x5c), $blocksize);
  return $hash(($key^$opad) . pack('H*', $hash(($key^$ipad) . $data)));
}
//--------------------------------
//--- Remember to initialize MT (using mt_srand() ) if required
function pw_encode($password) {
  $seed = substr('00' . dechex(mt_rand()), -3) .
   substr('00' . dechex(mt_rand()), -3) .
   substr('0' . dechex(mt_rand()), -2);
  return hmac($seed, $password, 'md5', 64) . $seed;
}
//--------------------------------
function pw_check($password, $secret_pswd) {
  $seed = substr($secret_pswd, 32, 8);
  return hmac($seed, $password, 'md5', 64) . $seed==$secret_pswd;
}
//--------------------------------


?>