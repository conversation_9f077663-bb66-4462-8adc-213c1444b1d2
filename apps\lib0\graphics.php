<?php  //-- images

$VALID_IMG_TYPE = array(  
	'image/pjpeg'	=> 'jpg',
	'image/jpeg'	=> 'jpg',
	'image/jpeg'	=> 'jpeg',
	'image/gif'		=> 'gif',
	'image/X-PNG'	=> 'png',
	'image/PNG'		=> 'png',
	'image/png'		=> 'png',
	'image/x-png'	=> 'png',
	'image/JPG'		=> 'jpg',
	'image/GIF'		=> 'gif'
//	'image/bmp'		=> 'bmp',
//	'image/bmp'		=> 'BMP'
);


//------------------------
function validImgType($type){
	global $VALID_IMG_TYPE;
	if(!array_key_exists($type, $VALID_IMG_TYPE)){return false;}
	else{return true;}
}
//------------------------
function getImgType($filename){
	global $VALID_IMG_TYPE;
	$fileArr = explode('.', $filename);
	$fileExt = array_pop($fileArr);					
	foreach($VALID_IMG_TYPE as $key=>$val){
		if($fileExt == $val){return strtoupper($val);}
	}
	return '';
}

//------------------------
function copyImg($srcPath, $dstDir, $dstFilename, $width, $height, $proportional=true, $crop=false, $delOrig=false){
$errorMsg = array();
	$picSize = getimagesize($srcPath);
	$dstPath = $dstDir . $dstFilename;
	if($picSize[0] > $picSize[1]){ //width > height
		if($picSize[0] > $maxWidth){//create original image
			smart_resize_image($srcPath, $width, $height, $proportional, $crop, $output=$dstPath, false, $use_linux_commands=false);
		} else { //original image is good, copy it over
			if(!copy($srcPath , $dstPath)){	$errorMsg[] = "Error occur while creating avatar image. Destination: '$dstPath'.";}
		}
	} else { //width < height
		if($picSize[1] > $maxHeight){
			smart_resize_image($srcPath, $width, $height, $proportional, $crop, $output=$dstPath, false, $use_linux_commands=false);
		} else { //original image is good, copy it over
			if(!copy($srcPath , $dstPath)){$errorMsg[] = "Error occur while creating avatar image. Destination: '$dstPath'.";}
		}
	}
	if($delOrig){@unlink($srcPath);}
	
return implode('<br>', $errorMsg);
}
//------------------------
function smart_resize_image($file, $width=0, $height=0, $proportional=false, $crop=false, $output='file', $delete_original=true, $use_linux_commands=false){
	if( $height <= 0 && $width <= 0 ) {return false;}
	$info = getimagesize($file);
	$image = '';
   
	$final_width = 0;
	$final_height = 0;
	list($width_old, $height_old) = $info;

if ($proportional) {
	if ($width == 0) $factor = $height/$height_old;
	elseif ($height == 0) $factor = $width/$width_old;
	else $factor = min ( $width / $width_old, $height / $height_old);  

	$final_width = round ($width_old * $factor);
	$final_height = round ($height_old * $factor);
} else {   
	$final_width = ( $width <= 0 ) ? $width_old : $width;
	$final_height = ( $height <= 0 ) ? $height_old : $height;
}


/* Start of new Code for cropping */
$int_width = 0;
$int_height = 0;

$adjusted_height = $final_height;
$adjusted_width = $final_width;

if ($crop) {
$wm = $width_old/$width;
$hm = $height_old/$height;
$h_height = $height/2;
$w_height = $width/2;

$ratio = $width/$height;
$old_img_ratio = $width_old/$height_old;

if ($old_img_ratio > $ratio) {
$adjusted_width = $width_old / $hm;
$half_width = $adjusted_width / 2;
$int_width = $half_width - $w_height;
} else if($old_img_ratio <= $ratio) {
$adjusted_height = $height_old / $wm;
$half_height = $adjusted_height / 2;
$int_height = $half_height - $h_height;
}
}


/* End new code for cropping*/

switch ($info[2] ) {
	case IMAGETYPE_GIF:
		$image = imagecreatefromgif($file);
	break;
	case IMAGETYPE_JPEG:
		$image = imagecreatefromjpeg($file);
	break;
	case IMAGETYPE_PNG:
		$image = imagecreatefrompng($file);
	break;
	default:
		return false;
}
   
$image_resized = imagecreatetruecolor( $final_width, $final_height );
	   
if ( ($info[2] == IMAGETYPE_GIF) || ($info[2] == IMAGETYPE_PNG) ) {
	$trnprt_indx = imagecolortransparent($image);

	// If we have a specific transparent color
	if ($trnprt_indx >= 0) {
		// Get the original image's transparent color's RGB values
		$trnprt_color    = imagecolorsforindex($image, $trnprt_indx);
		// Allocate the same color in the new image resource
		$trnprt_indx    = imagecolorallocate($image_resized, $trnprt_color['red'], $trnprt_color['green'], $trnprt_color['blue']);
		// Completely fill the background of the new image with allocated color.
		imagefill($image_resized, 0, 0, $trnprt_indx);
		// Set the background color for new image to transparent
		imagecolortransparent($image_resized, $trnprt_indx);   
	}
	// Always make a transparent background color for PNGs that don't have one allocated already
	elseif ($info[2] == IMAGETYPE_PNG) {
		// Turn off transparency blending (temporarily)
		imagealphablending($image_resized, false);
		// Create a new transparent color for image
		$color = imagecolorallocatealpha($image_resized, 0, 0, 0, 127);
		// Completely fill the background of the new image with allocated color.
		imagefill($image_resized, 0, 0, $color);
		// Restore transparency blending
		imagesavealpha($image_resized, true);
	}
}

imagecopyresampled($image_resized, $image, -$int_width, -$int_height, 0, 0, $adjusted_width, $adjusted_height, $width_old, $height_old);


if ( $delete_original ) {
	if ( $use_linux_commands )
		exec('rm '.$file);
	else
		@unlink($file);
}

switch ( strtolower($output) ) {
	case 'browser':
		$mime = image_type_to_mime_type($info[2]);
		header("Content-type: $mime");
		$output = NULL;
	break;
	case 'file':
		$output = $file;
	break;
	case 'return':
		return $image_resized;
	break;
	default:
	break;
}

switch (
$info[2] ) {
	case IMAGETYPE_GIF:
		imagegif($image_resized, $output);
	break;
	case IMAGETYPE_JPEG:
		imagejpeg($image_resized, $output);
	break;
	case IMAGETYPE_PNG:
		imagepng($image_resized, $output);
	break;
	default:
		return false;
}

return
true;
}
//-------NOT IN USE -----
function resizeImage($pic){
// Set a maximum height and width
$width = 200;
$height = 200;

// Get new dimensions
list($width_orig, $height_orig) = getimagesize($pic);

$ratio_orig = $width_orig/$height_orig;

if ($width/$height > $ratio_orig) {
   $width = $height*$ratio_orig;
} else {
   $height = $width/$ratio_orig;
}

}
?>