<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB0_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library


$picCnt = 0;

?>
<!DOCTYPE html>

<html><head>
   <meta charset="utf-8">
   <meta http-equiv="X-UA-Compatible" content="IE=edge"> 
   <meta name="description" content="<?php echo $SITE_NAME;?> committed to building a workforce that respect individual skills and diversity while valuing team contribution.">
   <meta name="keywords" content="IT jobs,software developer,php developer job, cloud administrator job, IT Career Opportunities,สมัครงานit,งานIT Support,IT Helpdesk">
   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">
	
   <link rel="canonical" href="https://www.bkk-it.com/coy/career.html" />
	
	<meta property="og:title" content="Career Opportunities ร่วมเป็นส่วนหนึ่งกับเรา<?php echo $SITE_NAME;?>" />
<meta property="og:description" content="<?php echo $SITE_NAME;?> IT Outsourcing Services Company เปิดรับสมัครงานหลายอัตรา ร่วมเป็นส่วนหนึ่งกับบริษัทไอที รุ่นใหม่ กับเรา ดูรายละเอียดเพิ่มเติมที่นี่ " />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://www.bkk-it.com/coy/career.html" />
<meta property="og:image" content="https://www.bkk-it.com/views/img/slideshow/career-<?php echo $SITE_NAME;?>.gif" />
<meta property="og:site_name" content="<?php echo $SITE_NAME;?>" />
	
   <title>ร่วมงานกับ <?php echo $SITE_NAME;?></title>


<?php echo getPgCss(); ?>

<style>
  .jobDetails {
     font-size: 85%;;
     color: #666;
  }

  tr.jobEntry0 td {
    background-color:  #fff;
  }

  tr.jobEntry1 td {
    background-color:  #EAF4FB;
  }
</style>

<script type="text/javascript">
function toggleDisplay(obj){
   if(document.getElementById(obj).style.display == 'none') {document.getElementById(obj).style.display = '';
   } else {document.getElementById(obj).style.display = 'none';}
}
</script>


</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-career.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
            <h1>Career@<?php echo $SITE_NAME;?></h1>
            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
     
              <p>
               <?php echo $SITE_NAME?> เปิดโอกาสให้ผู้ที่มีศักยภาพและต้องการความก้าวหน้ามาร่วมเป็นกำลังสำคัญในการสร้างสรรค์ 
              </p>
              <p>
               อีกทั้งพัฒนาองค์กรให้เติบโตอย่างมั่นคงและยั่งยืน เราเล็งเห็นถึงความสำคัญของความก้าวหน้าในการทำงาน การบริหารจัดการ 
              </p>
               พร้อมทั้งมุ่งส่งเสริมพัฒนาศักยภาพของพนักงานให้เติบโตเคียงคู่องค์กรอย่างมั่นคงและคำนึงถึงการให้โอกาสที่เท่าเทียมกันในการทำงาน 
               </p>
               <p>
                 เรายินดีต้อนรับทุกท่านเข้าเป็นส่วนหนึ่งของทีมและเติบโตไปด้วยกันกับเรา
              </p>
              <p><a href="mailto:<?php echo $EMAIL_JOBS?>">อีเมลล์</a> ถึงเราสำหรับการสมัครงาน</p>



            </div>

            <!--Header_section-->
               <?php include('menu-coy.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->



      <!-- container -->
      <section class="container" style="padding-top:0">
        <div class="row">
          <h2>Current Job Openings </h2>
          <?php
            $row = 1;
          ?>

          <?php include("career_openings.php"); ?>


        </div>
      </section>
      <!-- container end here -->



      <?php echo getPgFootJs(); ?>

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->

</body>
</html>