
function validEmail(email) {
  var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(email);
}


jQuery(document).ready(function($) {	
	var focusColor = "#169fe6";
	var labelError = { "color" : "#fff", "background" :"#ff4444" };
	var formError = { "border-color" : "#ff4444" };

	$('#buttonsend').click( function() {

		var name    	= $('.contact-form #name').val();
		var company 	= $('.contact-form #company').val();
		var enquiryType = $('.contact-form #enquiryType').val();
		var subject 	= $('.contact-form #subject').val();
		var email   	= $('.contact-form #email').val();
		var message 	= $('.contact-form #message').val();
		var honeypot	= '';

		if( $('.contact-form #contact_me_by_fax_only').prop('checked') ){
			honeypot 	= $('.contact-form #contact_me_by_fax_only').val();
		}



		$('.loading').css('display','inline-block').fadeIn('slow');

		var validData 	= false;

		if (name != "" && company != "" && enquiryType != "" && subject != "" && message != "") {
			if(validEmail(email)){	validData = true;}
		}

		if(validData){
			console.log("get data");
			$.ajax({
				url: 'https://www.bkk-it.com/coy/sendmail.php',
				type: 'POST',
				data: "name=" + name + "&company=" + company + "&enquiryType=" + enquiryType + "&subject=" + subject + "&email=" + email + "&honeypot=" + honeypot + "&message=" + message,
				success: function(result) {
					$('.loading').fadeOut('fast');
					
					console.log("flag");
					/*
					if(result == "email_error") {
						$('#email').css(formError).next('.require').text(' !');
						$('label[for="email"]').css(labelError);
						return false;

					} else {
					*/
						location.href="thankyou.html"

						/*
						$('#name, #company, #subject, #email, #message').val("","Name","company","Subject","Email","Message");
						$('.success-contact').fadeIn();
						$('.success-contact').fadeOut(25000, function(){ $(this).remove(); });
						*/
		/*			} */
				},
				error: function(result) {

	            },
			});

		} else {
			$('.loading').fadeOut('slow');

			if(name <= 0) { 
				$('.contact-form #name').css(formError); 
				$('label[for="name"]').css(labelError);
			} else {
				$('.contact-form #name, label[for="name"]').attr('style', '');
			}

			if(company <= 0) {
				$('.contact-form #company').css(formError);
				$('label[for="company"]').css(labelError);
			} else {
				$('.contact-form #company, label[for="company"]').attr('style', '');
			}

			if(enquiryType <= 0) {
				$('.contact-form #enquiryType').css(formError);
				$('label[for="enquiryType"]').css(labelError);
			} else {
				$('.contact-form #enquiryType, label[for="enquiryType"]').attr('style', '');
			}

			if(subject <= 0) {
				$('.contact-form #subject').css(formError);
				$('label[for="subject"]').css(labelError);
			} else {
				$('.contact-form #subject, label[for="subject"]').attr('style', '');
			}

			if(validData==false) {
				$('.contact-form #email').css(formError);
				$('label[for="email"]').css(labelError);
			} else {
				$('.contact-form #email, label[for="email"]').attr('style', '');
			}

			if(message <= 0) {
				$('.contact-form #message').css(formError);;
				$('label[for="message"]').css(labelError);
			} else {
				$('.contact-form #message, label[for="message"]').attr('style', '');
			}

			return false;
		}
	});
		
	$("#name, #company, #enquiryType, #subject, #email, #message").focus(function(){
		$("label[for='"+$(this).attr('id')+"']").addClass('focus');
		$(this).attr('style', '');
		$("label[for='"+$(this).attr('id')+"']").attr('style', '');
	}).blur(function() {
		$("label[for='"+$(this).attr('id')+"']").removeClass('focus');
		$(this).attr('style', '');
	});      	
});