<?php

/******** START BOOTSTRAP *********/

//--- 1.  bootstrap

define('DS', DIRECTORY_SEPARATOR);

include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';

include_once LIB0_PATH . 'bootstrap.php';



//--- 2. start session

//startBeSession();



//--- 3. extract parsed variables

extract($_GET);	extract($_POST);



/*

//--- 4. instantiate DB

$myDb		= new sysDB;	$myDb->init();

//====>>>>> DB Connection Established after this line ====>>>>>



//--- 5. SM init

include_once LIB1_PATH . 'sm_init.php';

*/



//==== include language type

$langUrl = SF_URL;

if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}

define('LANG_URL',		$langUrl);







							

//--- 6. special library





$picCnt = 0;



?>

<!DOCTYPE html>



<html>

<head>

   <meta charset="utf-8">

   <meta http-equiv="X-UA-Compatible" content="IE=edge"> 

	 <title>Field Service Manager, Work Order Management - <?php echo $SITE_NAME;?></title>

   <meta name="description" content="MySaaS Field Service Manager helps track work orders and service cost including labor, material and service charges.">

   <meta name="keywords" content="field service management, facility management, CMMS, work order management, PM Plan, work scheduling,field service management,work order management,planning software">

   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">

 <link rel="canonical" href="https://www.bkk-it.com/en/software/service-manager.html" />

  



<?php echo getPgCss(); ?>

</head>

<body>



   <!-- main-container -->

   <div id="main-container">



      <!--Header_section-->

         <?php include(INCLUDE_PATH . 'head.php');?>

      <!--Header_section--> 







      <!-- slideshow -->

         <?php include(INCLUDE_PATH . 'slider-software_en.php');?>

      <!-- slideshow end here -->



 

       <!-- container -->

      <section class="container">

         <div class="row">

      <h1>MySaaS Field Service Manager (FSM) / CMMS</h1>

          <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >

            <p>

              MySaaS Field Service Manager is a simple yet powerful application that is designed to help  improving work efficiency and customer satisfaction. It provides a 360 view of all your field service activities, enabling you to keep track of your  jobs, customers, invoices and schedules online.

            </p>

            <p>With MySaaS Field Service Manager, you can plan, schedule, assign and track work orders online. Additionally, the application also allows you to track job cost and labor utilization. Managers, engineers and technicians can track and update work  status through their laptops or mobile applications, providing instant  work status update to the customer and team.</p>



            <p>MySaaS FSM runs on the cloud, providing instant, real-time access to work order information anywhere, anytime. With MySaaS FSM, you can:</p>

            <ul class="me-list list-circle-check">

              <li>Create, submit, approve, track, and manage work orders in   real-time</li>

              <li>Receive work notification and alerts via E-mail or SMS.</li>

              <li>PM Planning and auto-scheduling of work order on due dates</li>

              <li>Maintain equipment data, service history and costs</li>

              <li>Configurable workflows to match your business needs.</li>

              <li>Improve  operational  efficiencies and customer satisfaction</li>

            </ul>



<img src="<?php echo IMG_URL?>software/service-manager-overview.png" alt="service manager overview"><br/><br/>





           <p>Key Features:</p>

           <ul class="me-list list-circle-check">

              <li>Increase productivity, improve customer loyalty, and save more time  each day</li>

              <li>Gain greater control of  jobs and service personnel.</li>

              <li>Track work orders and service cost including labor, material and service charges.</li>

              <li>Create and dispatch work orders across multiple business units, region or locations.</li>

              <li>Simple, easy to use interface for technician/repair men to enter work status.</li>

              <li>Ability to configure  workflow behaviors</li>

              <li>User-definable email notification and screen alert capabilities</li>

              <li>User-definable service code with labor, material and service charges</li>

              <li>Generate billings and service reports</li>

              <li>Track job time, response time, machine downtime and other metrics </li>

              <li>Mobile work order integration</li>

            </ul>



           <p>MySaaS FSM is fully integrated with the <a href="operations.html" target="_blank">MySaaS Operation Management System (OPS)</a>, providing    real-time access to customer billings and stocks information.</p>



            </div>



            <!--Header_section-->

               <?php include('menu-software.php');?>

            <!--Header_section--> 



         </div>   

      </section>

      <!-- container end here -->



   

      <section class="container container-gray container-border">

         <div class="row">

            <div class="large-12 column text-center">

               <h2 class="heading-light">Gain Control of your Maintenance Processes with MySaaS</h2>

               <p class="gap" data-gap-bottom="38">Improve efficiency, performance, collaboration and cut cost.</p>



               <ul class="portfolio-container large-block-grid-4 medium-block-grid-2 small-block-grid-1 no-wrap">

                  <li class="web">

                     <figure class="me-image">

                        <div class="image-content">

                           <img src="<?php echo IMG_URL?>software/fsm-dashboard.png" alt="FSM dashboard">

                           <div class="img-overlay"></div>  

                           

                           <a href="<?php echo IMG_URL?>software/fsm-dashboard.png" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">

                              <i class="fa fa-search"></i>

                           </a>

                        </div>



                        <figcaption class="image-caption">

                           <span>Service Dashboard</span>

                           <p>Quick access to key metrics including job status and assignements.</p>

                        </figcaption>

                     </figure>

                  </li>

                  <li class="web">

                     <figure class="me-image">

                        <div class="image-content">

                           <img src="<?php echo IMG_URL?>software/fsm-plan.png" alt="PM Plan">

                           <div class="img-overlay"></div>  

                           

                           <a href="<?php echo IMG_URL?>software/fsm-plan.png" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">

                              <i class="fa fa-search"></i>

                           </a>

                        </div>



                        <figcaption class="image-caption">

                           <span>PM Planning & Scheduling</span>

                           <p>Automate PM Planning, scheduling and job assisgnment </p>

                        </figcaption>

                     </figure>

                  </li>

                  <li class="web">

                     <figure class="me-image">

                        <div class="image-content">

                           <img src="<?php echo IMG_URL?>software/fsm-job.png" alt="Job Listing">

                           <div class="img-overlay"></div>  

                           

                           <a href="<?php echo IMG_URL?>software/fsm-job.png" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">

                              <i class="fa fa-search"></i>

                           </a>

                        </div>



                        <figcaption class="image-caption">

                           <span>Job Listing / Search</span>

                           <p>View job status, assignments, overdue jobs. Search job history</p>

                        </figcaption>

                     </figure>

                  </li>

                  <li class="web">

                     <figure class="me-image">

                        <div class="image-content">

                           <img src="<?php echo IMG_URL?>software/fsm-job-details.png" alt="Job Details">

                           <div class="img-overlay"></div>  

                           

                           <a href="<?php echo IMG_URL?>software/fsm-job-details.png" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">

                              <i class="fa fa-search"></i>

                           </a>

                        </div>



                        <figcaption class="image-caption">

                           <span>Job Details</span>

                           <p>Create or edit job details. Add tasks, labor hours, parts and attachments</p>

                        </figcaption>

                     </figure>

                  </li>

               </ul>









               <ul class="portfolio-container large-block-grid-4 medium-block-grid-2 small-block-grid-1 no-wrap">

                  <li class="web">

                     <figure class="me-image">

                        <div class="image-content">

                           <img src="<?php echo IMG_URL?>software/fsm-notification.png" alt="Alert & Notification">

                           <div class="img-overlay"></div>  

                           

                           <a href="<?php echo IMG_URL?>software/fsm-notification.png" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">

                              <i class="fa fa-search"></i>

                           </a>

                        </div>



                        <figcaption class="image-caption">

                           <span>Job Alert / Notification</span>

                           <p>Automated email notification on new job assignment & ovderdue jobs.</p>

                        </figcaption>

                     </figure>

                  </li>

                  <li class="web">

                     <figure class="me-image">

                        <div class="image-content">

                           <img src="<?php echo IMG_URL?>software/fsm-jobcost.png" alt="Job Costing">

                           <div class="img-overlay"></div>  

                           

                           <a href="<?php echo IMG_URL?>software/fsm-jobcost.png" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">

                              <i class="fa fa-search"></i>

                           </a>

                        </div>



                        <figcaption class="image-caption">

                           <span>Job Costing</span>

                           <p>Track job costing including labor, parts, service & subcontracting cost.</p>

                        </figcaption>

                     </figure>

                  </li>

                  <li class="web">

                     <figure class="me-image">

                        <div class="image-content">

                           <img src="<?php echo IMG_URL?>software/fsm-workflow.png" alt="Workflow Management">

                           <div class="img-overlay"></div>  

                           

                           <a href="<?php echo IMG_URL?>software/fsm-workflow.png" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">

                              <i class="fa fa-search"></i>

                           </a>

                        </div>



                        <figcaption class="image-caption">

                           <span>Workflow Management</span>

                           <p>Define individual, team or company wide workflow for service team</p>

                        </figcaption>

                     </figure>

                  </li>

                  <li class="web">

                     <figure class="me-image">

                        <div class="image-content">

                           <img src="<?php echo IMG_URL?>software/fsm-log.png" alt="Service Log">

                           <div class="img-overlay"></div>  

                           

                           <a href="<?php echo IMG_URL?>software/fsm-log.png" class="preview fancybox" data-fancybox-group="gallery" title="Click to enlarge">

                              <i class="fa fa-search"></i>

                           </a>

                        </div>



                        <figcaption class="image-caption">

                           <span>Service Log</span>

                           <p>View job service log, track work histories and more ...</p>

                        </figcaption>

                     </figure>

                  </li>

               </ul>

            </div>

         </div>

      </section>





      <!--Header_section-->

         <?php include(INCLUDE_PATH . 'foot.php');?>

      <!--Header_section--> 



   </div>

   <!-- main-container end here -->





<?php echo getPgFootJs(); ?>

</body>

</html>