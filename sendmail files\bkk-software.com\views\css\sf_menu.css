#header_wrapper {
    background: #00ADEF;
    padding: 20px 0px 5px 0;
	min-height:74px;
}


.logo {
    float: left;
    margin-top: -5px;
	position: absolute;
	z-index: 2;
}

.res-nav_click {
    display: none;
}
	

/******************************************************/

#menu {
	float: right;
	position:relative;
	z-index: 999;
}


#menu li{
	display: inline-block;
	list-style-type: none;
}



#menu li a:hover{
	color: #333;
}

#menu ul {
float: left;
list-style: none;
margin: 0;
padding: 0;
}

#menu ul a {
color: #fff;
font-size: 1.11em;
text-decoration: none;
}

#menu ul a:hover {color: #333;}



#menu ul ul {
      position: absolute;
	width: 180px;
	background: none repeat scroll 0 0 #FFFFFF;
    border: 1px solid #E5E5E5;
    border-radius: 5px 5px 5px 5px;
    box-shadow: 0 0 10px #E8E8E8 inset;
    padding: 10px;

}

#menu ul ul a {
	display: block;
 font-size:13px;
	line-height:28px;
   padding-left: 5px;
	color: #666;
	background: #FFFFFF;
	text-decoration: none; 
}

#menu ul ul a:hover {
	color: #666;
	background: #ffffde;
}

#menu ul ul li {float: left; width: 100%;  margin:0; padding: 0px; }

#menu ul ul ul {
	position: absolute;
	top: 0;
	left: 100%;

}




.divider {background: url(/img/css/bg-divider.gif) 0 50% no-repeat;}


#menu .hbar {border-top: 1px dashed #d0d0d0;}




#menu ul li ul { visibility: hidden; }
#menu ul li:hover > ul { visibility: visible; z-index: 999; }

#menu  li ul {
    opacity: 0;
    transition: all 0.5s ease 0.1s;
}

#menu  li:hover ul {
    opacity: 1;
    transition: all 0.5s ease 0s;
}


#menu a.btn-success{
	border-radius: 20px;
	padding: 10px 16px 7px 16px;
	margin-left: 20px;
}
#menu a.btn-success:hover{
	border-color: #FFF;
	background-color: transparent;
	color: #FFF;
}

#menu li.btn-language{
padding-left: 20px;
}
#menu li.btn-language a{
display:inline;
padding:0;
}

#menu li.btn-language img{
margin-top:15px;
padding: 0 0 10px 5px;
float:left;
}
/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
* HEADER - MENU
* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
.sticky-wrapper {
	position: relative;
	z-index: 1000;
	height: 95px;
	margin-bottom: -95px !important;
}
#navigation{
	width: 100%;
	z-index: 1000;
	position: relative;
    -webkit-transition-duration: 0.4s;
       -moz-transition-duration: 0.4s;
            transition-duration: 0.4s;
}
#navigation.navigation-fixed{
	padding: 20px 0 25px 0;

	border-bottom: solid 3px rgba(255, 255, 255, .8);
	width: 100%;
	position: fixed;
	top: 0;
	z-index: 1000;
    -webkit-transition-duration: 0.4s;
       -moz-transition-duration: 0.4s;
            transition-duration: 0.4s;
}

#navigation.navigation-fixed, .mean-container .mean-nav ul{
	background-color: #333;
	color: #0DB4E9;
}

#navigation a{
	color: #FFFFFF;
}



/*---start-footer-grids---*/
#footer_wrapper{
	background: #efefef;
	padding: 30px 0;
}

.footer-grid{
	width: 23.33%;
	float: left;
	margin-right: 2%;
padding-top:15px;
}
.footer-grid:nth-child(3n+1){
	margin-right:0;
}
.footer-grid h3{
	color:#666;
	font-size:1em;
	font-family: 'open_sanssemibold';
font-weight: normal;
	margin-bottom: 0.5em;
}
.footer-grid ul{ 
padding:0;
line-height: 1.5em;
}
.footer-grid ul li a{
	color: #999;
	font-size: 0.9em;
	transition:0.5s all;
	-webkit-transition:0.5s all;
	-moz-transition:0.5s all;
	-o-transition:0.5s all;
}
.footer-grid ul li a:hover{
	zoom: 1;
	filter: alpha(opacity=75);
	opacity: 0.7;
	-webkit-transition: opacity .15s ease-in-out;
	-moz-transition: opacity .15s ease-in-out;
	-ms-transition: opacity .15s ease-in-out;
	-o-transition: opacity .15s ease-in-out;
	transition: opacity .15s ease-in-out;
}
.social-icons li{
	display:inline-block;
}
.social-icons li a{
	width:48px;
	height:45px;
	display:block;
}
.social-icons li a.facebook{
	background:url(/views/img/footer-icons.png) no-repeat 0px 0px;
}
.social-icons li a.twitter{
	background: url(/views/img/footer-icons.png) no-repeat -56px 0px;
}
.social-icons li a.youtube{
	background: url(/views/img/footer-icons.png) no-repeat -112px 0px;
}
.footer-grid p{
	color: #A2A2A2;
	font-size: 0.875em;
	line-height: 1.5em;
	padding:0 0 0.4em 0;
}


.copy-right{
	margin-top:1em;
}
.copy-right a{
	color: #A2A2A2;
	transition:0.5s all;
	-webkit-transition:0.5s all;
	-moz-transition:0.5s all;
	-o-transition:0.5s all;
}
.copy-right a:hover{
	color:#F36EA7;
}
/*---//End-footer-grids---*/



.wrap{
	width:70%;
	margin:0 auto;
}

/*---start-responsive-design----*/
@media only screen and (max-width:1440px) and (min-width:1366px) {
	.wrap {
		width: 75%;
	}
}
@media only screen and (max-width:1366px) and (min-width:1280px) {
	.wrap {
		width:80%;
	}
}
@media only screen and (max-width:1280px) and (min-width:1024px) {
	.wrap {
		width:80%;
	}
}
@media only screen and (max-width:1024px) and (min-width:768px) {
	.wrap {
		width:80%;
	}
}
@media only screen and (max-width:768px) and (min-width:640px) {

	.wrap {
		width:80%;
	}
	.footer-grid {
		width:100%;
		float: none;
		margin-right:0%;
	}
	.footer-grid ul li {
		display:inline-block;
		margin-right:0.6em;
	}
	.footer-grid h3 {
		margin-bottom: 0em;
	}
	.footer-grid ul{
		margin:0.5em 0;
	}
	.footer-grid p {
		padding: 0.5em 0;
	}
	.copy-right {
		margin:1em 0 1em 0;
	}
}
@media only screen and (max-width:640px) and (min-width:480px) {
	.wrap {
		width:80%;
	}
	.footer-grid {
		width:100%;
		float: none;
		margin-right:0%;
	}
	.footer-grid ul li {
		display:inline-block;
		margin-right:0.6em;
	}
	.footer-grid h3 {
		margin-bottom: 0em;
	}
	.footer-grid ul{
		margin:0.5em 0;
	}
	.footer-grid p {
		padding: 0.5em 0;
	}
	.copy-right {
		margin:1em 0 1em 0;
	}
}
@media only screen and (max-width:480px) and (min-width:320px) {
	.wrap {
		width:80%;
	}
	.footer-grid {
		width:100%;
		float: none;
		margin-right:0%;
	}
	.footer-grid ul li {
		display:inline-block;
		margin-right:0.6em;
	}
	.footer-grid h3 {
		margin-bottom: 0em;
	}
	.footer-grid ul{
		margin:0.5em 0;
	}
	.footer-grid p {
		padding: 0.5em 0;
	}
	.copy-right {
		margin:1em 0 1em 0;
	}
}
@media only screen and (max-width:320px) and (min-width:240px) {
	.wrap {
		width:90%;
	}
	.footer-grid {
		width:100%;
		float: none;
		margin-right:0%;
	}
	.footer-grid ul li {
		display:inline-block;
		margin-right:0.6em;
	}
	.footer-grid h3 {
		margin-bottom: 0em;
	}
	.footer-grid ul{
		margin:0.5em 0;
	}
	.footer-grid p {
		padding: 0.5em 0;
	}
	.copy-right {
		margin:1em 0 1em 0;
	}
}

