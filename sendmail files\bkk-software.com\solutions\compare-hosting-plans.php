<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';

$picCnt = 0;

?>
<!DOCTYPE html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html lang="en" class="no-js"> <!--<![endif]-->
<head>
   <meta charset="utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta name="description" content="<?php echo $SITE_NAME;?> helps manage your IT functions including infrastructure setup, IT procurement and IT maintenance services.">
   <meta name="keywords" content="system deployment, infrastructure setup, system implementation, office IT setup,system setup, business IT setup,IT implementation">
   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">

   <title>Compare Hosting Plans</title>


<?php echo getPgCss(); ?>
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-main.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
      <h2>Compare Hosting Plans</h2>
         <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
            <p>
                <?php echo $SITE_NAME?> is the preferred choice for hosting personal and  business websites. Our fast, reliable servers and redundant networks provide the best performance and server uptime. Every web hosting plan comes with a 30 day money back guarantee and 99.9% uptime guarantee.
            </p>


  Compare our web hosting packages today and find the plan that best fits your need.
<p>&nbsp;</p>
<table width="650" cellspacing="1">
  <tbody>
  <tr valign="middle">
    <td colspan="5" height="25"><font color="#333333">»&nbsp;<strong>Shared Hosting Packages</strong></font></td></tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td width="7">&nbsp;</td>
    <td width="253">Plans</td>
    <td width="121"><strong>Standard</strong></td>
    <td width="125"><strong>Business</strong></td>
    <td width="126"><strong>Professional</strong></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Disk Space</td>
    <td>200mb<br></td>
    <td>500mb<br></td>
    <td>2,000mb<br></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Bandwidth</td>
    <td>2,500mb<br></td>
    <td>5,000mb</td>
    <td>20,000mb</td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Monthly Price</td>
    <td>150 baht</td>
    <td>350 baht</td>
    <td>1000 baht</td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
  </tr>
  <tr valign="middle"><td colspan="5" height="25"><font color="#333333">»&nbsp;<strong>You Can Create...</strong></font></td></tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Domains</td>
    <td>2</td>
    <td>unlimited</td>
    <td>unlimited</td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Sub Domains</td>
    <td>2</td>
    <td>unlimited</td>
    <td>unlimited</td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>POP3 Accounts</td>
    <td>10</td>
    <td>unlimited</td>
    <td>unlimited</td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>MySQL Databases</td>
    <td>10</td>
    <td>unlimited</td>
    <td>unlimited</td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>FTP Accounts</td>
    <td>10</td>
    <td>unlimited</td>
    <td>unlimited</td>
  </tr>
  <tr valign="middle"><td colspan="5" height="25"><font color="#333333">»&nbsp;<strong>All accounts you setup will have.....</strong></font></td></tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>CGI</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>PHP 5 with Zend&nbsp; Optimizer</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>MySQL 4+</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Perl</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Ruby On Rails</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Python</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>SSH</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>SSI</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Cron</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>FrontPage</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Curl</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>GD</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Image Magick</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Streaming Audio/Video</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Free PHP/Perl Module Installation</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr valign="middle"><td colspan="5" height="25"><font color="#333333">»&nbsp;<strong>Optional extra services...</strong></font></td></tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Site Builder*</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Chillisoft ASP*</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr valign="middle"><td colspan="5" height="25"><font color="#333333">»&nbsp;<strong>All accounts will have the below E-mail tools....</strong></font></td></tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Web Mail</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>E-mail Alias</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Auto Responders</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Mailing Lists</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Catch Alls</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Spam Assassin</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Mail Forwards</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>IMAP Support</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>SMTP</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr valign="middle"><td colspan="5" height="25"><font color="#333333">»&nbsp;<strong>Control Panel Features...</strong></font></td></tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Fantastico</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Soholaunch</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Multi Language</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Hotlink Protection</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>phpMyAdmin</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>IP Deny Manager</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Custom Error Pages</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Redirect URL</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Web Based File Manager</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>PW Protected Directories</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Instant Blogs</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Instant Customer Support Desks</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Instant PHPnuke</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Instant Forums</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Instant Guestbook</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Instant Portals</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Instant Counter</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Instant FormMail</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr valign="middle"><td colspan="5" height="25"><font color="#333333">»&nbsp;<strong>All accounts will have the below Website / FTP Statistics...</strong></font></td></tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>AWstats (Real Time Updates)</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Webalizer</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Raw Log Manager</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Referrer Logs</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Error Logs</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr valign="middle"><td colspan="5" height="25"><font color="#333333">»&nbsp;<strong>All accounts will have the below E-Commerce&nbsp; Features...</strong></font></td></tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Shared SSL</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Private SSL*</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Agora Cart</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>osCommerce</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>ZenCart</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Cube Cart</td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
    <td><img src="<?php echo IMG_URL?>icons/checked.gif" width="11" height="9" alt="Yes"></td>
  </tr>
  <tr valign="middle"><td colspan="5" height="25"><font color="#333333">»&nbsp;<strong>Purchase</strong></font></td></tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Plans</td>
    <td><strong>Elite</strong></td>
    <td><strong>Rage</strong></td>
    <td><strong>Extreme</strong></td>
  </tr>
  <tr onMouseOver="this.className='highlight'" onMouseOut="this.className='normal'">
    <td>&nbsp;</td>
    <td>Monthly Price (baht)</td>
    <td>150</td>
    <td>350</td>
    <td>1,000</td>
    </tr>
</tbody></table>

            </div>

            <!--Header_section-->
               <?php include('menu-solutions.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->

   

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->


<?php echo getPgFootJs(); ?>
</body>
</html>