<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';

$picCnt = 0;

?>
<!DOCTYPE html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html lang="en" class="no-js"> <!--<![endif]-->
<head>
   <meta charset="utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta name="description" content="<?php echo $CO_NAME;?> Online forms for client">
   <meta name="keywords" content="Client online forms, Online forms">
   <meta name="techspace" content="techspace.co.th">

   <title><?php echo $CO_NAME;?> Online Forms</title>


<?php echo getPgCss(); ?>


<script language="JavaScript" type="text/javascript">
function chkForm(frm){
  if(frm.name.value == ""){
    alert("Please enter your name name.");
    frm.name.focus();
    return false;
  } else if(frm.company.value == ""){
    alert("Please enter your company's name.");
    frm.company.focus();
    return false;
  }else if(!validEmail(frm.email)){
    alert("Please enter a valid Email address.");
    frm.email.focus();
    return false;
  }else if(frm.topic.value == ""){
    alert("Please select a topic.");
    frm.topic.focus();
    return false;
  }else if(frm.subject.value == ""){
    alert("Please enter a subject.");
    frm.subject.focus();
    return false;
  }else if(frm.message.value == ""){
    alert("Please enter your message.");
    frm.message.focus();
    return false; 
  }else{return true;}
}
</script>

</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-main.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
            <h2>Client Online Forms</h2>
            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >


      <li><a href="<?php echo SF_URL?>forms/fm_pc_installation.php" target="_blank">New PC Installation Form</a></li>
      <li><a href="<?php echo SF_URL?>forms/fm_pc_reinstallation.php" target="_blank">PC Re-installation Form</a></li>


            </div>




            <!--Header_section-->
               <?php include('menu-coy.php');?>
            <!--Header_section--> 

      </section>
      <!-- container end here -->



      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->

<?php echo getPgFootJs(); ?>
</body>
</html>