<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';

$picCnt = 0;

?>
<!DOCTYPE html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html lang="en" class="no-js"> <!--<![endif]-->
<head>
   <meta charset="utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta name="description" content="Helpdesk Support จัดเตรียมบริการให้คำปรึกษาแก้ไขปัญหาคอมพิวเตอร์ผ่านทางโทรศัพท์และรีโมทอย่างครบวงจร ไม่ว่าจะเป็นปัญหาทางด้าน Hardware, Software, Network">
   <meta name="keywords" content="help desk support,Desktop support services,แก้ปัญหาระบบคอมพิวเตอร์,บริหารจัดการ server,help desk">
   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">

   <title>บริการ IT Helpdesk Support</title>


<?php echo getPgCss(); ?>
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-main.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
            <h2>บริการ IT Helpdesk Support</h2>
            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
              <p>
                  Support ช่วยคุณลดค่าใช้จ่ายและเพิ่มความคล่องตัวในการบริหารจัดการงานไอทีภายในองค์กรเมื่อคุณเกิดปัญหาด้านไอที บริการ IT Helpdesk Support จาก IT Thailand จะให้บริการช่วยเหลือคุณทันที ด้วยเจ้าหน้าที่ไอทีที่มีประสบการณ์
              </p>

              <p>
                  ด้วยรูปแบบการให้บริการที่ยืนหยุ่น โดยเราปรับการให้บริการให้ตรงกับความต้องการของลูกค้า และงบประมาณของลูกค้า แต่ยังคงไว้ซึ่งประสิทธิภาพและความรวดเร็วในการให้บริการ
              </p>


              <h3>เต็มเปี่ยมด้วยประสิทธิภาพ ในราคาที่คุณสามารถเข้าถึงได้</h3>
              <p>
                IT Helpdesk Support จัดเตรียมบริการให้คำปรึกษาแก้ไขปัญหาคอมพิวเตอร์ผ่านทางโทรศัพท์และรีโมทอย่างครบวงจร ไม่ว่าจะเป็นปัญหาทางด้าน Hardware, Software, Network หรือปัญหาใดก็ตามเกี่ยวกับคอมพิวเตอร์ทีมงาน IT Helpdesk ของเราคอยช่วยเหลือคุณตลอดเวลา
              </p>


              <p>
                  สิ่งที่เรามอบให้ลูกค้า:
              </p>
               <ul class="me-list list-circle-check">
                 <li>ให้บริการแก้ปัญหาด้วยความรวดเร็ว</li>
                 <li>เพิ่มประสิทธิภาพและความพอใจในการใช้ระบบคอมพิวเตอร์</li>
                 <li>ลดค่าใช้จ่ายในการจ้างพนักงานประจำ</li>
                 <li>บริหารจัดการและแก้ปัญหาระบบ Server และ Computer</li>
                 <li>ปรับปรุงประสิทธิภาพการทำงานของระบบเน็ตเวิร์คและระบบ Security</li>
                 <li>เพิ่มความคล่องตัวและเพิ่มประสิทธิภาพในการทำงานของอุปกรณ์คอมพิวเตอร์</li>
               </ul>


              <p>
                ด้วยบริการจาก IT Thailand คุณจะได้รับการดูแลด้วยบริการที่ด้วยเร็วและเจ้าหน้าที่ไอทีที่เต็มเปี่ยมด้วยความสามารถ 
                สอบถามข้อมูลเพิ่มเติม 02 381-9075 หรือ <a href="<?php echo LANG_URL?>coy/contact-us.htm">Contact Us</a>
              </p>

            </div>

            <!--Header_section-->
               <?php include('menu-services.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->



      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->


<?php echo getPgFootJs(); ?>
</body>
</html>