      <header id="me-header" class="header-version2">
          <div class="row">
              <div class="large-12 column">
                  <!-- logo container -->
                  <div class="logo-container">
                      <a href="<?php echo LANG_URL;?>">
                          <img class="retina" src="<?php echo IMG_URL;?>logo.png" style="margin-top: 5px;" alt="Logo">
                      </a>
                  </div>
                  <!-- logo container end here -->

                  <!-- menu navigation -->
                  <div class="navigation-container">
                      <div class="form-search-trigger">



                          <?php   //===2. lang option icon
            $filename = $_SERVER['REQUEST_URI'];
            if($lang=='th'){    //thai
                $filename = preg_replace("/\/$lang\//", '', $filename);
                $langUs = '<a href="'.SF_URL. $filename.'" title="Eng"  ><img src="'. IMG_URL . 'icons/us.gif" title="Eng" /></a>'; 
                $langTh = '<img src="'. IMG_URL . 'icons/th.gif" title="Thai" />';  
            } else {
                $realFilename = preg_replace('/\.htm/','_th.php',$filename);
            //  $realFilename = preg_replace('/^\//','',$realFilename);
    //echo SF_PATH. $realFilename;
                if(file_exists(SF_PATH . $realFilename)){
                    $filename = preg_replace('/\.php$/','.htm',$filename);  //incase this url ends with .php, we convert the link back to .htm forma            
                    $langTh = '<a href="'.SF_URL. 'th' . $filename.'" title="Thai" ><img src="'. IMG_URL . 'icons/th.gif" /></a>';
                } else {
                    $langTh = '';   
                }
                $langUs = '<img src="'. IMG_URL . 'icons/us.gif" title="Eng" />';   
            } 

echo "$langUs $langTh";        
?>





                      </div>


                      <nav class="menu-container">
                          <ul id="menu" class="sm me-menu">

                              <li><a href="javascript:void();">Software</a>
                                  <ul>
                                      <li><a href="<?php echo LANG_URL;?>software/financial.htm">Financial
                                              Management</a></li>
                                      <li><a href="<?php echo LANG_URL;?>software/operations.htm">Operations</a></li>
                                      <li><a href="<?php echo LANG_URL;?>software/crm.htm">CRM</a></li>
                                      <li><a href="<?php echo LANG_URL;?>software/service-manager.htm">Service
                                              Manager</a></li>
                                  </ul>
                              </li>
                              <li><a href="javascript:void();">IT Solutions</a>
                                  <ul>
                                      <li><a href="<?php echo LANG_URL;?>solutions/system-deployment.htm">System
                                              Deployment</a></li>
                                      <li><a href="<?php echo LANG_URL;?>solutions/backup-solutions.htm">Backup
                                              Solutions</a></li>
                                      <li><a href="<?php echo LANG_URL;?>solutions/web-hosting.htm">Web Hosting</a></li>
                                  </ul>
                              </li>
                              <li><a href="javascript:void();">IT Services</a>
                                  <ul>
                                      <li><a href="<?php echo LANG_URL;?>services/it-consulting.htm">IT Consulting</a>
                                      </li>
                                      <li><a href="<?php echo LANG_URL;?>services/it-support.htm">IT Maintenance</a>
                                      </li>
                                      <li><a href="<?php echo LANG_URL;?>services/helpdesk.htm">Helpdesk Support</a>
                                      </li>
                                      <li><a href="<?php echo LANG_URL;?>services/troubleshooting.htm">PC
                                              Troubleshooting</a></li>
                                      <li><a href="<?php echo LANG_URL;?>services/it-procurement.htm">IT Procurement</a>
                                      </li>
                                  </ul>
                              </li>
                              <li><a href="javascript:void();">Resources</a>
                                  <ul>
                                      <li><a href="<?php echo SF_URL;?>blog" target="_blank">Blog</a></li>
                                      <li><a href="http://client.techspace.co.th">Knowledgebase</a></li>
                                      <li><a href="<?php echo LANG_URL;?>support/online-forms.htm">Online Forms</a></li>
                                      <li><a href="http://client.techspace.co.th">Client Login</a></li>
                                  </ul>
                              </li>
                              <li><a href="javascript:void();">About Us</a>
                                  <ul>
                                      <li><a href="<?php echo LANG_URL;?>coy/about-us.htm">About Us</a></li>
                                      <li><a href="<?php echo LANG_URL;?>coy/career.htm">Career Opportunities</a></li>
                                      <li><a href="<?php echo LANG_URL;?>coy/contact-us.htm">Contact Us</a></li>
                                  </ul>
                              </li>

                          </ul>
                      </nav>
                      <!-- menu navigation end here -->
                  </div>
              </div>
          </div>
      </header>