# Email Authentication Setup Guide for BKK-IT.com

## Overview
This guide explains how to set up proper email authentication to improve email deliverability and reduce spam issues.

## Current Issues Fixed

### 1. Email Headers
- **Before**: Used customer's email as "From" address (causes SPF failures)
- **After**: Uses `<EMAIL>` as "From" with customer email in "Reply-To"

### 2. Authentication Headers Added
- Message-ID for tracking
- Return-Path for bounce handling
- Organization header
- Auto-Response-Suppress headers
- RFC 3834 compliant auto-reply headers

## DNS Records to Add

### SPF Record
Add this TXT record to your DNS:
```
v=spf1 a mx include:_spf.google.com ~all
```

### DKIM Setup
Contact your hosting provider (A2 Hosting) to set up DKIM signing for bkk-it.com

### DMARC Policy
Add this TXT record for _dmarc.bkk-it.com:
```
v=DMARC1; p=quarantine; rua=mailto:<EMAIL>; ruf=mailto:<EMAIL>; fo=1
```

## Email Addresses to Create

1. `<EMAIL>` - Main sending address
2. `<EMAIL>` - Bounce handling
3. `<EMAIL>` - DMARC reports

## Configuration Options

### Basic Configuration (Current)
- Uses PHP mail() function
- Enhanced headers for better deliverability
- Proper From/Reply-To separation

### SMTP Configuration (Optional)
To enable SMTP, edit `sendmail.php`:
```php
'use_phpmailer' => true,
'smtp_host' => 'mail.bkk-it.com', // Your SMTP server
'smtp_port' => 587,
'smtp_secure' => 'tls',
'smtp_username' => '<EMAIL>',
'smtp_password' => 'your_password_here'
```

## Testing

### Test Recipients
1. **@techspace.co.th** - Should now receive emails in inbox
2. **@techspace-it.com** - Should continue working
3. **Gmail addresses** - Should go to inbox instead of spam

### Monitoring
Check these log files:
- `coy/email_log.txt` - Email sending attempts
- `coy/email_errors.log` - PHP errors

## Expected Improvements

1. **Microsoft Mail (@techspace.co.th)**: Should accept emails now
2. **Gmail Spam**: Should be reduced significantly
3. **A2 Server Delivery**: Should improve reliability
4. **Overall Reputation**: Better sender reputation over time

## Next Steps

1. Add DNS records (SPF, DKIM, DMARC)
2. Create required email addresses
3. Test with different recipient domains
4. Monitor delivery rates
5. Consider SMTP upgrade if needed

## Troubleshooting

If emails still go to spam:
1. Check DNS records are properly set
2. Verify email addresses exist
3. Monitor email logs
4. Consider warming up the new sender reputation
5. Test with email authentication tools

## Support

For technical support with DNS or email setup, contact:
- A2 Hosting support for DKIM setup
- Domain registrar for DNS record changes
- Email authentication testing tools online
