<!doctype html>
<html lang="en">
  <head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8">
    <title></title>
    <link href="forms.css" rel="stylesheet" type="text/css">
    <style type="text/css" media="screen">
body {
	font: .75em/1.5 "Lucida Grande", "Lucida Sans Unicode", helvetica, verdana, arial, sans-serif;
  	margin: 0 auto;
  	padding: 0 10px;
  	width: 610px;
}
strong {
  background: #ffc;
}
	.style1 {font-size: large}
    </style>
</head>
	<body>
    <p>&nbsp;</p>
    <table width="790" border="0" cellspacing="0" cellpadding="0">
      <tr>
        <td height="66"><img src="tslogo.gif" width="573" height="111"></td>
      </tr>
    </table>
    <h1 class="style1">New PC Installation Form.</h1>
    <form id="newpc" name="newpc" method="post" action="newpc_check.php">
      <p>Please complete this form, We will require 6 hours to complete the installation.</p>
      <fieldset>
        <legend>Company Installation</legend>
      <div>
     	<table width="537" border="0">
          <tr>
            <td width="107">Company Name </td>
            <td width="11"> : </td>
            <td width="414"><input type="text" name="com_name"></td>
          </tr>
          <tr>
            <td>Request Name</td>
            <td> : </td>
            <td><input type="text" name="request"></td>
          </tr>
          <tr>
            <td>Request Contact</td>
            <td> : </td>
            <td><input type="text" name="contact"></td>
          </tr>
          <tr>
            <td>Request E-mail </td>
            <td>: </td>
            <td><input type="text" name="email"></td>
          </tr>
        </table>
      </div>
      </fieldset>
      <fieldset>
        <legend>Installation Details</legend>
      <div class="inline">
        <table width="485" border="0">
          <tr>
              <td width="145">Operation System : </td>
            <td width="150"><select id="post[os]" name="os">
                <option value="">Please Select...</option>
				<option value="Windows 8.1 64 bit">Windows 8.1 64 bit</option>
				<option value="Windows 8.1 Pro 64 bit">Windows 8.1 Pro 64 bit</option>
				<option value="Windows 8 64 bit">Windows 8 64 bit</option>
				<option value="Windows 8 Pro 64 bit">Windows 8 Pro 64 bit</option>
				<option value="Windows 7 Home Basic 64 Bit">Windows 7 Home Basic 64 Bit</option>
       			<option value="Windows 7 Home Premium 64 Bit">Windows 7 Home Premium 64 Bit</option>
        		<option value="Windows 7 Professional 64 Bit">Windows 7 Professional 64 Bit</option>
				<option value="Windows 7 Ultimate 64 Bit">Windows 7 Ultimate 64 Bit</option>
				<option value="Windows 8.1 32 bit">Windows 8.1 32 bit</option>
				<option value="Windows 8.1 Pro 32 bit">Windows 8.1 Pro 32 bit</option>
				<option value="Windows 8 32 bit">Windows 8 32 bit</option>
				<option value="Windows 8 Pro 32 bit">Windows 8 Pro 32 bit</option>
				<option value="Windows 7 Home Basic 32 Bit">Windows 7 Home Basic 32 Bit</option>
       			<option value="Windows 7 Home Premium 32 Bit">Windows 7 Home Premium 32 Bit</option>
        		<option value="Windows 7 Professional 32 Bit">Windows 7 Professional 32 Bit</option>
				<option value="Windows 7 Ultimate 32 Bit">Windows 7 Ultimate 32 Bit</option>
				<option value="Windows XP Professional">Windows XP Professional</option>
       		</select></td>
          </tr>
          <tr>
            <td>Microsoft Office : </td>
            <td width="150"><select id="post[office]" name="office">
            <option value="">Please Select...</option>
			<option value="Professional 2013">Professional 2013</option>
			<option value="Home and Business 2013">Home and Business 2013</option>
			<option value="Professional Plus 2013">Professional Plus 2013</option>
			<option value="Professional 2010">Professional 2010</option>
			<option value="Home and Business 2010">Home and Business 2010</option>
			<option value="Professional 2007">Professional 2007</option>
			<option value="Small Business 2007">Small Business 2007</option>
    		<option value="Professional Plus 2007">Professional Plus 2007</option>
            </select></td>
          </tr>
          <tr>
            <td>Default Language:</td>
            <td width="320"><select id="post[lang]" name="lang">
			  <option value="English">English</option>
			  <option value="Thai">Thai</option>
            </select></td>
          </tr>
          <tr>
            <td>Application :  </td>
            <td colspan="3"><input id="post[teamviewer]" name="app1" type="checkbox" value="Teamviewer">
			<label for="post[teamviewer]">Teamviewer</label>
			<input id="post[skype]" name="app2" type="checkbox" value="Skype">
			<label for="post[skype]">Skype</label>
			<input id="post[line]" name="app3" type="checkbox" value="Line">
			<label for="post[line]">Line</label></td>
		  </tr>
          <tr>
            <td>&nbsp;</td>
            <td colspan="3"><input id="post[firefox]" name="app4" type="checkbox" value="Firefox">
			<label for="post[firefox]">Mozilla Firefox</label>
			<input id="post[chrome]" name="app5" type="checkbox" value="Chrome">
			<label for="post[chrome]">Google Chrome</label></td>
          </tr>
          <tr>
            <td>&nbsp;</td>
            <td colspan="3"><input id="post[winrar]" name="app6" type="checkbox" value="WinRAR">
                <label for="post[winrar]">Winrar</label>
                <input id="post[adobe]" name="app7" type="checkbox" value="Adobe Reader">
                <label for="post[adobe]">Adobe Reader (PDF Viewer) </label></td>
          </tr>
        </table>
      </div>
      <div>
          <label for="post_content">Additional Note </label>
          <p>Enter additional requirement or preferences (E.g. other application or e-mail configuration)
            <textarea id="post[content]" name="content" maxlength="250"></textarea>
          </p>
      </div>
      </fieldset>
      <p>Thank you for your submission. We will contact you when the installation is completed. </p>
      <p>
        <input type="submit" value="Submit">
        or
        <a href="#">Cancel</a>
      </p>
  </form>
</body>
</html>