<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?></title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="<?php echo isset($description) ? $description : 'Global IT Partner - Leading provider of enterprise IT solutions, managed services, network infrastructure, and technology consulting across Singapore, Thailand, and USA.'; ?>">
    <meta name="keywords" content="<?php echo isset($keywords) ? $keywords : 'IT services, enterprise solutions, network infrastructure, managed services, technology consulting, IT support, Singapore, Thailand, USA'; ?>">
    <meta name="author" content="Global IT Partner">
    <meta name="robots" content="index, follow">

    <!-- Open Graph Meta Tags for Social Media -->
    <meta property="og:title" content="<?php echo $title; ?>">
    <meta property="og:description" content="<?php echo isset($description) ? $description : 'Global IT Partner - Leading provider of enterprise IT solutions, managed services, network infrastructure, and technology consulting.'; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http'; ?>://<?php echo $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:image" content="<?php echo $base_url; ?>assets/images/logo/globalitpartner-logo.png">
    <meta property="og:site_name" content="Global IT Partner">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $title; ?>">
    <meta name="twitter:description" content="<?php echo isset($description) ? $description : 'Global IT Partner - Leading provider of enterprise IT solutions, managed services, network infrastructure, and technology consulting.'; ?>">
    <meta name="twitter:image" content="<?php echo $base_url; ?>assets/images/logo/globalitpartner-logo.png">

    <!-- Canonical URL -->
    <link rel="canonical" href="<?php echo isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http'; ?>://<?php echo $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    <?php
    // Determine the base URL based on the server host
    $is_localhost = (isset($_SERVER['HTTP_HOST']) && $_SERVER['HTTP_HOST'] === 'localhost');
    $base_url = $is_localhost ? 'http://localhost/globalitpartner.com/' : 'https://www.globalitpartner.com/';
    ?>
    <!-- Favicon img -->
    <link rel="shortcut icon" href="<?php echo $base_url; ?>assets/images/logo/globalitpartner-favicon.png">
    <!-- Bootstarp min css -->
    <link rel="stylesheet" href="<?php echo $base_url; ?>assets/css/bootstrap.min.css">
    <!-- Mean menu css -->
    <link rel="stylesheet" href="<?php echo $base_url; ?>assets/css/meanmenu.css">
    <!-- All min css -->
    <link rel="stylesheet" href="<?php echo $base_url; ?>assets/css/all.min.css">
    <!-- Swiper bundle min css -->
    <link rel="stylesheet" href="<?php echo $base_url; ?>assets/css/swiper-bundle.min.css">
    <!-- Magnigic popup css -->
    <link rel="stylesheet" href="<?php echo $base_url; ?>assets/css/magnific-popup.css">
    <!-- Animate css -->
    <link rel="stylesheet" href="<?php echo $base_url; ?>assets/css/animate.css">
    <!-- Nice select css -->
    <link rel="stylesheet" href="<?php echo $base_url; ?>assets/css/nice-select.css">
    <!-- Style css -->
    <link rel="stylesheet" href="<?php echo $base_url; ?>assets/css/style.css?v=1.1">
    <!-- Custom css -->
    <link rel="stylesheet" href="<?php echo $base_url; ?>assets/css/custom.css">
    <!-- Footer alignment fix -->
    <link rel="stylesheet" href="<?php echo $base_url; ?>assets/css/footer-fix.css">
    <!-- Industry grid styles -->
    <link rel="stylesheet" href="<?php echo $base_url; ?>assets/css/industry-grid.css">
    <!-- Industry optimized styles -->
    <link rel="stylesheet" href="<?php echo $base_url; ?>assets/css/industry-optimized.css">
    <!-- Mission Vision styles -->
    <link rel="stylesheet" href="<?php echo $base_url; ?>assets/css/mission-vision.css">
    <!-- iOS-specific fixes -->
    <link rel="stylesheet" href="<?php echo $base_url; ?>assets/css/ios-fixes.css">
    <?php echo (isset($css) ? $css   : '') ?>

    <!-- Pace.js iOS fix - must load before any other scripts -->
    <!-- <script src="<?php echo $base_url; ?>assets/js/pace-ios-fix.js"></script> -->

    <!-- iOS preloader fix - load early to ensure it works -->
    <!-- <script src="<?php echo $base_url; ?>assets/js/ios-preloader-fix.js"></script> -->
</head>