<?php

#########################################################################
#	category.php														#
#-----------------------------------------------------------------------#
#	Author:		 	<PERSON>e <PERSON><PERSON> Liaw										#
#	Version:	 	7.06.1												#
#	Last Edited:	01 Oct 06											#
#	Remarks:															#
#																		#
#########################################################################


//------prepare page CSS ----
function getPgCss(){
global $lang;

	if($lang=='th'){
	//	$str = '<style type="text/css" media="all">@import "' . CSS_URL . 'default_th.css";</style>';
	} else {
	//	$str = '<style type="text/css" media="all">@import "' . CSS_URL . 'default.css";</style>';
	}

	$str .= '

	  <!-- retina Bookmark Icon -->
	   <link rel="apple-touch-icon-precomposed" href="'. IMG_URL .'apple-icon.png" />

	   <!-- CSS -->
	   <link href="'. CSS_URL .'foundstrap.css" rel="stylesheet" />
	   <link href="'. CSS_URL .'font-awesome.min.css" rel="stylesheet" />

	   <!--[if (lt IE 9) & (!IEMobile)]>
	      <script src="https://cdnjs.cloudflare.com/ajax/libs/respond.js/1.4.2/respond.min.js"></script>
	   <![endif]-->
	  
	   <!-- CSS Plugin -->   
	   <link href="'. JSLIB_URL .'rs-plugin/css/settings.css" rel="stylesheet" media="screen">

	   <!-- theme Stylesheet -->


      <link href="'. CSS_URL .'animate.min.css" rel="stylesheet">
      <link href="'. CSS_URL .'linea-icon.min.css" rel="stylesheet">
      <link href="'. CSS_URL .'smartmenu.min.css" rel="stylesheet">
      <link href="'. CSS_URL .'owl-carousel.min.css" rel="stylesheet">
      <link href="'. CSS_URL .'fancybox.css" rel="stylesheet">
      <link href="'. CSS_URL .'element.css" rel="stylesheet">


	   <link href="'. CSS_URL .'style.css" rel="stylesheet">
	   <link href="'. CSS_URL .'theme-responsive.css" rel="stylesheet">   
	   
	   <!-- favicon -->
	   <link rel="shortcut icon" href="'. IMG_URL .'favicon.ico?v=2">

	   <!-- modernizr -->
	   <script src="'. JSLIB_URL .'modernizr.js"></script>

	';
	return $str;
}



//------prepare page CSS ----
function getPgFootJs(){
global $lang;



	$str .= '

   <!-- javascript -->
   <script src="'. JSLIB_URL .'jquery.min.js"></script>
   <script src="'. JSLIB_URL .'foundstrap.js"></script>
   <script src="'. JSLIB_URL .'jquery.sscr.js"></script>
   <script src="'. JSLIB_URL .'jquery.waypoints.min.js"></script>
   <script src="'. JSLIB_URL .'owl.carousel.min.js"></script>
   <script src="'. JSLIB_URL .'jquery.scrollUp.js"></script>
   <script src="'. JSLIB_URL .'jquery.retina.js"></script>

   <!-- javascript plugin - popup images  -->
   <script src="'. JSLIB_URL .'jquery.fancybox.js"></script>
   <script src="'. JSLIB_URL .'jquery.fancybox-media.js"></script>
   <script src="'. JSLIB_URL .'jquery.isotope.js"></script>


   <!-- javascript plugin -->
   <script src="'. JSLIB_URL .'rs-plugin/js/jquery.themepunch.tools.min.js"></script>
   <script src="'. JSLIB_URL .'rs-plugin/js/jquery.themepunch.revolution.min.js"></script>

   <!-- javascript core -->
   <script src="'. JSLIB_URL .'theme-script.js"></script>
   <script src="'. JSLIB_URL .'jquery.cookie.js"></script>
   
   <script type="text/javascript">
      jQuery(document).ready(function($) { 
         Foundstrap.theme();

         // revolution slider configuration here
         $(\'.slideshow\').revolution({
            delay:8000,
            startwidth:1080,
            startheight:520,
            hideThumbs:1,
            navigationType:"none",                  // bullet, thumb, none
            navigationArrows:"solo",                // nexttobullets, solo (old name verticalcentered), none
            navigationStyle:"square",               // round,square,navbar,round-old,square-old,navbar-old, or any from the list in the docu (choose between 50+ different item), custom
            navigationHAlign:"center",              // Vertical Align top,center,bottom
            navigationVAlign:"bottom",              // Horizontal Align left,center,right
            navigationHOffset:0,
            navigationVOffset:0,
            soloArrowLeftHalign:"left",
            soloArrowLeftValign:"center",
            soloArrowLeftHOffset:25,
            soloArrowLeftVOffset:0,
            soloArrowRightHalign:"right",
            soloArrowRightValign:"center",
            soloArrowRightHOffset:25,
            soloArrowRightVOffset:0,
            touchenabled:"on",                      // Enable Swipe Function : on/off
            onHoverStop:"on",                       // Stop Banner Timet at Hover on Slide on/off
            stopAtSlide:-1,                         // Stop Timer if Slide "x" has been Reached. If stopAfterLoops set to 0, then it stops already in the first Loop at slide X which defined. -1 means do not stop at any slide. stopAfterLoops has no sinn in this case.
            stopAfterLoops:-1,                      // Stop Timer if All slides has been played "x" times. IT will stop at THe slide which is defined via stopAtSlide:x, if set to -1 slide never stop automatic
            hideCaptionAtLimit:0,                   // It Defines if a caption should be shown under a Screen Resolution ( Basod on The Width of Browser)
            hideAllCaptionAtLilmit:0,               // Hide all The Captions if Width of Browser is less then this value
            hideSliderAtLimit:0,                    // Hide the whole slider, and stop also functions if Width of Browser is less than this value
            shadow:0,                               // 0 = no Shadow, 1,2,3 = 3 Different Art of Shadows  (No Shadow in Fullwidth Version !)
            fullWidth:"off",                        // Turns On or Off the Fullwidth Image Centering in FullWidth Modus
            fullScreen:"off"
         });
      });
   </script>

	';
	return $str;
}

?>