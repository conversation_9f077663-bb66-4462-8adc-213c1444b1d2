<?php
//======== TIME ==============
$TIME_UOM_DAY = array (
	"1" => "Minutes",
	"2" => "Hours",
	"3" => "Days"
);

$TIME_UOM_YEAR = array (
	"1" => "Minutes",
	"2" => "Hours",
	"3" => "Days",
	"4" => "Years"
);

$DATETIME_MONTH_SHORT = array (
	"1" => "Jan",
	"2" => "Feb",
	"3" => "Mar",
	"4" => "Apr",
	"5" => "May",
	"6" => "Jun",
	"7" => "Jul",
	"8" => "Aug",
	"9" => "Sep",
	"10" => "Oct",
	"11" => "Nov",
	"12" => "Dec"
);


$DATETIME_WEEKDAY = array (
	"1" => "Monday",
	"2" => "Tuesday",
	"3" => "Wednesday",
	"4" => "Thursday",
	"5" => "Friday",
	"6" => "Saturday",
	"7" => "Sunday"
);

$DATETIME_HOUR = array (
	"00", "01", "02", "03", "04", "05", "06", "07", "08", "09",
	"10", "11", "12", "13", "14", "15", "16", "17", "18", "19",
	"20", "21", "22", "23"
);

//$DATETIME_MIN = array ("00", "05", "10", "15", "20", "25", "30", "35", "40", "45", "50", "55");
$DATETIME_MIN = array (
	'00', '01', '02', '03', '04', '05', '06', '07', '08', '09',
	'10', '11', '12', '13', '14', '15', '16', '17', '18', '19',
	'20', '21', '22', '23', '24', '25', '26', '27', '28', '29',
	'30', '31', '32', '33', '34', '35', '36', '37', '38', '39',
	'40', '41', '42', '43', '44', '45', '46', '47', '48', '49',
	'50', '51', '52', '53', '54', '55', '56', '57', '58', '59'
);


//======== DATE - Day ==============
function validDate($date){
  $date = strtr($date,'/','-');
  $datearr = explode('-', $date);
  if(count($datearr) == 3){ //count that there are 3 elements in the array
	  list($y, $m, $d) = $datearr;
	  /*checkdate - check whether the date is valid. strtotime - Parse about any English textual datetime description into a Unix timestamp. Thus, it restricts any input before 1901 and after 2038, i.e., it invalidate outrange dates like 01-01-2500. preg_match - match the pattern*/
	  if(checkdate($m, $d, $y) && strtotime("$y-$m-$d") && preg_match('#\b\d{2}[/-]\d{2}[/-]\d{4}\b#', "$d-$m-$y")){return true;} else {return false;}
  }
return false;
}
//------ return local current date 
function getLocalDate($format='Y-m-d'){
global $DATETIME_HOUR_OFFSET;
	$servertime = date('U'); //gets PST Unix timestamp
	$local_timestamp = $servertime + $DATETIME_HOUR_OFFSET*3600; //add offset in second (60*60)
	return date($format, $local_timestamp); //format for MySQL DATETIME field
}
//--------
function formatDate($date, $format='Y-m-d'){
/*
	sample format:	d-m-Y, d/m/Y, d.m.Y
					d-m-y, d/m/y, d.m.y
					Y-m-d, Y/m/d, Y.m.d
*/
	return date($format, strtotime($date));
}
//------ return local current datetime 
function getLocalDatetime(){
global $myDb;
	$datetimeOffset = getDatetimeOffset();
	$sql = "SELECT ADDTIME(NOW(), '$datetimeOffset')";
	$res = $myDb->query($sql);
	$rec = $myDb->fetchArray($res);
	return $rec[0];
}
//----------- add or subtract year to a date (2011-02-12)
function addYears($date, $numYears){
	list($yr, $mth, $day) = explode('-', $date);
	$d = mktime(0,0,0,$mth,$day,$yr);
	if($numYears >= 0){
		$endDate = date("Y-m-d",strtotime("+$numYears year ", $d));
	} else {
		$endDate = date("Y-m-d",strtotime("$numYears year ", $d));
	}
return $endDate;
}
//----------- add or subtract month to a date (2011-02-12)
function addMonths($date, $numMonths){
	list($yr, $mth, $day) = explode('-', $date);
	$d = mktime(0,0,0,$mth,$day,$yr);
	if($numMonths >= 0){
		$endDate = date("Y-m-d",strtotime("+$numMonths month ", $d));
	} else {
		$endDate = date("Y-m-d",strtotime("$numMonths month ", $d));
	}
return $endDate;
}
//----------- add or subtract days to a date (2011-02-12)
function addDays($date, $numDays){
	list($yr, $mth, $day) = explode('-', $date);
	$d = mktime(0,0,0,$mth,$day,$yr);
	if($numDays >= 0){
		$endDate = date("Y-m-d",strtotime("+$numDays day", $d));
	} else {
		$endDate = date("Y-m-d",strtotime("$numDays day", $d));
	}
return $endDate;
}
//------------add day,hrs,min to a datime (2011-02-12 18:30:00)
function addDatetime($datetime, $addDay, $addHrs, $addMin, $option='add'){
	list($date, $time) = explode(' ', $datetime);
	list($yr, $mth, $day) = explode('-', $date);
	list($hrs, $min, $sec) = explode(':', $time);

//echo "$datetime, $addDay, $addHrs, $addMin<br>";

	$d = mktime($hrs,$min,$sec,$mth,$day,$yr);
	if($option=='add'){
//echo "action==add";
		$endDate = date("Y-m-d h:i:s",strtotime("+$addDay days $addHrs hours $addMin seconds", $d));
	} else {
		$endDate = date("Y-m-d h:i:s",strtotime("-$addDay days $addHrs hours $addMin seconds", $d));
	}
return $endDate;
}

//---------
function getDatetimeDifference($start, $end, $format='general'){
global $DATETIME_HOUR_OFFSET;

if($end=='NOW'){
	$servertime = date('U'); //gets PST Unix timestamp
	$est_timestamp = $servertime + $DATETIME_HOUR_OFFSET*3600; //add offset in second
	$end = date('Y-m-d H:i:s', $est_timestamp); //format for MySQL DATETIME field
} else if ($end=='0000-00-00 00:00:00'){return '0';}

//echo "start: $start, ed: $end , offset: $DATETIME_HOUR_OFFSET<br>";
$uts['start'] = strtotime( $start );
$uts['end']   = strtotime( $end );
if( $uts['start']!==-1 && $uts['end']!==-1 ){
	if( $uts['end'] > $uts['start'] ){
		$diff = $uts['end'] - $uts['start'];
		if($format=='general'){
			$result = '';
			$skipDetails = false;
			if( $years	=intval((floor($diff/31536000))) ){$diff = $diff % 31536000;}
			if( $days	=intval((floor($diff/86400))) ){$diff = $diff % 86400;}
			if( $hours	=intval((floor($diff/3600))) ){$diff = $diff % 3600;}
			if( $minutes=intval(floor($diff/60)) ){$diff = $diff % 60;} else { $minutes=1;} //minimum time interval is 1min
			//$seconds    =    intval( $diff );
			if($years>0){
				$result = "$years yr ";
				$skipDetails = true;
			}
			if($days>0){$result .= "$days day ";}
			if(!$skipDetails){
				if($hours>0){$result .= "$hours hr ";}
				if($minutes>0){$result .= "$minutes min ";}
			}
			return $result;
		} else if($format=='specific'){	//show actual: 88days 21hr 34min
			$result = '';
			if( $years	=intval((floor($diff/31536000))) ){$diff = $diff % 31536000;}
			if( $days	=intval((floor($diff/86400))) ){$diff = $diff % 86400;}
			if( $hours	=intval((floor($diff/3600))) ){$diff = $diff % 3600;}
			if( $minutes=intval(floor($diff/60)) ){$diff = $diff % 60;} else { $minutes=1;} //minimum time interval is 1min
			//$seconds    =    intval( $diff );
			if($years>0){$result = "$years yr ";}
			if($days>0){$result .= "$days day ";}
			if($hours>0){$result .= "$hours hr ";}
			if($minutes>0){$result .= "$minutes min ";}
			return $result;
		} else if($format=='hours'){
			if( $hours=intval((floor($diff/3600))) ){ return $hours;} else {return 1;} //1 hr
		} else if($format=='minutes'){
			if( $minutes=intval((floor($diff/60))) ){ return $minutes;} else {return 1;} //1 mins
		} else if($format=='seconds'){
			if( $seconds=intval($diff) ){ return $seconds;} else {return 1;} //1 sec
		}
				
	} else {return 0;}
}
return 'unknown';
}
//-----------------
function getDatetimeOffset(){
global $DATETIME_HOUR_OFFSET;
	return "0 $DATETIME_HOUR_OFFSET:0:0";
}

//--------------
/* NOT IN USE
function workDays($strTime = NULL){
//This is a little function that returns only a working day (monday until friday). It's flexible due to the use of a 'forbidden days' array.
//I used this in a project where the delivery date can only be 10 days from now and only on a working day (mo - fr). 
    $arrWeekend = array("6","7"); // saturday and sunday
    $strTime = (is_null($strTime)) ? strtotime("+10 days") : $strTime;
    if(in_array(date("N", $strTime), $arrWeekend)){
        return wdays(strtotime("+1 day", $strTime));
    }
    else {
        return $strTime;
    }
}
*/
//echo date("d-m-Y",wdays()); 
//--------------
/* NOT IN USE
function week_day($month, $year, $current_day, $week_day_number=1){
		//If u want to get weekday date -
		//30.08.2009 weekdays are 24-30
		//i want to get 5-th day of that week - 28.08.2009
    $loop_start = $current_day-(date('N', mktime(0, 0, 0, $month, $current_day, $year))-1);//lets start loop from first day of week
    $loop_end = $current_day+(7-(date('N', mktime(0, 0, 0, $month, $current_day, $year))));//lets end loop to last day of week
    for($i = $loop_start; $i<=$loop_end; $i++){
        $day_of_the_week = date('N', mktime(0, 0, 0, $month, $i, $year));//current day number 1-7 of week
        $loop_date = date('d', mktime(0, 0, 0, $month, $i, $year));//current day in calendar
        if($day_of_the_week == $week_day_number){//if weekday number equals day number then lets return date
            return $loop_date;
        }
    }
}
*/
/*
echo week_day(8, 2009, 30, 1);//returns (24).08.2009
echo '<br>';
echo week_day(8, 2009, 30, 5);//returns (28).08.2009
*/
//--------------
function getLastDayOfMonth($year, $month){
	return date('d',strtotime('-1 second',strtotime('+1 month',strtotime($month . '/01/'.$year.' 00:00:00'))));	
}
//--------------
function getCurWeekMondayDate($date){	//return last monday date of tje same week
global $DATETIME_WEEKDAY;
	$dayName	= date("l", strtotime($date));	//e.g. Wednesday
	$day		= current(array_keys($DATETIME_WEEKDAY, $dayName)); //eg. 3
	$dayOffset	= 1-$day;
	return addDays($date, $dayOffset);
}
//--------------
function findFirstDayOfWeek($month, $year, $day, $offset){ // supply the month, year, day and offset
/*
		echo findFirstDayofWeek(6,2009, "Wednesday", 2);
		where 6 is "June", 2009 is the year, Wednesday is the weekday we want and 2 is the 2nd occurence. 
		echo findFirstDayofWeek(9,2009, "Friday", 5); // returns a formatted date for the first instance of a certain day in the argumental month 
*/
    $firstDay = mktime(0, 0, 0, $month, 1, $year); // Get the first day of the month in question
    $curStamp = $firstDay; // set a disposable variable for use in the while loop
    $result = 0; //set the number of results (for use with the offset argument)
    while($result != $offset){ //while the number of results does not equal the offset we are looking for
        $curStamp = $curStamp + 86400; // add a day
        if(date("l", $curStamp) == $day){ // if the name of the weekday we are currently looping at is the same name as the argument supplied, set the date variable to that weekday and increment the results variable by 1
            $Date = date("Y-m-d", $curStamp);
            $result++;
        }
    }
    if(date("n", $curStamp) != $month){ // this line checks whether or not the date that the while loop has found is in the same month we are asking form otherwise, there must be no "3rd friday in august"
    return "No weekday at this offset";
    } else {
    return $Date; //send back the date
    }
}

//-----------------
/* NOT IN USE
function getMondays($year) {
		//Here's a function that takes the year as input and returns an array or dates that are mondays. (It can be used for generating weekly reports just like I did)
		
  $newyear = $year;
  $week = 0;
  $day = 0;
  $mo = 1;
  $mondays = array();
  $i = 1;
  while ($week != 1) {
    $day++;
    $week = date("w", mktime(0, 0, 0, $mo,$day, $year));
  }
  array_push($mondays,date("r", mktime(0, 0, 0, $mo,$day, $year)));
  while ($newyear == $year) {
    $test =  strtotime(date("r", mktime(0, 0, 0, $mo,$day, $year)) . "+" . $i . " week");
    $i++;
    if ($year == date("Y",$test)) {
      array_push($mondays,date("r", $test));
    }
    $newyear = date("Y",$test);
  }
  return $mondays;
} 
*/
//----------------
// NOT IN USE
/*
function next_7_days() {
        // create array of day names. You can change these to whatever you want
    $days = array('Monday','Tuesday','Wednesday','Thursday','Friday','Saturday','Sunday');
    $today = date('N');
    for ($i=1;$i<$today;$i++) {
                // take the first element off the array
        $shift = array_shift($days);

                // ... and add it to the end of the array
        array_push($days,$shift);
    }
        // returns the sorted array
    return $days;
}
*/
//----------------
/* NOT IN USE
function week_of_year($month, $day, $year) {
		//I suppose we run an off week in which week one is considered to start on the Monday of the week that contains Jan 1.  All the date functions seemed to be incorrect for this week calculation so here's what I came up with to give me the week number we needed.  Ideally we aren't the only people in the world that uses that calculation:
		//So:
		//week_of_year("12", "27", "2009") is "52"
		//week_of_year("12", "28", "2009") is "1"
		//week_of_year("1", "4", "2010") is "2"
		
		//Id assume if you needed to start on another weekday you could modify it to look for that day instead of Mon. 

    //Get date supplied Timestamp;
    $thisdate = mktime(0,0,0,$month,$day,$year);
    //If the 1st day of year is a monday then Day 1 is Jan 1
    if (date("D", mktime(0,0,0,1,1,$year)) == "Mon"){
        $day1=mktime (0,0,0,1,1,$year);
    } else {
        //If date supplied is in last 4 days of last year then find the monday before Jan 1 of next year
        if (date("z", mktime(0,0,0,$month,$day,$year)) >= "361"){
            $day1=strtotime("last Monday", mktime(0,0,0,1,1,$year+1));
        } else {
            $day1=strtotime("last Monday", mktime(0,0,0,1,1,$year));
        }
    }
    // Calcualte how many days have passed since Day 1
    $dayspassed=(($thisdate - $day1)/60/60/24);
    //If Day is Sunday then count that day other wise look for the next sunday
    if (date("D", mktime(0,0,0,$month,$day,$year))=="Sun"){
        $sunday = mktime(0,0,0,$month,$day,$year);
    } else {
        $sunday = strtotime("next Sunday", mktime(0,0,0,$month,$day,$year));   
    }
    // Calculate how many more days until Sunday from date supplied
    $daysleft = (($sunday - $thisdate)/60/60/24);
    // Add how many days have passed since figured Day 1
    // plus how many days are left in the week until Sunday
    // plus 1 for today
    // and divide by 7 to get what week number it is
    $thisweek = ($dayspassed + $daysleft+1)/7;
    return $thisweek;
} 
*/

?>