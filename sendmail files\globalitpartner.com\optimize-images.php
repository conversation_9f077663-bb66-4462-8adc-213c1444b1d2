<?php
/**
 * Image Optimization Script for Global IT Partner
 * 
 * This script creates optimized versions of the large images in the Industries Served folder.
 * It reduces file size while maintaining reasonable quality, which improves page load times.
 */

// Set maximum execution time to 5 minutes to handle large images
ini_set('max_execution_time', 300);
ini_set('memory_limit', '512M');

// Configuration
$source_dir = 'assets/images/industries-served/';
$output_dir = 'assets/images/industries-served/optimized/';
$max_width = 800; // Maximum width for optimized images
$quality = 80;    // JPEG quality (0-100)

// Create output directory if it doesn't exist
if (!file_exists($output_dir)) {
    mkdir($output_dir, 0755, true);
    echo "Created output directory: $output_dir<br>";
}

// Get all image files from source directory
$files = glob($source_dir . '*.jpg');
$total_files = count($files);
$processed = 0;
$total_original_size = 0;
$total_optimized_size = 0;

echo "<h1>Image Optimization for Global IT Partner</h1>";
echo "<p>Processing $total_files images from $source_dir</p>";
echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
echo "<tr><th>Image</th><th>Original Size</th><th>Optimized Size</th><th>Reduction</th><th>Status</th></tr>";

foreach ($files as $file) {
    $filename = basename($file);
    $output_file = $output_dir . $filename;
    
    // Get original file size
    $original_size = filesize($file);
    $total_original_size += $original_size;
    
    // Skip if optimized file exists and is newer than source
    if (file_exists($output_file) && filemtime($output_file) > filemtime($file)) {
        $optimized_size = filesize($output_file);
        $total_optimized_size += $optimized_size;
        $reduction = round(100 - ($optimized_size / $original_size * 100), 2);
        
        echo "<tr>";
        echo "<td>$filename</td>";
        echo "<td>" . formatSize($original_size) . "</td>";
        echo "<td>" . formatSize($optimized_size) . "</td>";
        echo "<td>$reduction%</td>";
        echo "<td>Skipped (already optimized)</td>";
        echo "</tr>";
        
        $processed++;
        continue;
    }
    
    // Load the image
    $image = imagecreatefromjpeg($file);
    if (!$image) {
        echo "<tr>";
        echo "<td>$filename</td>";
        echo "<td>" . formatSize($original_size) . "</td>";
        echo "<td>-</td>";
        echo "<td>-</td>";
        echo "<td>Error: Could not load image</td>";
        echo "</tr>";
        continue;
    }
    
    // Get image dimensions
    $width = imagesx($image);
    $height = imagesy($image);
    
    // Calculate new dimensions while maintaining aspect ratio
    if ($width > $max_width) {
        $new_width = $max_width;
        $new_height = floor($height * ($max_width / $width));
    } else {
        $new_width = $width;
        $new_height = $height;
    }
    
    // Create a new image with the new dimensions
    $new_image = imagecreatetruecolor($new_width, $new_height);
    
    // Copy and resize the old image to the new image
    imagecopyresampled($new_image, $image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
    
    // Save the new image
    imagejpeg($new_image, $output_file, $quality);
    
    // Free up memory
    imagedestroy($image);
    imagedestroy($new_image);
    
    // Get optimized file size
    $optimized_size = filesize($output_file);
    $total_optimized_size += $optimized_size;
    $reduction = round(100 - ($optimized_size / $original_size * 100), 2);
    
    echo "<tr>";
    echo "<td>$filename</td>";
    echo "<td>" . formatSize($original_size) . "</td>";
    echo "<td>" . formatSize($optimized_size) . "</td>";
    echo "<td>$reduction%</td>";
    echo "<td>Optimized</td>";
    echo "</tr>";
    
    $processed++;
}

// Display summary
$total_reduction = round(100 - ($total_optimized_size / $total_original_size * 100), 2);
echo "<tr style='font-weight: bold;'>";
echo "<td>Total ($processed files)</td>";
echo "<td>" . formatSize($total_original_size) . "</td>";
echo "<td>" . formatSize($total_optimized_size) . "</td>";
echo "<td>$total_reduction%</td>";
echo "<td>Complete</td>";
echo "</tr>";
echo "</table>";

echo "<h2>Next Steps</h2>";
echo "<p>1. The optimized images are now available in the <code>$output_dir</code> directory.</p>";
echo "<p>2. To use these optimized images, update your image paths in index.php to point to the optimized directory.</p>";
echo "<p>3. Example: <code>src=\"assets/images/industries-served/optimized/aerospace.jpg\"</code></p>";

/**
 * Format file size in human-readable format
 */
function formatSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $i = 0;
    while ($bytes > 1024 && $i < count($units) - 1) {
        $bytes /= 1024;
        $i++;
    }
    return round($bytes, 2) . ' ' . $units[$i];
}
