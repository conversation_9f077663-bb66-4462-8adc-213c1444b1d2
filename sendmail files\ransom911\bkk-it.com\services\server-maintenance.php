<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB0_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library


$picCnt = 0;

?>
<!DOCTYPE html>

<html>
<head>
   <meta charset="utf-8">
   <meta http-equiv="X-UA-Compatible" content="IE=edge"> 
   <meta name="description" content="Fast, reliable IT maintenance Support including helpdesk support, remote and onsite support.">
   <meta name="keywords" content="IT Support,IT Maintenance Support,PC maintenance services,IT outsource,computer maintenance,server maintenance,notebook maintenance">
   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">

   <title>Server Maintenance and Support Services | Thailand</title>


<?php echo getPgCss(); ?>

</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-services-it-support_en.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
            <h1>Server Maintenance and Support</h1>
            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
                <h3 style="font-size: 110%; color: #666; margin-bottom: 12px;">
                  Extend the life of your server equipment with proactive maintenance services

                </h3>
                  <p>
                    <?php echo $SITE_NAME;?> provides a comprehensive server maintenance and support services to ensures that your servers continue to operate at peak performance - even after manufacturers' warranty has expired.
           
                    At <?php echo $SITE_NAME;?>, we proactively monitor your server equipment  to identify and resolve problems long before they result in a system outage.

                  </p>

                <p>


               <h3 style="font-size: 110%; color: #666; margin-top: 30px;">Mitigate risks associated with server downtime</h3>
               <p>

                <?php echo $SITE_NAME;?>'s goal is to maximize server life and uptime, keeping your business running smoothly without interruption. 
                We perform updates, maintenance and other checklist items on a routine basis on all of your servers to keep your servers up-to-date and protected against threats and outages.
             


               </p>
                <h3 style="font-size: 110%; color: #666; margin-top: 30px;">The Key Benefits</h3>
               <ul class="me-list list-circle-check">
                  <li>Proactive monitoring of hardware and services</li>
                  <li>Software Updates and Patches</li>
                  <li>Security Reviews & Hardening</li>
                  <li>File Systems Maintenance</li>
                  <li>Backup Checks</li>        
               </ul>



              <p><a href="<?php echo LANG_URL?>coy/contact-us.html">Contact us</a> today to find out how <?php echo $SITE_NAME;?> can help you discover additional value from your server hardware with our server maintenance and support services.

            </div>

            <!--Header_section-->
               <?php include('menu-services.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->

      <?php echo getPgFootJs(); ?>

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->

<!-- Google Code for adword conversion Conversion Page -->
<script type="text/javascript">
/* <![CDATA[ */
var google_conversion_id = 859884778;
var google_conversion_language = "en";
var google_conversion_format = "3";
var google_conversion_color = "ffffff";
var google_conversion_label = "WU3ZCOaf5W4Q6pmDmgM";
var google_remarketing_only = false;
/* ]]> */
</script>
<script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js">
</script>
<noscript>
<div style="display:inline;">
<img height="1" width="1" style="border-style:none;" alt="" src="//www.googleadservices.com/pagead/conversion/859884778/?label=WU3ZCOaf5W4Q6pmDmgM&amp;guid=ON&amp;script=0"/>
</div>
</noscript>

</body>
</html>