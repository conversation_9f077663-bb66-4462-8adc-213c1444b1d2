/* Industry Grid Layout Styles */

.industry-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  padding: 0 15px;
  max-width: 1200px;
  margin: 0 auto;
}

.industry-grid-row-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  padding: 0 15px;
  max-width: 800px;
  margin: 15px auto 0;
}

.industry-item {
  position: relative;
  overflow: hidden;
  height: 180px;
  border-radius: 5px;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.industry-item:hover {
  transform: translateY(-5px);
}

.industry-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.industry-item:hover img {
  transform: scale(1.1);
}

.industry-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(15, 13, 29, 0.6);
  z-index: 1;
  transition: background 0.3s ease;
}

.industry-item:hover::before {
  background: rgba(0, 0, 0, 0.7);
}

.industry-title {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  color: var(--white);
  text-align: center;
  padding: 15px;
  font-weight: 600;
  font-size: 18px;
  z-index: 2;
  text-transform: uppercase;
}

/* Responsive styles - Improved for better tablet and mobile experience */
/* Large tablets */
@media (max-width: 1199px) {
  .industry-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .industry-grid-row-2 {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .industry-item {
    height: 160px;
  }
}

/* Medium tablets */
@media (max-width: 991px) {
  .industry-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .industry-grid-row-2 {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .industry-item {
    height: 150px;
  }

  .industry-title {
    font-size: 16px;
    padding: 12px 10px;
  }
}

/* Small tablets and large phones */
@media (max-width: 767px) {
  .industry-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .industry-grid-row-2 {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .industry-item {
    height: 140px;
  }

  .industry-title {
    font-size: 14px;
    padding: 10px 8px;
  }
}

/* Small phones */
@media (max-width: 575px) {
  .industry-grid {
    grid-template-columns: repeat(1, 1fr);
    gap: 10px;
  }

  .industry-grid-row-2 {
    grid-template-columns: repeat(1, 1fr);
    gap: 10px;
  }

  .industry-item {
    height: 160px;
  }

  .industry-title {
    font-size: 16px;
    padding: 10px;
  }
}
