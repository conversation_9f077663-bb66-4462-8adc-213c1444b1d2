<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';

$picCnt = 0;

?>
<!DOCTYPE html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html lang="en" class="no-js"> <!--<![endif]-->
<head>
   <meta charset="utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta name="description" content="<?php echo $CO_NAME;?> committed to building a workforce that respect individual skills and diversity while valuing team contrbution.">
   <meta name="keywords" content="IT jobs, software developer, php developer job, cloud administrator job, IT Career Opportunities, jobs at <?php echo $CO_NAME;?>,<?php echo $CO_NAME;?> company profile,<?php echo $CO_NAME;?> official website">
   <meta name="techspace" content="techspace.co.th">

   <title>Career Opportunities at <?php echo $CO_NAME;?></title>


<?php echo getPgCss(); ?>

<style>
  .jobDetails {
     font-size: 85%;;
     color: #666;
  }
</style>

<script type="text/javascript">
function toggleDisplay(obj){
   if(document.getElementById(obj).style.display == 'none') {document.getElementById(obj).style.display = '';
   } else {document.getElementById(obj).style.display = 'none';}
}
</script>


</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-main.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
            <h2>Career@<?php echo $CO_NAME;?></h2>
            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
               <p><?php echo $CO_NAME?> is a home to a team of young talented individuals who share a common interest - technology. We are committed to building a  workforce that respect individual skills and diversity while valuing team contrbution. We take great pride in providing the best technology and services to our customers. </p>
    
               <p>Our innovative culture means everyone is encouraged to have creative ideas; to express them and to share them. We want to invest in ideas to help change lives and improve working practices.</p>
         
               <p>We are searching for creative and talented individuals to join our team. <?php echo $CO_NAME?> offers its employees limitless opportunity for development and growth! We strive to create the best working culture to inspire and drive individuals while having fun in the process.</p>
     
               <p><a href="mailto:<?php echo $EMAIL_JOBS?>">Email us</a> to apply for your ideal job!</p>


<h2>Current Job Openings </h2>
<?
$row = 1;
?>
<table width="100%" style="border: 1px solid #ccc">
<tr>
<td width="5%"><strong><?php echo $row?>.</strong></td>
<td width="38%"><strong><a href="javascript:void(0)" onclick="toggleDisplay('row<?php echo $row?>');">Senior PHP Developer</a></strong></td>
<td width="34%"><strong>2 Positions</strong></td>
<td width="23%"> Posted on 08 Jan 2016</td>
</tr>
<tr id="row<?php echo $row++?>" style="display:none"  class="jobDetails"><td colspan="4" >
  <p><?php echo $CO_NAME?> Development Team is seeking a <strong>Senior PHP Developer</strong> to join our team!&nbsp; If you are an excellent developer with  strong  programming skills, you may be the person we are  looking for.</p>
  <p><strong>SKILLS &amp; REQUIREMENTS:</strong></p>
    <ul type="disc">
      <li>
        <p><strong>Skills</strong></p>
        <ul>
          <li>Bachelors degree and 3 years work experience</li>
          <li>Strong PHP (Laravel / MVC framework)</li>
          <li>MySQL</li>
          <li>HTML / JS (JQuery) / CSS</li>
        </ul>
        <p><strong>Skills Nice to have</strong></p>
        <ul>
          <li>PostgreSQL (Extra points for this skill)</li>
          <li>Git </li>
          <li>HTML Boilerplate, LESS, Grunt, Bower, Minimise.js</li>
          <li>Amazon AWS (Opswork, EC2, RDS, SES, SQS, Route53)</li>
          <li>Ubuntu / Linux</li>
          <li>Unit testing</li>
        </ul>
        <p><strong>Role &amp; Responsibilities</strong></p>
        <ul>
          <li>Manage the project build and lead team</li>
          <li>Developer new and improve existing functionality</li>
          <li>Maintain code quality</li>
          <li>Maintain system stability and backups </li>
        </ul>
      </li>
  </ul></td></tr>
<tr>
<td width="5%"><strong><?php echo $row?>.</strong></td>
<td width="38%"><strong><a href="javascript:void(0)" onclick="toggleDisplay('row<?php echo $row?>');"> Systems Engineer</a></strong></td>
<td width="34%"><strong>1 Position</strong></td>
<td width="23%"> Posted on 05 Jan 2016</td>
</tr>
<tr id="row<?php echo $row++?>" style="display:none" class="jobDetails">
  <td colspan="4" ><p><?php echo $CO_NAME?> is looking for a system administrator to design, implement and   administer our web infrastructure on AWS. This is a new initiative and   you will be in charge to drive innovation on a number of different   technologies critical to building solutions for our cloud infrastructure   to support our customers and e-commerce portal.</p>
<p>You are responsible for system engineering, planning, design, and   implementation of Cloud infrastructure, Cloud systems, and bridging   on-premise VMware virtualization environments to the cloud. Responsible   for configuration, maintenance, and operations of information systems   and storage environments on Cloud. The system engineer will also design,   architect, and troubleshoot systems and applications to identify and   correct performance and other operational issues. Work and maintain   information security policies, security standards, and system   engineering guidelines and procedures.</p>
<p>You will get the opportunity to own major deliverable, implement   automated tasks, and manage key infrastructure systems to support our   business. This position may include, requirements analysis, systems   integration &amp; server configuration with a focus on automation, high   availability, and remote managed services. This position requires an   extremely dynamic, enthusiastic, and creative individual who enjoys   working in a collaborative environment for the support of critical   systems. In order to be successful in this position, you must have a   strong desire to enhance, lead, test &amp; automate the services you   provide.</p>
<p>Please apply if you are capable of delivering exceptional work for   our organization and are interested in joining a dynamic team.</p>
<p><strong>Responsibilities</strong></p>
<ul>
  <li>Lead the design and management of our Cloud Infrastructure</li>
  <li>Implement highly available systems using VMware and AWS</li>
  <li>Help ensure security and compliance across systems and network</li>
</ul>
<p> </p>
<p><strong>SKILLS &amp; REQUIREMENTS:</strong></p>
<ul>
  <li>Linux systems administration (CentOS, RedHat)</li>
  <li>Experience in implementing highly available systems using VMware and AWS</li>
  <li>A demonstrated understanding of Linux systems including kernel, network, disk, CPU &amp; memory</li>
  <li>Ability to solve problems with automation of repetitive tasks utilizing common scripting languages</li>
  <li>Scripting &amp; programming languages such as shell, Perl, Ruby &amp; Python</li>
  <li>Source control management (Subversion, Git, etc.)</li>
  <li>Strength in virtualization of systems and networks</li>
  <li>Cloud solutions like Open Stack, AWS or other comparable software</li>
  <li>Goal oriented, forward thinker that can provide solutions for complex technical problems</li>
  <li>Data center operations experience is a plus</li>
  <li>Bachelor&rsquo;s Degree in Computer Science, Math, or similar field</li>
</ul>
    <p>&nbsp;</p></td></tr>

<?php
/*
<tr>
<td width="5%"><strong><?php echo $row?>.</strong></td>
<td width="38%"><strong><a href="javascript:void(0)" onclick="toggleDisplay('row<?php echo $row?>');">IT Support Specialist</a></strong></td>
<td width="34%"><strong>1 Position</strong></td>
<td width="23%"> Posted on 06 March 2015</td>
</tr>
<tr id="row<?php echo $row++?>" style="display:none"  class="jobDetails">
  <td colspan="4" ><p><?php echo $CO_NAME?> Support Team  is seeking an experienced and competent IT Support Specialist <strong></strong> to join our team. If you are a person with  strong IT knowledge and problem solving skills, you may be the person we are  looking for.</p>
<p><br />
  <strong>JOB SUMMARY:</strong></p>
    <ul type="disc">
      <li>Provide       on-site IT support and maintenance services to our customers.</li>
      <li>Proposed       solutions to help improve customer IT infrastructure, systems/networks       performance.</li>
      <li>Responsible       for the logging and fixing of customer IT problems in an effective and       timely manner.</li>
      <li>Technical       lead for projects involving server, PCs, firewall and VPN installations. </li>
      <li>Assist       SI Team in implementing large scale server and network infrastructure.</li>
      <li>Plan,       prepare proposal and document to customer.</li>
    </ul>
<p>&nbsp;</p>
    <p><strong>SKILLS &amp; REQUIREMENTS:</strong></p>
    <ul type="disc">
            <li>Strong       IT knowledge and experience in Windows server 2003, 2008, Windows 7, XP,       VLAN, VPN, firewall and network setup.</li>
            <li>Good       troubleshooting and problem solving skills. Able to work fast and response       quickly to the customer</li>
            <li>Experience       in Unix/Linux is a plus.</li>
            <li>Good       command English  </li>
            <li>Have       strong service mind with excellent communication and presentation skills.</li>
            <li>Good       relationship and ability to work under pressure.</li>
    </ul></td></tr>



<tr>
<td width="5%"><strong><?php echo $row?>.</strong></td>
<td width="38%"><strong><a href="javascript:void(0)" onclick="toggleDisplay('row<?php echo $row?>');">Channel Development Executive</a></strong></td>
<td width="34%"><strong>2 Positions</strong></td>
<td width="23%"> Posted on 06 March 2015</td>
</tr>
<tr id="row<?php echo $row++?>" style="display:none"  class="jobDetails"><td colspan="4" ><p><?php echo $CO_NAME?> is seeking an experienced   <strong>Channel Development Executive </strong>to join our team!&nbsp;</p>
  <p><br /></p>
  <p data-mce-style="background: none repeat scroll 0% 0% white;"><strong><span data-mce-style="text-decoration: underline;"><span data-mce-style="font-size: 10pt;"><span data-mce-style="padding: 0cm; font-family: 'Arial','sans-serif'; border: 1pt none windowtext;">Duties and Responsibilities:</span></span></span></strong></p>
  <ul>
    <li><span data-mce-style="font-size: 10pt; font-family: 'Arial','sans-serif';"><span data-mce-style="font-size: 10pt; font-family: 'Arial','sans-serif';">Create brand awareness and grow revenue</span></span></li>
    <li><span data-mce-style="font-size: 10pt; font-family: 'Arial','sans-serif';">Work to prospect, identify, recruit and manage new channel partners. <br />
    </span></li>
    <li><span data-mce-style="font-size: 10pt; font-family: 'Arial','sans-serif';">Develop and implement monthly, quarterly and annual plans to ensure partner growth</span></li>
    <li><span data-mce-style="font-size: 10pt; font-family: 'Arial','sans-serif';">Communicate and report sales engagement status on a weekly basis </span></li>
    <li><span data-mce-style="font-size: 10pt; font-family: 'Arial','sans-serif';">Self driven, results oriented, and possess a positive outlook with clear focus of sales and profitability.</span></li>
    <li><span data-mce-style="font-size: 10pt; font-family: 'Arial','sans-serif';">Natural   forward planner who critically assesses his or her own performance in   the context of the successes or challenges of their distributor   customers<br data-mce-style="font-size: 10pt; font-family: 'Arial','sans-serif';" />
    </span></li>
    <li><span data-mce-style="font-size: 10pt; font-family: 'Arial','sans-serif';">Conduct 1 to many partner communications &amp; training</span></li>
  </ul>
  <p data-mce-style="background: none repeat scroll 0% 0% white;"><strong><span data-mce-style="text-decoration: underline;"><span data-mce-style="font-size: 10pt;"><span data-mce-style="padding: 0cm; font-family: 'Arial','sans-serif'; border: 1pt none windowtext;">Desired Skills and Experiences:</span></span></span></strong></p>
  <ul>
    <li><span data-mce-style="font-family: arial,helvetica,sans-serif; font-size: 10pt; color: #333333;">Worked in a partnership or channel sales role in other software or technology companies (2 to 5 years minimum)</span></li>
    <li><span data-mce-style="font-family: arial,helvetica,sans-serif; font-size: 10pt; color: #333333;">Successful track record in recruiting and managing software resellers in Thailand</span></li>
    <li><span data-mce-style="font-size: 10pt; font-family: 'Arial','sans-serif';"><span data-mce-style="color: #333333;">Strong channel background and experience in building a</span>nd leveraging channel partners</span></li>
    <li><span data-mce-style="font-size: 10pt; font-family: 'Arial','sans-serif';">Strong organizational skills with the ability to multi-task and set priorities</span></li>
    <li><span data-mce-style="font-size: 10pt; font-family: 'Arial','sans-serif'; background: none repeat scroll 0% 0% white;"><span data-mce-style="font-size: 10pt; font-family: 'Arial','sans-serif';">Bachelor's degree or equivalent preferred, MBA a plus</span></span></li>
    <li><span data-mce-style="font-size: 10pt; font-family: 'Arial','sans-serif'; background: none repeat scroll 0% 0% white;">Fluent in Thai and English, both speaking and writing</span></li>
  </ul></td></tr>

/*/
?>

</table>
            </div>

            <!--Header_section-->
               <?php include('menu-coy.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->



      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->

<?php echo getPgFootJs(); ?>
</body>
</html>