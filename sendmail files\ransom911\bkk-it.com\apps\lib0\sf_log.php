<?php


$LOG_DISPLAY_LEVEL = 9;	//logs display in mybe
$LOG_SHOW_LAST_X_MONTH = 6;
$LOG_LEVEL = 9;	

$LOG_EMAIL_ALERT_LEVEL = 3;	//sent email if level <= 3
//1 => CRITICAL
//3 => CRITICAL, ERROR
//4 => CRITICAL, ERROR, ALERT
//5 => CRITICAL, ERROR, ALERT, WARNING
//7 => CRITICAL, ERROR, ALERT, WARNING, INFO
//9 => CRITICAL, ERROR, ALERT, WARNING, INFO, LOG


class sysLog{
	var $logLevel;
	
function init(){
	$this->logLevel = array(
			'CRITICAL'	=> 1,
			'ERROR' 	=> 3,
			'ALERT'		=> 4,
			'WARNING'	=> 5,
			'INFO'		=> 7,
			'LOG'		=> 9
	);
}

function writeLog($level, $compKey, $logMsg){
global $myDb, $SM_COMPONENT, $SITE_DOMAIN_SHORT, $EMAIL_WEB_MASTER;
global $LOG_LEVEL, $LOG_EMAIL_ALERT_LEVEL;

	$myIp		= getIP();
	$myCountry	= getCcode();
	$coyName	= getSmCoyName();	
	if (isset($_SESSION['SF_UID'])){$uid = $_SESSION['SF_UID'];	} else {$uid = ''; }//user can be from SF, BE or mystore

	$levelId = $this->logLevel[$level];
	if($this->logLevel[$level] <= $LOG_LEVEL){
		$path			= $_SERVER['REQUEST_URI'];
		$datetimeOffset	= getDatetimeOffset();
		$compId			= $SM_COMPONENT[$compKey];
		$dataArr = array(
					's89_id'		=> '',
					's89_comp_id'	=> $compId,
					's89_level'		=> $levelId,
					's89_date'		=> "ADDTIME(NOW(), '$datetimeOffset')",
					's89_path'		=> $path,
					's89_uid'		=> $uid,
					's89_ip'		=> $myIp,
					's89_country'	=> $myCountry,
					's89_log'		=> $logMsg
		);
		if( ($result=$myDb->insertRowEsc('SMLOG', $dataArr))!=''){} else {}
	}//close if LOG_LEVEL
	
	if($this->logLevel[$level] <= $LOG_EMAIL_ALERT_LEVEL){
		$mailOpt['subject']	= "System Alert: [$level]";
		$recipientStr	= $EMAIL_WEB_MASTER;
		$xMsg 			= 'Company: ' .	$coyName . 
						'<br>Date: ' .	$date . 
						'<br>Time: ' .	$time .
						'<br>Status: '. $level . ", Class: " .$compKey. 
						'<br>Found in: ' . $_SERVER['REQUEST_URI'] . 
						'<p>Log Message:<br>' .$logMsg . ' [' . getSmUsrname($uid) . ']' .
						'<p>Please refer to system log for details.';
				
		if( ($result=smEmailSmLog($recipientStr, $mailOpt, $xMsg))!=''){
			//error while sending email	
		}


	}	//close if LOG_EMAIL_ALERT_LEVEL

	
	
}	//close fn writeLog



/********* Write transaction log to file ***/
/*
function writeTXLog2File($level,$memId,$fname,$lname,$countryb,$countryIP,$cusIP,$responseText,$cardNum,$cardExpire,$cardCvv2,$creditType,$orderNumber,$tel,$email){


$fileName=date("Y-m")."_transaction.html";
$pathDir = DATA_URL . "system/ccard/tx_log/";
$pathFile= $pathDir . $fileName;

if(!file_exists($pathFile)){  //first entry of the month
 $firstTime = "true";
}

if(!is_dir("$pathDir")){
	$mode = 0755;
	$result = create_folder($pathDir, $mode) . '<BR>';

}



$msg = "<font style='FONT-SIZE: 10px;font-family:arial,helvetica,san-serif;'>";
$fp=fopen($pathFile,"a");
if($firstTime == "true"){
$msg.= "<b>Order ID &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;| Date &nbsp;&nbsp; | &nbsp;&nbsp; Time &nbsp;&nbsp;&nbsp;&nbsp; | &nbsp;&nbsp;&nbsp; Customer Name (CID)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;| Billing Country | Detected Country + IP Addr | Authorize.net response &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; | 
Card Number-[cvv2] &nbsp;&nbsp;&nbsp;&nbsp; | Expiry(mm/yy) | &nbsp;cardType&nbsp; | Tel &nbsp;&nbsp;&nbsp; | &nbsp;&nbsp;&nbsp; Email &nbsp;&nbsp;&nbsp;</b><br>";
}

if($level == "ERROR"){
 $msg.="<font color=cc0000>";
} else {
 $msg.="<font color=339900>";
}
$msg.=$orderNumber."|".getLocalDate(). "|".date("h:i:sA")."|". $fname. ", " . $lname . " (". $memId.")|".$countryb."|".$countryIP.":".$cusIP."|".$responseText."|".$cardNum. "-".$cardCvv2. "|". $cardExpire . "|" . $creditType. "|" . $tel . "|". $email . "<br></font></font>";
fputs($fp,$msg);
fclose($fp);
 
}
*/

}//======= END class ==========

?>