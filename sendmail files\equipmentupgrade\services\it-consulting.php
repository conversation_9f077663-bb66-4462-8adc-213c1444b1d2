<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';

$picCnt = 0;

?>
<!DOCTYPE html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html lang="en" class="no-js"> <!--<![endif]-->
<head>
   <meta charset="utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta name="description" content="TechSpace consultants provide an objective, independent perspective to help companies optimize business processes and improve back-office operations through managed services, application development and IT Outsourcing.">
   <meta name="keywords" content="IT consulting, IT consult, business IT consulting, business automation consulting, application consulting">
   <meta name="techspace" content="techspace.co.th">

   <title>Effective and Affordable IT Consulting Services</title>


<?php echo getPgCss(); ?>
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-main.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
            <h2>Effective and Affordable IT Consulting Services</h2>
            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
               <p>
                  In today&rsquo;s advanced technology world, your company&rsquo;s ability to grow   and innovate depends on how effectively you leverage IT resources. At 
                    <?php echo $CO_NAME;?>, we help you think ahead and devise a executable plan to improve work efficiency and lower your TCO (Total Cost of Ownership).
               </p>

               <p>
                  Whether you need IT technical support, integrated IT solutions, an outsource IT Consultant, technology   strategy advice or assistance   in developing your customized software applications, we can tailor-make the right package for   your business at an affordable price.
               </p>
               <p>Our team provide hardware, systems  and network infrastructure design, implementation &amp; support, custom software development and assistance with procuring hardware and software   systems to support your business.  </p>


               <h3>
  <?php echo $CO_NAME;?> Consulting provides services through seven practices:
</h3>
               <ul class="me-list list-circle-check">
                 <li>Business Transformation</li>
                 <li>Process Excellence</li>
                 <li>Enterprise Architecture Consulting</li>
                 <li>Enterprise Resource Planning</li>
                 <li>Customer Relationship Management</li>
                 <li>Field Service Management</li>
                 <li>Governance, Risk and Compliance</li>
               </ul>


<p>To find out how we can work with you to make IT happen, <a href="<?php echo LANG_URL?>coy/contact-us.htm">contact us</a> or call us at <strong>02 381-9075</strong></p>

            </div>

            <!--Header_section-->
               <?php include('menu-services.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->



      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->


<?php echo getPgFootJs(); ?>
</body>
</html>