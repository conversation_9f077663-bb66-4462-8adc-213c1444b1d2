<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';

$picCnt = 0;

?>
<!DOCTYPE html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html lang="en" class="no-js"> <!--<![endif]-->
<head>
   <meta charset="utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta name="description" content="ปัญหาคอมพิวเตอร์สามารถเกิดขึ้นได้ตลอดเวลา  หากคุณไม่สามารถเข้าถึงแหล่งซ่อมหรือไม่ไว้วางใจว่าจ่ายเงินแล้วจะสามารถแก้ปัญหาได้หรือไม่ ติดต่อเรา 02-381-9075">
   <meta name="keywords" content="บริการรับซ่อมและแก้ปัญหาคอมพิวเตอร์, รับติดตั้ง Antivirus, รับติดตั้ง software, บริการ Backup Data, บริการกู้ข้อมูล server, บริการกู้ข้อมูลคอมพิวเตอร์, รับลงโปรแกรมคอมพิวเตอร์, บริการแก้ปัญหา Network">
   <meta name="<?php echo $SITE_NAME;?>" content="<?php echo $SITE_DOMAIN_SHORT;?>">

   <title>บริการรับซ่อมและแก้ปัญหาคอมพิวเตอร์ (Computer Troubleshooting)</title>


<?php echo getPgCss(); ?>
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-main.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
            <h2>บริการรับซ่อมและแก้ปัญหาคอมพิวเตอร์ (Computer Troubleshooting)</h2>
            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >

               <p><em>
                  ปัญหาคอมพิวเตอร์สามารถเกิดขึ้นได้ตลอดเวลา
               </em></p>

               <p>
                 หากคุณไม่สามารถเข้าถึงแหล่งซ่อมหรือไม่ไว้วางใจว่าจ่ายเงินแล้วจะสามารถแก้ปัญหาได้หรือไม่ <?php echo $SITE_NAME;?>
                 ข้าใจถึงความกังวลเหล่านี้ดี เราจึงเพิ่มเติมบริการรับซ่อมและแก้ปัญหาคอมพิวเตอร์ (Computer Troubleshooting) ด้วยไอทีผู้เชี่ยวชาญ ให้บริการทั้งในและนอกสถานที่


               </p>


               <h3>
                บริการซ่อมคอมและแก้ปัญหาคอมพิวเตอร์มีอะไรบ้าง:
               </h3>
               <ul class="me-list list-circle-check">
                 <li>รับติดตั้ง Antivirus Software และแก้ปัญหาไวรัส</li>
                 <li>บริการ Backup Data และกู้ข้อมูลทั้ง Server และ Computer</li>
                 <li>รับลงโปรแกรมคอมพิวเตอร์</li>
                 <li>ติดตั้งอีเมล์ และย้ายโฮส</li>
                 <li>แก้ปัญหาเครื่องคอม แฮงค์ ค้าง , มี Error ขึ้น , การทำงานช้าผิดปกติ</li>
                 <li>เครื่อง computer เสีย , เปิด-ปิดเครื่องไม่ได้ ไม่ติด</li>
                 <li>เครื่องคอมฯ Restart เอง , ปัญหาคอมติดไวรัส</li>
                 <li>โปรแกรมไม่สมบูรณ์ มีปัญหา ค้างบ่อย</li>
                 <li>Monitor ภาพไม่ขึ้น หรือ Blues screen</li>
                 <li>คอมพิวเตอร์เชื่อมต่ออินเตอร์เน็ตไม่ได้ , ต่อได้ แต่ Internet หลุดบ่อยๆ</li>
                 <li>ซ่อมประกอบคอมพิวเตอร์ PC รับประกอบเครื่องคอมพิวเตอร์ จัดสเปคคอมพิวเตอร์</li>
                 <li>บริการแก้ปัญหา Network</li>
                 <li>บริการอื่นๆ</li>
               </ul>


              <p>
                สอบถามข้อมูลเพิ่มเติม 02 381-9075 หรือ <a href="<?php echo LANG_URL?>coy/contact-us.htm">contact us</a> 
              </p>

            </div>

            <!--Header_section-->
               <?php include('menu-services.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->



      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->


<?php echo getPgFootJs(); ?>
</body>
</html>