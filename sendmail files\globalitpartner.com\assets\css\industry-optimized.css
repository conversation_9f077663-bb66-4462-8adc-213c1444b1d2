/**
 * Optimized styles for Industries Served section
 * These styles improve performance and responsiveness
 */

/* Optimize image loading and rendering */
.industry-grid img,
.industry-grid-row-2 img {
  /* Prevent layout shifts during loading */
  aspect-ratio: 3/2;
  object-fit: cover;

  /* Smooth transitions */
  transition: transform 0.3s ease;

  /* Prevent image overflow */
  max-width: 100%;

  /* Improve rendering performance */
  will-change: transform;

  /* Prevent blurry images during scaling */
  image-rendering: auto;

  /* Ensure images load properly */
  display: block;
}

/* Optimize container performance */
.industry-grid,
.industry-grid-row-2 {
  /* Use hardware acceleration */
  transform: translateZ(0);

  /* Improve rendering performance */
  will-change: opacity;

  /* Prevent unnecessary repaints */
  backface-visibility: hidden;

  /* Ensure proper display on all devices */
  contain: layout style paint;
}

/* Optimize hover effects for better performance */
.industry-item:hover img {
  /* Use transform instead of other properties for better performance */
  transform: scale(1.05);
}

/* Optimize title rendering */
.industry-title {
  /* Improve text rendering */
  text-rendering: optimizeSpeed;

  /* Prevent text from being selectable to improve performance */
  user-select: none;

  /* Ensure text is readable on all backgrounds */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Fix for Safari and iOS */
@supports (-webkit-touch-callout: none) {
  .industry-item {
    /* Fix for Safari overflow issues */
    -webkit-mask-image: -webkit-radial-gradient(white, black);
  }
}

/* Reduce animation complexity on low-power devices */
@media (prefers-reduced-motion: reduce) {
  .industry-item:hover img {
    transform: scale(1.02);
    transition-duration: 0.5s;
  }

  .industry-item {
    transition-duration: 0.5s;
  }
}

/* Media queries for responsive optimization */
@media (max-width: 1200px) {
  .industry-grid,
  .industry-grid-row-2 {
    /* Adjust container for better tablet display */
    width: 100%;
    max-width: 960px;
  }
}

@media (max-width: 992px) {
  /* Optimize for tablets */
  .industry-grid {
    /* Ensure proper spacing on tablets */
    gap: 10px;
    padding: 0 10px;
  }

  .industry-grid-row-2 {
    gap: 10px;
    padding: 0 10px;
  }

  /* Reduce animation complexity for better performance */
  .industry-item:hover {
    transform: translateY(-3px);
  }
}

@media (max-width: 768px) {
  /* Optimize for small tablets and large phones */
  .industry-grid,
  .industry-grid-row-2 {
    /* Ensure proper spacing */
    gap: 8px;
    padding: 0 8px;
  }

  /* Fix for touch devices */
  .industry-item {
    -webkit-tap-highlight-color: transparent;
  }

  /* Improve touch target size */
  .industry-title {
    padding-top: 12px;
    padding-bottom: 12px;
  }
}

@media (max-width: 576px) {
  /* Optimize for phones */
  .industry-grid,
  .industry-grid-row-2 {
    /* Ensure proper spacing */
    gap: 10px;
    padding: 0 15px;
  }

  /* Ensure images display properly */
  .industry-item {
    height: 160px;
    margin-bottom: 5px;
  }

  /* Ensure text is readable */
  .industry-title {
    font-size: 16px;
    padding: 10px;
  }
}
