<?php
/******** START BOOTSTRAP *********/
//--- 1.  bootstrap
define('DS', DIRECTORY_SEPARATOR);
include_once  substr($_SERVER['SCRIPT_FILENAME'], 0, -strlen($_SERVER['SCRIPT_NAME'])) . DIRECTORY_SEPARATOR . 'define.php';
include_once LIB_PATH . 'bootstrap.php';

//--- 2. start session
//startBeSession();

//--- 3. extract parsed variables
extract($_GET);	extract($_POST);

/*
//--- 4. instantiate DB
$myDb		= new sysDB;	$myDb->init();
//====>>>>> DB Connection Established after this line ====>>>>>

//--- 5. SM init
include_once LIB1_PATH . 'sm_init.php';
*/

//==== include language type
$langUrl = SF_URL;
if(isset($lang) && validCcode(strtoupper($lang))){$langUrl .= $lang . '/';}
define('LANG_URL',		$langUrl);



							
//--- 6. special library
include_once LIB_PATH . 'sf_category.php';

$picCnt = 0;

?>
<!DOCTYPE html>
<!--[if IE 8]>         <html lang="en" class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html lang="en" class="no-js"> <!--<![endif]-->
<head>
   <meta charset="utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta name="description" content="<?php echo $CO_NAME;?>is the leading IT helpdesk support and remote desktop assistance provider,offering comprehensive network, software or hardware.">
   <meta name="keywords" content="helpdesk support, IT support, technical support, remote IT support, remote assistance, IT help desk,IT support">
   <meta name="techspace" content="techspace.co.th">

   <title>Technical Helpdesk Support</title>


<?php echo getPgCss(); ?>
</head>
<body>

   <!-- main-container -->
   <div id="main-container">

      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'head.php');?>
      <!--Header_section--> 



      <!-- slideshow -->
         <?php include(INCLUDE_PATH . 'slider-main.php');?>
      <!-- slideshow end here -->

 
       <!-- container -->
      <section class="container">
         <div class="row">
            <h2>Help Desk Support</h2>
            <div class="large-8 medium-12 column me-animate" data-animate="fadeIn" >
               <p>
                  <?php echo $CO_NAME;?> Desktop Support Services</strong> reduce IT costs and streamline the management of your organization's IT resources so that you can focus on your business objectives.</p>
               </p>

                <p>Our flexible approach to help desk support allows us   to customize our service to meet your specific needs and to simplify  IT support, improve   security and  productivity.</p>


<h3>Reliable and Cost-Effective Help Desk Support</h3>
<p>
<?php echo $CO_NAME;?> Desktop Support Services provide a single point of contact for managing all your end-user requests. From service, hardware, network issues to warranty repairs, our help desk support team provides you with a reliable and cost-effective IT support solution.</p>


               <p>
                  <?php echo $CO_NAME;?> Desktop Support Services can help you:
               </p>
               <ul class="me-list list-circle-check">
                 <li>Provide end-user with  fast, reliable support</li>
                 <li>Improve user satfisfaction and work efficiency</li>
                 <li>Establish predictable monthly costs and eliminate the need for costly internal support</li>
                 <li>Manage server, desktops and laptops</li>
                 <li>Improve network performance and data security</li>
                 <li>Streamline and improve IT asset utilization</li>
               </ul>

<p>With 
  <?php echo $CO_NAME;?>, you can rely on our people, processes and technology to provide your team with quality support services. <br />
</p>
<p>Contact a <a href="<?php echo LANG_URL?>coy/contact-us.htm"><?php echo $CO_NAME;?> Specialist</a> for more information about how we can help you with your techical  support needs.</p>

            </div>

            <!--Header_section-->
               <?php include('menu-services.php');?>
            <!--Header_section--> 

         </div>   
      </section>
      <!-- container end here -->



      <!--Header_section-->
         <?php include(INCLUDE_PATH . 'foot.php');?>
      <!--Header_section--> 

   </div>
   <!-- main-container end here -->


<?php echo getPgFootJs(); ?>
</body>
</html>