<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />


    <title>BeatPicker Live Demo</title>


    <link rel="stylesheet" type="text/css" href="css/jquery-ui.css" />
    <script type="text/javascript" src="js/jquery-1.10.2.js"></script>
    <script type="text/javascript" src="js/jquery-ui.js"></script>

    <title>Untitled Document</title>
    <script language="javascript">
    function chak() {
        var a = document.getElementById("companyname").value;
        var b = document.getElementById("mid").value;
        var c = document.getElementById("yname").value;
        if (a == "") {
            alert("Please insert your Company name");
            return false;
        } else if (b == "") {
            alert("Please insert your Machine ID ");
            return false;
        } else if (c == "") {
            alert("Please insert your Name");
            return false;
        }
    }

    //Calendar
    $(function() {
        $("#datepicker").datepicker();
    });
    </script>
    <style type="text/css">
    .a {
        color: #CCC;
        font-family: "Courier New", Courier, monospace;
    }
    </style>
</head>

<body>

    <body bgcolor="CCFFFF">
        <form id="form1" name="form1" method="post" action="checkredata.php" onSubmit="return chak()">
            <table width="662" border="0" align="center" cellpadding="0" cellspacing="0">
                <tr>
                    <td height="66" colspan="2">
                        <table width="669" border="0" cellspacing="0" cellpadding="0">
                            <tr>
                                <td width="321" height="66"><img src="image/techspace.JPG" width="312" height="66" />
                                </td>
                                <td width="348">
                                    <h5>1112/110-111&nbsp;Sukhumvit Rd. Phra Khanong, <br />
                                        Khlong Toei,Bangkok&nbsp; 10110 Tel: +66 2381 9075<br />
                                    </h5>
                                    <!-- Fax:  +66  2713 6800 Website:&nbsp;&nbsp;www.techspace.co.th -->
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <h1>Re-installation Form</h1>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <table width="669" border="0">
                            <tr>
                                <td width="304" valign="top">
                                    <font color="red">***Please your make backup data</font>
                                </td>


                                <td width="355">Please complete this form upon receiving the pc/notebook,<br />
                                    We will require 6 hours to complete the re-installation.</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <h3>Company Installation</h3>
                    </td>
                </tr>
                <tr>
                    <td width="167">
                        <div class="fieldTitle">*Company Name :</div>
                        <div class="fieldInput"></div>
                    </td>
                    <td width="502"><input type="text" name="companyname" id="companyname" /></td>
                </tr>
                <tr>
                    <td>*Machine ID :</td>
                    <td><input type="text" name="mid" id="mid" /></td>
                </tr>
                <tr>
                    <td>*Request By :</td>
                    <td><input type="text" name="byname" id="byname" /></td>
                </tr>
                <tr>
                    <td colspan="2">&nbsp;</td>
                </tr>
                <tr>
                    <td height="2" colspan="2"><img src="file:///D|/support - Copy/image/h1.jpg" width="669"
                            height="3" /></td>
                </tr>
                <tr>
                    <td colspan="2">
                        <h1>Re-installation Details</h1>
                    </td>
                </tr>
                <tr>
                    <td>Current Operation System :</td>
                    <td><select name="cos" id="cos">
                            <option value="" selected="selected"> Please Select... </option>

                            <option value="Windows XP Professional 32 Bit">Windows XP Professional 32 Bit</option>
                            <option value="Windows XP Professional 64 Bit">Windows XP Professional 64 Bit</option>
                            <option value="Windows 7 Starter 32 Bit">Windows 7 Starter 32 Bit</option>
                            <option value="Windows 7 Home Basic 32 Bit">Windows 7 Home Basic 32 Bit</option>
                            <option value="Windows 7 Home Premium 32 Bit">Windows 7 Home Premium 32 Bit</option>
                            <option value="Windows 7 Professional 32 Bit">Windows 7 Professional 32 Bit</option>
                            <option value="Windows 7 Ultimate 32 Bit">Windows 7 Ultimate 32 Bit</option>
                            <option value="Windows 7 Enterprise 32 Bit">Windows 7 Enterprise 32 Bit</option>
                            <option value="Windows 7 Home Basic 64 Bit">Windows 7 Home Basic 64 Bit</option>
                            <option value="Windows 7 Home Premium  64 Bit">Windows 7 Home Premium 64 Bit</option>
                            <option value="Windows 7 Professional  64 Bit">Windows 7 Professional 64 Bit</option>
                            <option value="Windows 7 Ultimate 64 Bit">Windows 7 Ultimate 64 Bit</option>
                            <option value="Windows 7 Enterprise 64 Bit">Windows 7 Enterprise 64 Bit</option>
                            <option value="Windows 8 32 bit">Windows 8 32 bit</option>
                            <option value="Windows 8 64 bit">Windows 8 64 bit</option>
                            <option value="Windows 8 Pro 32 bit">Windows 8 Pro 32 bit</option>
                            <option value="Windows 8 Pro 64 bit">Windows 8 Pro 64 bit</option>
                            <option value="Windows 8 Enterprise 32 bit">Windows 8 Enterprise 32 bit</option>
                            <option value="Windows 8 Enterprise 64 bit">Windows 8 Enterprise 64 bit</option>
                            <option value="Windows 8.1 32 bit">Windows 8.1 32 bit</option>
                            <option value="Windows 8.1 64 bit">Windows 8.1 64 bit</option>
                        </select></td>
                </tr>
                <tr>
                    <td>New Operation System :</td>
                    <td><select name="nos" id="nos">

                            <option value="" selected="selected"> Please Select... </option>
                            <option value="Windows XP Professional 32 Bit">Windows XP Professional 32 Bit</option>
                            <option value="Windows XP Professional 64 Bit">Windows XP Professional 64 Bit</option>
                            <option value="Windows 7 Starter 32 Bit">Windows 7 Starter 32 Bit</option>
                            <option value="Windows 7 Home Basic 32 Bit">Windows 7 Home Basic 32 Bit</option>
                            <option value="Windows 7 Home Premium 32 Bit">Windows 7 Home Premium 32 Bit</option>
                            <option value="Windows 7 Professional 32 Bit">Windows 7 Professional 32 Bit</option>
                            <option value="Windows 7 Ultimate 32 Bit">Windows 7 Ultimate 32 Bit</option>
                            <option value="Windows 7 Enterprise 32 Bit">Windows 7 Enterprise 32 Bit</option>
                            <option value="Windows 7 Home Basic 64 Bit">Windows 7 Home Basic 64 Bit</option>
                            <option value="Windows 7 Home Premium  64 Bit">Windows 7 Home Premium 64 Bit</option>
                            <option value="Windows 7 Professional  64 Bit">Windows 7 Professional 64 Bit</option>
                            <option value="Windows 7 Ultimate 64 Bit">Windows 7 Ultimate 64 Bit</option>
                            <option value="Windows 7 Enterprise 64 Bit">Windows 7 Enterprise 64 Bit</option>
                            <option value="Windows 8 32 bit">Windows 8 32 bit</option>
                            <option value="Windows 8 64 bit">Windows 8 64 bit</option>
                            <option value="Windows 8 Pro 32 bit">Windows 8 Pro 32 bit</option>
                            <option value="Windows 8 Pro 64 bit">Windows 8 Pro 64 bit</option>
                            <option value="Windows 8 Enterprise 32 bit">Windows 8 Enterprise 32 bit</option>
                            <option value="Windows 8 Enterprise 64 bit">Windows 8 Enterprise 64 bit</option>
                            <option value="Windows 8.1 32 bit">Windows 8.1 32 bit</option>
                            <option value="Windows 8.1 64 bit">Windows 8.1 64 bit</option>
                        </select></td>
                </tr>
                <tr>
                    <td>Language :</td>
                    <td><select name="language" id="language">
                            <option value="" selected="selected"> Please Select... </option>
                            <option value="Thailand">Thailand</option>
                            <option value="English">English</option>
                        </select></td>
                </tr>
                <tr>
                    <td height="31">Microsoft Office :</td>
                    <td><select name="micro_off" id="micro_off">
                            <option selected="selected">Please Select... </option>
                            <option value="Basic 2007">Basic 2007</option>
                            <option value="Enterprise 2007">Enterprise 2007</option>
                            <option value="Home and Student 2007">Home and Student 2007</option>
                            <option value="Professional 2007">Professional 2007</option>
                            <option value="Professional Plus 2007">Professional Plus 2007</option>
                            <option value="Small Business 2007">Small Business 2007</option>
                            <option value="Standard 2007">Standard 2007</option>
                            <option value="Ultimate 2007">Ultimate 2007</option>
                            <option value="Home and Business 2010">Home and Business 2010</option>
                            <option value="Home and Student 2010">Home and Student 2010</option>
                            <option value="Standard 2010">Standard 2010 </option>
                            <option value="Professional 2010">Professional 2010</option>
                            <option value="Professional Plus 2010">Professional Plus 2010</option>
                            <option value="Starter 2010">Starter 2010</option>
                            <option value="Home and Business 2013">Home and Business 2013</option>
                            <option value="Home and Student 2013">Home and Student 2013</option>
                            <option value="Professional 2013">Professional 2013</option>
                            <option value="Professional Plus 2013">Professional Plus 2013</option>
                            <option value="Standard 2013">Standard 2013</option>
                            <option value="365 Home Premium">365 Home Premium</option>
                            <option value="365 University">365 University</option>
                        </select>

                    </td>
                </tr>
                <tr>
                    <td height="33">Web Browers :</td>
                    <td><input name="Firefox" type="checkbox" id="Firefox" value="Firefox" checked="checked" />
                        Mozilla Firefox (recommend)
                        <input name="Chome" type="checkbox" id="Chome" value="Chome" checked="checked" />
                        Google Chome
                        <input name="ie" type="checkbox" id="ie" value="IE" checked="checked" />
                        <label for="ie">Internet Exploer </label>
                    </td>
                </tr>
                <tr>
                    <td height="32">Communication :</td>
                    <td><input name="Teamviewer" type="checkbox" id="Teamviewer" value="Teamviewer" checked="checked" />
                        Teamviewer
                        <input name="Skype" type="checkbox" id="Skype" value="Skype" checked="checked" />
                        Skype
                        <input name="Msn" type="checkbox" id="Msn" value="Msn" checked="checked" />
                        Msn
                        <input name="Line" type="checkbox" id="Line" value="Line" checked="checked" />
                        Line
                    </td>
                </tr>
                <tr>
                    <td>Other :</td>
                    <td><input name="winrar" type="checkbox" id="winrar" value="Winrar" checked="checked" />
                        <label for="winrar">Winrar
                            <input name="AdobeReader" type="checkbox" id="AdobeReader" value="PDF" checked="checked" />
                            Adobe Reader PDF
                            <input name="adf" type="checkbox" id="adf" checked="checked" />
                            Adobe flash player</label>
                    </td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td><input name="7zip" type="checkbox" id="7zip" checked="checked" />
                        <label for="7zip">7zip
                            <input name="nfw" type="checkbox" id="nfw" checked="checked" />
                            Net.framework</label>
                    </td>
                </tr>
                <tr>
                    <td align="left">&nbsp;</td>
                </tr>
                <tr>
                    <td height="35" valign="top">Additional Program :</td>
                    <td align="left">Please enter any additional program.
                        <p>
                            <textarea name="additionalprogram" id="additionalprogram" cols="45" rows="5"></textarea>
                        </p>
                    </td>
                </tr>
                <tr>
                    <td height="35" valign="top">Additional Note :</td>
                    <td align="left">Please enter any additional re-installation information or preferences.

                        <p>
                            <textarea name="additionalnote" id="additionalnote" cols="45" rows="5"></textarea>
                        </p>
                    </td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td>
                        <p>Thank your for your Submission.We will contact you when the re-nstallation is completed. </p>
                    </td>
                </tr>
                <tr>
                    <td>Due Date</td>
                    <td><input type="text" name="cal" id="datepicker"></td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td><input type="Submit" name="Submit" id="Submit" value="Submit" /> <input type="reset"
                            name="cancel" id="cancel" value="Cancel" /></td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <tr>
                    <td height="10" colspan="2"><img src="image/h1.jpg" width="669" height="11" /></td>
                </tr>
                <tr>
                    <td colspan="2" align="center"><span class="a">Copyright © 2014 Techspace.co.th</span></td>
                </tr>
            </table>
        </form>
    </body>

</html>