<?php
ini_set("session.auto_start","1");
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_httponly', 1);

$VERBOSE_SYS	= false;
$VERBOSE_MYPHP	= false;
$VERBOSE_MYJS	= false;


if($VERBOSE_SYS){
	//error_reporting(E_ALL);			//all errors
	error_reporting(E_ERROR | E_WARNING | E_PARSE);
	ini_set('display_errors', TRUE);		
} else {
	error_reporting(0); 				//turn off
}




//--- User Login
$MIN_PSWD_LENGTH		= 8;		//password min length
$MAX_PSWD_LENGTH		= 30;
$MAX_IP_BLOCK_TIME		= 24;		//block access from a particular IP for 24 hours if usr verification during signed in fails



//---- System Modules
$SM_MODULE = array (	
	'SM' 		=> '1',
	'BK' 		=> '40',

	'KB' 		=> '770',		//KB
	'WS' 		=> '800',		//webstore
//	'BE' 		=> '810',		//backend
//	'WM' 		=> '820',		//WHM
	'WX' 		=> '850',		//web Xtension
	'AN' 		=> '850'		//Analytics
);

$SM_MODULE_NAME = array (	
	'SM' 		=> 'System Manager',
	'WS' 		=> 'Webstore',	
//	'BE' 		=> 'Backend',
//	'WM' 		=> 'WHM',
	'WX' 		=> 'Web Extension',
	'AN' 		=> 'Analytics'
);


//---- System Sub-Modules
$SM_COMPONENT = array (		   
	'SM_SETUP'			=> '10',
	'SM_ADJ'			=> '11',
	'SM_MNGR'			=> '12',	//system Manager
	'SM_SVC'			=> '15',	//system services
	'SM_LOG' 			=> '16',	//system log
	'SM_COY' 			=> '17',	//my company
	'SM_USR' 			=> '20',	//user
//	'SM_USR_BOARD' 		=> '21',	//My Dashboard
//	'SM_USR_NOTE' 		=> '22',	//user Notepad
//	'SM_USR_NOTIFY'		=> '23',	//User Notification
	'SM_CON' 			=> '30',
	'SM_IM' 			=> '35',	//import data file

	'SM_CUS_SETUP' 		=> '40',	//Customer setup
	'SM_CUS'	 		=> '41',	//customer
	'SM_CUS_LOC' 		=> '42',	//customer Location

	'SM_VEN_SETUP' 		=> '50',	//Vendor setup
	'SM_VEN' 			=> '51',	//Vendor
	'SM_VEN_LOC' 		=> '52',	//Vendor Location

	'WX_VEN_USR' 	=> '806'


/* future
			'VISITOR'			=> 'Visitor Activities',
			'VISITOR'			=> 'Visitor Activities',
			'CHECKOUT'			=> 'Checkout Process',
			'SYSTEM'			=> 'System',
			'MEMBER'			=> 'Member',
			'SF_ANNOUNCE'		=> 'Annoucement',
			'STORE'				=> 'Storefront Updates',
			'SF_SYSTEM'			=> 'System',
			'SF_CSC'			=> 'Customer Order Log',
			'SF_CRONJOB'		=> 'Cronjobs',
			'MS_ACCESS'			=> 'Merchant Access Log',
			'MS_ANNOUNCE'		=> 'Merchant Announcement',
			'MS_SYSTEM'			=> 'Merchant System Log',
			'MS_CSC'			=> 'Merchant\'s Customer/Order Log',
			'MS_STORE'			=> 'Merchant Store Log',
			'MS_PDT'			=> 'Merchant Product Log'

*/
);

$SM_COMPONENT_NAME = array (	
	'SM_SETUP'		=> 'SM Setup',
	'SM_ADJ'		=> 'SM Adjustment',
	'SM_MNGR'		=> 'SM Manager',
	'SM_SVC'		=> 'SM Services',
	'SM_LOG' 		=> 'SM Log',
	'SM_COY' 		=> 'SM Company',
	'SM_USR' 		=> 'SM User',
//	'SM_USR_BOARD' 	=> 'User Dashboard',
//	'SM_USR_NOTE' 	=> 'User Notepad', 			//user Notepad
//	'SM_USR_NOTIFY' => 'User Notification',		//User Notification
	'SM_CON' 		=> 'SM Contacts',
	'SM_IM' 		=> 'SM Import Logs',		//import data logfile

	'SM_CUS_SETUP' 	=> 'Customer Setup',
	'SM_CUS'	 	=> 'Customer',			//SM customer
	'SM_CUS_LOC' 	=> 'Customer Location',	//SM customer Location

	'SM_VEN_SETUP' 	=> 'Vendor Setup',
	'SM_VEN' 		=> 'Vendor',				//SM Vendor
	'SM_VEN_LOC' 	=> 'Vendor Location',	//SM Vendor Location

	'BK_SETUP'			=> 'Bank Setup',
	'BK_ACC'			=> 'Bank Account',
	'BK_XFER' 			=> 'Bank Transfer',



	'WX_VEN_USR' 	=> 'Web Extension Vendor User'

);

//---- Data Source Option
$SM_DATA_SRC = array(
	'SYSTEM'	=> '1',
	'IMPORTED'	=> '2',
	'ENTERED'	=> '3',		//default data source
	'WXCUS'		=> '4',
	'WXVEN'		=> '5',
	'WXPAR'		=> '6',
	'MOBILE'	=> '7',
	'SOCIAL'	=> '8',
	'WEB'		=> '9'

);

$SM_DATA_SRC_NAME = array(
	'SYSTEM'	=> 'System',
	'IMPORTED'	=> 'Imported',
	'ENTERED'	=> 'Entered',
	'WXCUS'		=> 'Customer Portal',
	'WXVEN'		=> 'Vendor Portal',
	'WXPAR'		=> 'Partner Portal',
	'SOCIAL'	=> 'Social',
	'MOBILE'	=> 'Mobile',
	'WEB'		=> 'Web'
);







//---- document type options
$SM_DOC_TYPE = array (
	'SM_PROS'	=> 'pros',
	'SM_CUS'	=> 'cus',
	'SM_VEN'	=> 'ven'
);

$SM_DOC_TYPE_NAME = array (
	"SM_PROS"	=> "Prospect Number",
	"SM_CUS"	=> "Customer Number",
	"SM_VEN"	=> "Vendor Number"
);




$SM_TEL_TYPE = array (	
	"B" => "Business",
	"M" => "Mobile",
	"H" => "Home",
	"F" => "Fax"
);



//--- field validation
//old $RESTRICTED_DOC_CHAR = '~!@#£$%^&\*()=\+{}\[\]\|\\\s;:\'"\?<>`';
$RESTRICTED_DOC_CHAR = '~`\!@#£\$%^&\*\(\)\+=\[\]\{\}\|:;\"\'\<,\>\s\\\\';


$RESTRICTED_DOC_PHRASES = array(
	'SYS-BUCKET'	//used as docNo in ICMMRCOST (fifo) table when prorating an adjustment where no ICMMRCOST entry found in DB.
);

$ALLOW_DOC_CHAR = '#\*\[\]-\/\\\.';

$URL_SPECIAL_CHAR = array(
	'/\//', '/\^/', '/\./', '/\$/', '/\|/',
	'/\(/', '/\)/', '/\[/', '/\]/', '/\*/',
	'/\+/', '/\?/', '/\{/', '/\}/', '/\,/',
	'/\&/', '/\'/', '/:/', '/</', '/>/',
	'/#/', '/\%/', '/\@/', '/;/', '/=/',
	'/\^/', '/\|/', '/~/', '/\s+/'
);

$URL_SPECIAL_CHAR_REPLACE = array(
	'-', '-', '-', '-', '-',
	'-', '-', '-', '-', '-',
	'-', '-', '-', '-', '-',
	'-', '-', '-', '-', '-',
	'-', '-', '-', '-', '-',
	'-', '-', '-', '-' 
);


$excludes = array("a", "i",
	"an", "as", "at", "be", "by", "if", "in", "is", "it", "of", "on", "or", "to", "up",
	"and", "are", "but", "for", "nor", "out", "the",
	"else", "from", "that", "then", "this", "over", "when", "with");
$excludesCapFirst = array("A", "I",
	"An", "As", "At", "Be", "By", "If", "In", "Is", "It", "Of", "On", "Or", "To", "Up",
	"And", "Are", "But", "For", "Nor", "Out", "The",
	"Else", "From", "That", "Then", "This", "Over", "When", "With");


?>